package loyalty.share.lib.utils.date;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;

/**
 * <AUTHOR>
 */
public class DateTimeUtils {

    public static String getFormattedDate() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取当前年份
        int currentYear = currentDate.getYear();
        // 获取当前日期是当前年的第几周
        int currentWeek = currentDate.get(WeekFields.ISO.weekOfWeekBasedYear());
        // 获取当前日期是本月的第几天
        int dayOfWeekValue = currentDate.getDayOfWeek().getValue();
        // 拼接成字符串
        return String.format("%d%02d%d", currentYear, currentWeek, dayOfWeekValue);
    }

    /**
     * 获取当前的年月日时分秒
     *
     * @return string
     */
    public static String getCurrentTimeString() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 格式化当前时间
        return now.format(formatter);
    }

    /**
     * 获取当前的年月日时分秒
     *
     * @return string
     */
    public static String getCurrentTimeMillisecondString() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 格式化时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        // 格式化当前时间
        return now.format(formatter);
    }

    public static void main(String[] args) {
        String currentTimeMillisecondString = getCurrentTimeMillisecondString();
        System.out.println(currentTimeMillisecondString);
    }

}
