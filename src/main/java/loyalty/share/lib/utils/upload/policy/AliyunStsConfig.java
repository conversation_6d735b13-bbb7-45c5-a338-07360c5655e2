package loyalty.share.lib.utils.upload.policy;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@ConfigurationProperties(prefix = "aliyun")
@Data
public class AliyunStsConfig {

    private String accessKeyId;

    private String accessKeySecret;

    private String ossBucket;

    private String endpoint;

    private String accessAddress;

    private Integer tempUrlExpiration;

    private Map<String, String> path;
}