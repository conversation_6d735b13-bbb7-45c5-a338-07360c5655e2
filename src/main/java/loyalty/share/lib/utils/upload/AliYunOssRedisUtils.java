package loyalty.share.lib.utils.upload;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;
import loyalty.share.lib.common.model.UploadTargetEnum;
import loyalty.share.lib.utils.upload.policy.AliyunStsConfig;
import loyalty.share.lib.utils.upload.policy.UploadFiles;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static loyalty.share.lib.utils.date.DateTimeUtils.getCurrentTimeMillisecondString;

/**
 * <AUTHOR>
 */
@Slf4j
public class AliYunOssRedisUtils implements UploadFiles {

    private final AliyunStsConfig aliyunStsConfig;

    public AliYunOssRedisUtils(AliyunStsConfig aliyunStsConfig) {
        this.aliyunStsConfig = aliyunStsConfig;
    }


    @Override
    public List<String> uploadFiles(List<MultipartFile> files, String modelName) {
        log.info("AliYunOssRedisUtils uploadFiles");
        // 创建 OSSClient 实例
        OSS ossClient = new OSSClientBuilder().build(aliyunStsConfig.getEndpoint(),
                aliyunStsConfig.getAccessKeyId(),
                aliyunStsConfig.getAccessKeySecret());
        ArrayList<String> list = new ArrayList<>();
        try {
            for (MultipartFile file : files) {
                // 获取文件名
                String fileName = file.getOriginalFilename();
                log.info("AliYunOssRedisUtils fileName:{}", fileName);
                String fileExt = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

                String newFileName = this.removeFileNameSuffix(fileName) + "_" + getCurrentTimeMillisecondString() + "." + fileExt;

                log.info("AliYunOssRedisUtils newFileName:{}", newFileName);
                // 创建 ObjectMetadata 对象，并设置 Content-Length 和 Content-Type
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(file.getSize());
                metadata.setContentType(file.getContentType());
                // 获取多个上传地址
                Map<String, String> paths = aliyunStsConfig.getPath();
                String path = paths.get(modelName);
                if (path == null || path.isEmpty()) {
                    return new ArrayList<>();
                }
                // 上传文件到阿里云 OSS
                PutObjectRequest request = new PutObjectRequest(aliyunStsConfig.getOssBucket(), path + newFileName, file.getInputStream(), metadata);
                ossClient.putObject(request);
                //https://cs-present.oss-cn-zhangjiakou.aliyuncs.com/amway/videocontent/img/girl-7913052_1280.jpg
                String url = aliyunStsConfig.getAccessAddress() + path + newFileName;
                list.add(url);
            }
            log.info("finish to /base-upload/upload start time :{},", System.currentTimeMillis());
            return list;
        } catch (IOException e) {
            log.error("文件上传失败！");
        } finally {
            // 关闭 OSSClient
            ossClient.shutdown();
        }
        return null;
    }

    @Override
    public String uploadTarget() {
        return UploadTargetEnum.OSS_UPLOAD.getCode();
    }

    /**
     * 去除文件名称后缀
     *
     * @param fileName 文件名称
     * @return 图片名称
     */
    public String removeFileNameSuffix(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }

}
