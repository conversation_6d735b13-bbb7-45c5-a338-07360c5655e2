package loyalty.share.lib.utils.upload.policy;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UploadFiles {
    /**
     * 图片上传
     *
     * @param files     上传的图片
     * @param modelName 上传文件地址key
     * @return List<UploadResult>
     */
    public List<String> uploadFiles(List<MultipartFile> files, String modelName);

    /**
     * 获取上传目标
     *
     * @return
     */
    String uploadTarget();
}
