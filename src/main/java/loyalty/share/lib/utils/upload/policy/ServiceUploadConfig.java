package loyalty.share.lib.utils.upload.policy;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 读取配置文件
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "service")
@Data
public class ServiceUploadConfig {
    private String uploadIp;
    private Map<String, String> uploadAddress;
    private Map<String, String> excelUploadAddress;
}