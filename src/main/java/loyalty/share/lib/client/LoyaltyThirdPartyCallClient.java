package loyalty.share.lib.client;


import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import loyalty.share.lib.db.mapper.LoyaltyV2Log3rdpartycallMapper;
import loyalty.share.lib.db.model.LoyaltyV2Log3rdpartycall;

import java.util.Map;

@Slf4j
public abstract class LoyaltyThirdPartyCallClient<O, I extends ThirdPartyCallInput> {
    private final LoyaltyV2Log3rdpartycallMapper log3rdpartycallMapper;

    public LoyaltyThirdPartyCallClient(LoyaltyV2Log3rdpartycallMapper log3rdpartycallMapper) {
        this.log3rdpartycallMapper = log3rdpartycallMapper;
    }


    public O execute(I thirdPartyCallInput) {
        O responseData = sendMessages(thirdPartyCallInput);
        safeToDB(thirdPartyCallInput, responseData);
        return responseData;
    }

    private void safeToDB(final ThirdPartyCallInput inputData, final Object outputData) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    LoyaltyV2Log3rdpartycall loyaltyV2Log3rdpartycall = new LoyaltyV2Log3rdpartycall();
                    loyaltyV2Log3rdpartycall.setRequestSenario(inputData.getSenario());
                    loyaltyV2Log3rdpartycall.setCreator(inputData.getSender());
                    loyaltyV2Log3rdpartycall.setUpdator(inputData.getSender());
                    if (inputData.getRequestBody() != null)
                        loyaltyV2Log3rdpartycall.setRequestBody(new ObjectMapper().writeValueAsString(inputData.getRequestBody()));
                    if (inputData.getRequestParams() != null && inputData.getRequestParams().size() > 0) {
                        String param = "?";
                        for (Map.Entry entry : inputData.getRequestParams().entrySet()) {
                            if (entry.getValue() != null) {
                                param += entry.getKey() + "=" + entry.getValue().toString();
                                param += "&";
                            }
                        }
                        if (param.endsWith("&")) {
                            param = param.substring(0, param.length() - 1);
                        }
                        loyaltyV2Log3rdpartycall.setRequestUrl(inputData.getRequestUrl() + param);
                    } else {
                        loyaltyV2Log3rdpartycall.setRequestUrl(inputData.getRequestUrl());
                    }
                    if (outputData != null)
                        loyaltyV2Log3rdpartycall.setResponseBody(new ObjectMapper().writeValueAsString(outputData));
                    log3rdpartycallMapper.insert(loyaltyV2Log3rdpartycall);
                } catch (Exception e) {
                    log.error("Error while write to db with {}", e.getMessage());
                }
            }
        }).start();
    }

    protected abstract O sendMessages(I thirdPartyCallInput);


}
