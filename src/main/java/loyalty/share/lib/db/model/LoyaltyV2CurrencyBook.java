package loyalty.share.lib.db.model;


import lombok.Data;

import java.util.Date;


@Data
public class LoyaltyV2CurrencyBook {
	private Long id;
	private Long giftId;
	private Long bookOwnerId;
	private String remark;
	private String bookName;
	private Boolean isEnabled;
	private String createdByUser;
	private String updatedByUser;
	private Date createTime;
	private Date updateTime;
	private String giftName;
	private String ownerName;
	private Boolean isInternalBook;

}
