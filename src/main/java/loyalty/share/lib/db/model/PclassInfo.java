package loyalty.share.lib.db.model;


import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import lombok.ToString;


import java.util.Date;
import lombok.Data;
import loyalty.share.lib.common.model.MainBaseAdminEntity;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PclassInfo extends MainBaseAdminEntity implements Serializable{
	private String id;
	private String classesCode;
	private String topic;
	private Date startTime;
	private Date endTime;
	private String ownerName;
	private String ownerMobile;
	private String ownerEmployeeNum;
	private String director;
	private String directorMobile;
	private String directorExmployeeNum;
	private Boolean isEnabled;
	private String remark;
	private String province;
	private String city;
	private String place;
	private String address;
	private String longitude;
	private String latitude;
	private String expertName;
	private String expertIntroduce;
	private String hotline;
	private String qrcodeUrl;
	private Integer limitCount;
	private String surveyTemplate;
	private String pclassType;
	private String pclassProperty;
	private String pclassType2;
	private String courseName;
	private String otherCourseName;
	private Integer invitedFamilyCount;
	private String ncCode;
	private Date lastModifyTime;
	private Date uploadTime;
	private String uploadStatus;
	private String status;
	private String onlineChannel;
	private String hospitalCode;
	private String hospitalName;
	private Boolean isGodeepCity;
	private Boolean isSendCoupon;
	private String businessReason;
	private Boolean isOnlineActivity;
	private String activityType;
	private String actionType;
	private Boolean isDeleted;

}
