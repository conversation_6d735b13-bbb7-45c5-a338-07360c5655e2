package loyalty.share.lib.db.model;


import java.util.Date;
import lombok.Data;


@Data
public class LoyaltyV2MemberCurrencyReduce {
	private Long id;
	private Long currencyId;
	private String cellphone;
	private Integer memberGrade;
	private Float currencyAmount;
	private Long eventId;
	private String historyTableName;
	private Long changedByHistoryId;
	private Long changedByCmpId;
	private Long loyaltyChannelId;
	private String reasonCode;
	private String reduceCode;
	private String reduceType;
	private Long migrateId;
	private String migrateKey;
	private Date triggerTime;
	private Date reduceTriggerTime;
	private String creator;
	private String updator;
	private Date createTime;
	private Date updateTime;
	private Long userTagId;
	private String userIp;
	private String userAgent;
}
