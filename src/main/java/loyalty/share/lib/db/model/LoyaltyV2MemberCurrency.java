package loyalty.share.lib.db.model;


import java.util.Date;
import lombok.Data;


@Data
public class LoyaltyV2MemberCurrency {
	private Long id;
	private Long giftId;
	private String cellphone;
	private Float currencyAmount;
	private String creator;
	private String updator;
	private Date createTime;
	private Date updateTime;
	private Long lastChangedByAddId;
	private Long lastChangedByReduceId;

	public LoyaltyV2MemberCurrency(){

	}

	public LoyaltyV2MemberCurrency(Long giftId, String cellphone, Float currencyAmount, String creator, String updator, Long lastChangedByAddId, Long lastChangedByReduceId) {
		this.giftId = giftId;
		this.cellphone = cellphone;
		this.currencyAmount = currencyAmount;
		this.creator = creator;
		this.updator = updator;
		this.lastChangedByAddId = lastChangedByAddId;
		this.lastChangedByReduceId = lastChangedByReduceId;
	}

	public LoyaltyV2MemberCurrency(Long giftId, String cellphone, Float currencyAmount,String updator, Long lastChangedByAddId, Long lastChangedByReduceId) {
		this.giftId = giftId;
		this.cellphone = cellphone;
		this.currencyAmount = currencyAmount;
		this.updator = updator;
		this.lastChangedByAddId = lastChangedByAddId;
		this.lastChangedByReduceId = lastChangedByReduceId;
	}
}
