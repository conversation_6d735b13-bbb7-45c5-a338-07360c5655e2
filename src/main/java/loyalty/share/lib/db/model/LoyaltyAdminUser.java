package loyalty.share.lib.db.model;


import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
public class LoyaltyAdminUser {
	private Long id;
	private String userName;
	private String cellphone;
	private String mailBox;
	private String remark;
	private String password;
	private Boolean isEnabled;
	private String creator;
	private String updator;
	private Date createTime;
	private Date updateTime;
    /*private Long id;
    private String userName;
    private String password;
    private String mobile;
    private Integer status;
    private String remake;
    private String email;
    private String nickName;
    private Boolean isSuper;
    private Boolean isAdmin;
    private String creator;
    private String updator;
    private Date createTime;
    private Date updateTime;*/

}
