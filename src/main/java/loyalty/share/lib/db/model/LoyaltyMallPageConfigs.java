package loyalty.share.lib.db.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class LoyaltyMallPageConfigs implements Serializable {
    private Long id;
    private String pageCode;
    private String pageName;
    private String pagePath;
    private String pageTitle;
    private String layoutType;
    private String backgroudImage;
    private String backgroundColor;
    private String cssStyle;
    private String content;
    private String sourceCode;
    private Integer sort;
    private String extra;
    private Integer themeId;
    private String remark;
    private String status;
    private Date publishTime;
    private Boolean isEnabled;
    private Boolean isEditable;

    private String creator;
    private String updator;
    private Date createTime;
    private Date updateTime;
}
