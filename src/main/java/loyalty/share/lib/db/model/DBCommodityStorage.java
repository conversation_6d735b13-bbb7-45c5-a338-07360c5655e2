package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DBCommodityStorage {

    private Long id;
    private Long commodityId;
    private Long sizeCombId;
    private Long accumultateAmount;
    private Long currentAvailableAmount;
    private Long safeStorage;
    private Long lastUpdateByOrderId;
    private String lastUpdateEvent;
    private Long createdByUser;
    private Long updatedByUser;
    private Date createTime;
    private Date updateTime;

    //附加字段:兑换数量
    private Long redemptionAmount;


}
