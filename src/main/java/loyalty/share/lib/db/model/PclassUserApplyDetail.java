package loyalty.share.lib.db.model;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.util.Date;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PclassUserApplyDetail extends BaseEntity implements Serializable{
	private Long id;
	private String cellphone;
	private String pnecId;
	private String pclassCode;
	private String userName;
	private Date babyBirthday;
	private Integer attendance;
	private Date triggerTime;
	private String status;

}
