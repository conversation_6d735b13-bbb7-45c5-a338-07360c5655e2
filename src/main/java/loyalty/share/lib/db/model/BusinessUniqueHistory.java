package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
public class BusinessUniqueHistory {
    private Long id;
    private String bizUniqueKey;
    private String businessType;
    private String businessTable;
    private String remark;
    private Date createTime;
    private Date updateTime;
}
