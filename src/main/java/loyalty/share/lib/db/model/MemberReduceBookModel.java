package loyalty.share.lib.db.model;

import lombok.Data;

import java.util.Date;


@Data
public class MemberReduceBookModel extends ModelBase {
    private Long id;
    private Long bookId;
    private String cellphone;
    private Float currencyAmount;
    private Long eventId;
    private Long changedByReduceId;
    private Date triggerTime;
    private Date closeTime;
    private String closeOperationId;

    private String tempOperationId;

    public MemberReduceBookModel() {
    }

    public MemberReduceBookModel(Long bookId, Long eventId, String cellphone, String closeOperationId, Float currencyAmount, Long changedByReduceId, Date triggerTime, Date closeTime) {
        this.bookId = bookId;
        this.cellphone = cellphone;
        this.currencyAmount = currencyAmount;
        this.eventId = eventId;
        this.changedByReduceId = changedByReduceId;
        this.triggerTime = triggerTime;
        this.closeOperationId = closeOperationId;
        this.closeTime=closeTime;
    }

    public MemberReduceBookModel(Long bookId, String cellphone, Float currencyAmount, Long eventId, Date triggerTime, String closeOperationId, String tempOperationId) {
        this.bookId = bookId;
        this.cellphone = cellphone;
        this.currencyAmount = currencyAmount;
        this.eventId = eventId;
        this.triggerTime = triggerTime;
        this.closeOperationId = closeOperationId;
        this.tempOperationId = tempOperationId;
    }
}
