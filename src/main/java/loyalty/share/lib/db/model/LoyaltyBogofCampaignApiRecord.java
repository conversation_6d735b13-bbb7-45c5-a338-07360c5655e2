package loyalty.share.lib.db.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyBogofCampaignApiRecord extends BaseEntity implements Serializable{
	private Long id;
	private String apiPlatform;
	private String cellphone;
	private Long loyaltyBogofCampaignRecordId;
	private String url;
	private String reqParam;
	private String reqBody;
	private String respBody;
	private String respCode;
	private Boolean isSuc;
	private Date triggerTime;
}
