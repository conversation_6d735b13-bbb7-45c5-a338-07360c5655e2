package loyalty.share.lib.db.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyBogofCampaignRecord extends BaseEntity implements Serializable{
	private Long id;
	private String cellphone;
	private String openid;
	private String unionid;
	private Long ccCampaignId;
	private String ccCampaignInstId;
	private String ccCampaignType;
	private String ccCampaignName;
	private String ccCampaignCode;
	private String orderNo;
	private Integer gravida;
	private BigDecimal longitude;
	private BigDecimal latitude;
	private Date triggerTime;
	private Date ccParticipatedTime;

}
