package loyalty.share.lib.db.model;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.MainBaseAdminEntity;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PclassPnecRel extends MainBaseAdminEntity implements Serializable{
	private Long id;
	private String pclassCode;
	private String pnecId;

}
