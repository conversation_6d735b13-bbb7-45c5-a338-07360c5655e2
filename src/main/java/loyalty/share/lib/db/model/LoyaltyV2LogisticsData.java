package loyalty.share.lib.db.model;


import lombok.Data;

import java.util.Date;


@Data
public class LoyaltyV2LogisticsData {
    private Long id;
    private Long campaignHistoryBxgxid;
    private Integer mallOrderId;
    private Long historyInstid;
    private String historyTableName;
    private String country;
    private String province;
    private String city;
    private String area;
    private String road;
    private String addDetail;
    private String recvCellphone;
    private String recvName;
    private Long channelId;
    private String fromCellphone;
    private String creator;
    private String updator;
    private Date createTime;
    private Date updateTime;
    private String userIp;
    private String userAgent;
    private String roomNumber;
    private String logisticsWarningType;
}
