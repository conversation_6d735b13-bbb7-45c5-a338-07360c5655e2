package loyalty.share.lib.db.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
@Data
public class QuestionInfo {

    private Integer id;
    @NotNull(message = "品牌id不能空")
    private Integer campaignId ;
    @NotBlank(message = "问题不能空")
    private String question ;
    @NotNull(message = "问题类型不能空")
    private Integer type ;
    @NotNull(message = "是否启用不能空")
    private Integer IsEnabled ;
    @NotBlank (message = "品牌分类不能空")
    private String series  ;
    @NotBlank (message = "问题分析不能空")
    private String questionAnalysis ;
    private Date createTime;
    private Date updateTime;

    @NotEmpty(message = "选项不能空")
    @Valid
    private List<Answer> answerList;//选项

    private String correctAnswer;//正确答案


}
