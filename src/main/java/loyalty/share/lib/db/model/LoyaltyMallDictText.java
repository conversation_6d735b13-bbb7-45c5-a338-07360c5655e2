package loyalty.share.lib.db.model;


import lombok.Data;

import java.util.Date;


@Data
public class LoyaltyMallDictText {
    private Long id;
    private Long channelId;
    private String textArea;
    private String intCode;
    private String extCode;
    private String extDesc;
    private String intDesc;
    private Integer priority;
    private Boolean isEnabled;
    private Date createTime;
    private Date updateTime;

    public LoyaltyMallDictText() {
    }

    public LoyaltyMallDictText(String textArea, String intCode, String extCode, String extDesc, String intDesc, Integer priority, Boolean isEnabled) {
        this.textArea = textArea;
        this.intCode = intCode;
        this.extCode = extCode;
        this.extDesc = extDesc;
        this.intDesc = intDesc;
        this.priority = priority;
        this.isEnabled = isEnabled;
    }

    public LoyaltyMallDictText(Long id, Long channelId, String textArea, String intCode, String extCode, String extDesc, String intDesc, Integer priority, Boolean isEnabled, Date createTime, Date updateTime) {
        this.id = id;
        this.channelId = channelId;
        this.textArea = textArea;
        this.intCode = intCode;
        this.extCode = extCode;
        this.extDesc = extDesc;
        this.intDesc = intDesc;
        this.priority = priority;
        this.isEnabled = isEnabled;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
}
