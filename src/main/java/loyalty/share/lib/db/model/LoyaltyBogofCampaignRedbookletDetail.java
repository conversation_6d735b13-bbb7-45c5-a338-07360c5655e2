package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyBogofCampaignRedbookletDetail extends BaseEntity implements Serializable {

    private Integer id;
    private Long loyaltyBogofCampaignRecordId;
    private String ccCampaignInstId;
    private String redBookletImgUrl;
    private String redBookletLink;
    private String auditStatus;
    private Date triggerTime;
}
