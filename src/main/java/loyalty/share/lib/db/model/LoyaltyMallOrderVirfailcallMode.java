package loyalty.share.lib.db.model;

import lombok.Data;

import java.util.Date;

@Data
public class LoyaltyMallOrderVirfailcallMode extends ModelBase{
    private Long id;
    private Long orderDetailId;
//    private Integer serialNumber;
    private String userIp;
    private String failedIndex;
    private Boolean hasSuc;
    private String failReason;
    private String failReasonCode;
    private Date retryTimes;
    private String cellphone;

    public LoyaltyMallOrderVirfailcallMode() {
    }

    public LoyaltyMallOrderVirfailcallMode(Long orderDetailId,String cellphone,String userIp, String failedIndex, Boolean hasSuc, String failReason, String failReasonCode,String creator,String updator) {
        this.orderDetailId = orderDetailId;
        this.cellphone=cellphone;
//        this.serialNumber=serialNumber;
        this.userIp = userIp;
        this.failedIndex = failedIndex;
        this.hasSuc = hasSuc;
        this.failReason = failReason;
        this.failReasonCode = failReasonCode;
        super.creator=creator;
        super.updator=updator;
    }
}
