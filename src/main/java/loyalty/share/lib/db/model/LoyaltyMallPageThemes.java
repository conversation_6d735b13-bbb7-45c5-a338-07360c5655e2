package loyalty.share.lib.db.model;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.MainBaseAdminEntity;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyMallPageThemes extends MainBaseAdminEntity implements Serializable{
	private Long id;
	private String themeName;
	private String primaryColor;
	private String secondaryColor;
	private String backgroudImage;
	private String backgroundColor;
	private String fontColor;
	private String extra;
	private String remark;
	private String updator;

}
