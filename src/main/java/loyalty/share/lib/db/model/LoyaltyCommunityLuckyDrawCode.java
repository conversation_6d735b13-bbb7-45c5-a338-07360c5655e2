package loyalty.share.lib.db.model;


import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


@Data
@ToString(callSuper = true)
public class LoyaltyCommunityLuckyDrawCode implements Serializable {
	private Integer id;
	private String redeemCode;
	private String codePlatform;
	private Boolean enabled;
	private Date importTime;
	private String userCellphone;
	private String openid;
	private String unionid;
	private Date redeemTime;
	private String creator;
	private String updator;
	private Date createTime;
	private Date updateTime;
	private Date startTime;
	private Date endTime;

}
