package loyalty.share.lib.db.model;


import lombok.Data;

import java.util.Date;


@Data
public class LoyaltyV2Log3rdpartycall {
	private Integer id;
	private String failRecordTable;
	private Long failRecordId;
	private String requestSenario;
	private String requestBody;
	private String requestUrl;
	private String responseBody;
	private String creator;
	private String updator;
	private Date createTime;
	private Date updateTime;

    public LoyaltyV2Log3rdpartycall() {
    }

    public LoyaltyV2Log3rdpartycall(String failRecordTable, Long failRecordId, String requestSenario, String requestUrl, String requestBody, String responseBody, String creator) {
        this.failRecordTable = failRecordTable;
        this.failRecordId = failRecordId;
        this.requestSenario = requestSenario;
        this.requestUrl = requestUrl;
        this.requestBody = requestBody;
        this.responseBody = responseBody;
        this.creator = creator;
    }

}
