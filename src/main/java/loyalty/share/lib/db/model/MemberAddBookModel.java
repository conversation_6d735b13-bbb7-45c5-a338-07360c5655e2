package loyalty.share.lib.db.model;

import lombok.Data;

import java.util.Date;


@Data
public class MemberAddBookModel extends ModelBase{
    private Long id;
    private Long bookId;
    private String cellphone;
    private Float currencyAmount;
    private Long eventId;
    private Long changedByAddId;
    private Date triggerTime;
    private Date expireTime;
    private Long closeExpendId;
    private Long inheritParentId;
    private String closeOperationId;
    private String createOperationId;

    public MemberAddBookModel() {
    }

    public MemberAddBookModel(Long bookId, String cellphone, Float currencyAmount, Long eventId, Long changedByAddId, Date triggerTime, Date expireTime,String closeOperationId, Long closeExpendId) {
        this.bookId = bookId;
        this.cellphone = cellphone;
        this.currencyAmount = currencyAmount;
        this.eventId = eventId;
        this.changedByAddId = changedByAddId;
        this.triggerTime = triggerTime;
        this.expireTime = expireTime;
        this.closeExpendId = closeExpendId;
        this.closeOperationId=closeOperationId;
    }

    public MemberAddBookModel(Long bookId, String cellphone, Float currencyAmount, Long eventId, Long changedByAddId, Date triggerTime, Date expireTime) {
        this.bookId = bookId;
        this.cellphone = cellphone;
        this.currencyAmount = currencyAmount;
        this.eventId = eventId;
        this.changedByAddId = changedByAddId;
        this.triggerTime = triggerTime;
        this.expireTime = expireTime;
    }
}
