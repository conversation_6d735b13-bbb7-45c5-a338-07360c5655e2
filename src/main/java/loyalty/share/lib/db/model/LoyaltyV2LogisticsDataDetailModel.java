package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoyaltyV2LogisticsDataDetailModel extends ModelBase {
    private Long id;
    private Long logisticsDataId;
    private Long orderDetailId;
    private Long giftId;
    private Long goodCount;
    private String logisticsStatus;
    private String logisticsParty;
    private String logisticsNo;
    private Date logisticsDeliverTime;
    private Date logisticsReceivedTime;

    public LoyaltyV2LogisticsDataDetailModel(Long logisticsDataId, Long orderDetailId, String logisticsStatus, Long goodCount) {
        this.logisticsDataId = logisticsDataId;
        this.orderDetailId = orderDetailId;
        this.logisticsStatus = logisticsStatus;
        this.goodCount = goodCount;
    }

    public LoyaltyV2LogisticsDataDetailModel(Long logisticsDataId, Long orderDetailId, String logisticsStatus, Long goodCount, Date logisticsDeliverTime) {
        this.logisticsDataId = logisticsDataId;
        this.orderDetailId = orderDetailId;
        this.logisticsStatus = logisticsStatus;
        this.goodCount = goodCount;
        this.logisticsDeliverTime = logisticsDeliverTime;
    }
}
