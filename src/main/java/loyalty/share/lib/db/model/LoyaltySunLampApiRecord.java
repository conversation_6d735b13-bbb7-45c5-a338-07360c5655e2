package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import java.util.Date;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * 太阳码-小程序码生成记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoyaltySunLampApiRecord extends ModelBase {

    /**
     * id
     */
    private Long id;

    /**
     * appid
     */
    private String appid;

    /**
     * 请求平台
     */
    private String reqPlatform;

    /**
     * 请求id
     */
    private String reqId;

    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求参数
     */
    private String reqParam;

    /**
     * 请求体
     */
    private String reqBody;

    /**
     * 响应体
     */
    private String respBody;

    /**
     * 响应码
     */
    private String respCode;

    /**
     * 是否请求成功
     */
    private Boolean isSuc;

    /**
     * 触发时间
     */
    private Date triggerTime;

}