package loyalty.share.lib.db.model;


import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class Answer {

    private Integer id;
    private Integer questionId;
    @NotNull(message = "选项类型不能为空")
    private Integer type;
    @NotBlank(message = "选项不能为空")
    private String answer;
    @NotNull(message = "是否为正确不能为空")
    private Integer isTrue;





}
