package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoyaltyMallPurchaseOrderDetailModel extends ModelBase{
    private Long id;
    private Long orderId;
    private Long commodityId;
    private Long sizeCombId;
    private Long commodityPriceId;
    private long commodityCount;
    private String priceData;
    private String externalOrderNo;

    private String currencyBookIds;
    private Long currencyId;
    private Float amount;

    private String commodityType;
    private String commodityMaterial;
    private String cellphone;
    private String userIp;

    private Float payAmount;

    public LoyaltyMallPurchaseOrderDetailModel(Long orderId, Long commodityId, Long sizeCombId, Long commodityPriceId, long commodityCount, String externalOrderNo, String currencyBookIds, Long currencyId, Float amount,String commodityType, String creator) {
        this.orderId = orderId;
        this.commodityId = commodityId;
        this.sizeCombId = sizeCombId;
        this.commodityPriceId = commodityPriceId;
        this.commodityCount = commodityCount;
        this.externalOrderNo = externalOrderNo;
        this.currencyBookIds = currencyBookIds;
        this.currencyId = currencyId;
        this.amount = amount;
        this.commodityType=commodityType;
        super.creator=creator;
    }

    public LoyaltyMallPurchaseOrderDetailModel(Long orderId, Long commodityId, Long sizeCombId, Long commodityPriceId, long commodityAmount, String creator) {
        this.orderId = orderId;
        this.commodityId = commodityId;
        this.sizeCombId = sizeCombId;
        this.commodityPriceId = commodityPriceId;
        this.commodityCount = commodityAmount;
        super.creator=creator;
    }
}
