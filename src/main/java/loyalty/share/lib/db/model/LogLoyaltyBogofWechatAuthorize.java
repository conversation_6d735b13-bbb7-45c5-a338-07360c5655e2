package loyalty.share.lib.db.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LogLoyaltyBogofWechatAuthorize extends BaseEntity implements Serializable{
	private Long id;
	private String cellphone;
	private Long ccCampaignId;
	private String openid;
	private String unionid;
	private String authType;
	private Boolean isAuth;

}
