package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoyaltyMallLogisticsAddress extends ModelBase {
    private Long id;
    private String userCellphone;
    private String country;
    private String province;
    private String city;
    private String area;
    private String road;
    private String detail;
    private String roomNumber;
    private String recvCellphone;
    private String recvName;
    private Boolean isDefault;
    private Long channelId;

    public LoyaltyMallLogisticsAddress(Long id, String userCellphone, String country, String province, String city, String area, String road, String detail,String roomNumber, String recvCellphone, String recvName, <PERSON><PERSON><PERSON> isDefault, String creator, String updator,Long channelId) {
        this.id = id;
        this.userCellphone = userCellphone;
        this.country = country;
        this.province = province;
        this.city = city;
        this.area = area;
        this.road = road;
        this.detail = detail;
        this.roomNumber=roomNumber;
        this.recvCellphone = recvCellphone;
        this.recvName = recvName;
        this.isDefault = isDefault;
        this.channelId=channelId;
        super.creator = creator;
        super.updator = updator;
    }
}
