package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoyaltyV2LogisticsDataModel extends ModelBase {
    private Long id;
    private Long orderId;
    private Long campaignHistoryBxgxId;
    private String country;
    private String province;
    private String city;
    private String area;
    private String road;
    private String detail;
    private String recvCellphone;
    private String recvName;
    private String fromCellphone;
    private Long channelId;
    private String userIp;
    private String userAgent;
    private String cityCode;
    private String provinceCode;
    private String areaCode;
    private String streetCode;
    private String roomNumber;

    public LoyaltyV2LogisticsDataModel(LoyaltyMallLogisticsAddress model,Long orderId,String fromCellphone,Long channelId,String userIp,String userAgent) {
        this.orderId=orderId;
        this.country=model.getCountry();
        this.province=model.getProvince();
        this.city=model.getCity();
        this.area=model.getArea();
        this.road=model.getRoad();
        this.detail=model.getDetail();
        this.recvCellphone=model.getRecvCellphone();
        this.recvName=model.getRecvName();
        this.fromCellphone=fromCellphone;
        this.channelId=channelId;
        this.userIp=userIp;
        this.userAgent=userAgent;
        this.roomNumber=model.getRoomNumber();
    }

    public LoyaltyV2LogisticsDataModel(LoyaltyMallLogisticsAddress model,Long orderId,String fromCellphone,Long channelId,String userIp,String userAgent,Long campaignHistoryBxgxId) {
        this.orderId=orderId;
        this.country=model.getCountry();
        this.province=model.getProvince();
        this.city=model.getCity();
        this.area=model.getArea();
        this.road=model.getRoad();
        this.detail=model.getDetail();
        this.recvCellphone=model.getRecvCellphone();
        this.recvName=model.getRecvName();
        this.fromCellphone=fromCellphone;
        this.channelId=channelId;
        this.userIp=userIp;
        this.userAgent=userAgent;
        this.roomNumber=model.getRoomNumber();
        this.campaignHistoryBxgxId=campaignHistoryBxgxId;
    }

    public LoyaltyV2LogisticsDataModel(Long orderId, String country, String province, String city, String area, String road, String detail, String recvCellphone, String recvName,String userIp,String userAgent,String roomNumber) {
        this.orderId=orderId;
        this.country = country;
        this.province = province;
        this.city = city;
        this.area = area;
        this.road = road;
        this.detail = detail;
        this.recvCellphone = recvCellphone;
        this.recvName = recvName;
        this.userIp=userIp;
        this.userAgent=userAgent;
        this.roomNumber = roomNumber;
    }
}
