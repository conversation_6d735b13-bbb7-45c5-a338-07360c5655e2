package loyalty.share.lib.db.model;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.util.Date;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PclassUserSignDetail extends BaseEntity implements Serializable{
	private Long id;
	private String pclassCode;
	private String cellphone;
	private String pnecId;
	private Date triggerTime;
	private String currentLongitude;
	private String currentLatitude;

}
