package loyalty.share.lib.db.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.util.Date;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyWxmppSubscribeMsgInfo extends BaseEntity implements Serializable {
    private Long id;
    private String appid;
    private String cellphone;
    private String unionid;
    private String openid;
    private String event;
    private String templateId;
    private String subscribeStatus;
    private Date subscribeTime;

}
