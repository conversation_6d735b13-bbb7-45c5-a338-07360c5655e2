package loyalty.share.lib.db.model;


import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import lombok.ToString;


import java.util.Date;
import lombok.Data;
import loyalty.share.lib.common.model.MainBaseAdminEntity;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PclassScheduleJobLog extends MainBaseAdminEntity implements Serializable{
	private Integer id;
	private String taskCode;
	private Date jobStarttime;
	private Date jobEndtime;
	private String result;
	private Integer isSuccess;
	private Long successCount;

}
