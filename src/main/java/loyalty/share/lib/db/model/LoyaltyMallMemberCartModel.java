package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoyaltyMallMemberCartModel extends ModelBase {
    private Long id;
    private String cellphone;
    private Long commodityId;
    private Long sizeCombId;
    private Long commodityAmount;
    private Date addTime;
    private Long channelId;
    private String userIp;
    private String userAgent;

    private String commodityType;

    public LoyaltyMallMemberCartModel(String cellphone, Long commodityId, Long sizeCombId, Long commodityAmount, Date addTime, String creator, Long channelId) {
        this.cellphone = cellphone;
        this.commodityId = commodityId;
        this.sizeCombId = sizeCombId;
        this.commodityAmount = commodityAmount;
        this.addTime = addTime;
        this.channelId = channelId;
        super.creator = creator;
        super.updator = creator;
    }

    public LoyaltyMallMemberCartModel(Long commodityId, Long sizeCombId,Long commodityAmount) {
        this.commodityId = commodityId;
        this.sizeCombId = sizeCombId;
        this.commodityAmount=commodityAmount;
    }
}
