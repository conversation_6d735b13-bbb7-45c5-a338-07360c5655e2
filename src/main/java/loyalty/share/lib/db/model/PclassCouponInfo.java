package loyalty.share.lib.db.model;


import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import lombok.ToString;


import java.util.Date;
import lombok.Data;
import loyalty.share.lib.common.model.MainBaseAdminEntity;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PclassCouponInfo extends MainBaseAdminEntity implements Serializable{
	private Integer id;
	private String pclassCode;
	private String firstCouponType;
	private String firstCouponContent;
	private String firstCouponAddress;
	private Integer firstCouponNum;
	private Date firstCouponExpirationDate;
	private String secCouponType;
	private String secCouponContent;
	private String secCouponAddress;
	private Integer secCouponNum;
	private Date secCouponExpirationDate;

}
