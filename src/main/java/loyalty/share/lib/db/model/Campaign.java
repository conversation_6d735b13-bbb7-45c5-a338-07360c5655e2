package loyalty.share.lib.db.model;

import lombok.Data;

import java.util.Date;

@Data
public class Campaign {
    private Long id;
    private String campaignName;
    private String description;
    private String platform;
    private Date startTime;
    private Date endTime;
    private Boolean isEnabled;
    private Boolean isDeleted;
    private Boolean hasEnabledBefore;
    private String campaignSeries;
    private String checkProperties;
    private String quotaDimension;
	private String disQuotaSenario;
	private String intName;
	private String intDescription;
	private String creator;
    private String updator;
    private Date signingStartTime;
    private Date signingEndTime;
    private String campaignSenario;

    private Date createTime;
    private Date updateTime;
}
