package loyalty.share.lib.db.model;

import lombok.Data;

import java.util.Date;

@Data
public class DBCommoditySize {
    private Long id;
    private Long commodityId;//产品编号
    private String optionKey;//规格
    private String optionValue;//规则值
    private Integer keySequency;//顺序
    private Integer valueSequency;//顺序
    private Long createdByUser;
    private Long updatedByUser;
    private Date createTime;
    private Date updateTime;


}
