package loyalty.share.lib.db.model;

import lombok.*;
import loyalty.share.lib.common.model.BaseEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/6 下午 03:15
 * @describe
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class LoyaltyCampaignShare extends BaseEntity {
    private Long id;
    private Long campaignId;
    private String ownerCellphone;
    private Boolean isFull;
    private Date triggerTime;
    private String wxaCode;
    private String unionid;
    private String openid;

    public LoyaltyCampaignShare(Long campaignId, String ownerCellphone, Boolean isFull, Date triggerTime,
                                String unionid, String openid, String creator
        , String updater) {
        this.campaignId = campaignId;
        this.ownerCellphone = ownerCellphone;
        this.isFull = isFull;
        this.triggerTime = triggerTime;
        super.setCreator(creator);
        super.setUpdater(updater);
        this.openid = openid;
        this.unionid = unionid;
    }
}
