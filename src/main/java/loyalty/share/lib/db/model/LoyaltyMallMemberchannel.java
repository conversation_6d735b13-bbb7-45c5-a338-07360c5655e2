package loyalty.share.lib.db.model;


import lombok.Data;

import java.util.Date;


@Data
public class LoyaltyMallMemberchannel {
    private Long id;
    private String cellphone;
    private Long syncTagId;
    private Long manualTagId;
    private Long currentTagId;
    private String currentTagType;
    private Date syncTime;
    private Boolean canSync;
    private String lastModifiedBy;
    private Long lastModiferId;
    private String lastModifyRemark;
    private String customizeTag;//(孩子王标签,和sftp啦过来的定制标签)
    private Date lastModifyTime;
    private String creator;
    private String updator;
    private Date createTime;
    private Date updateTime;
    private String md5;

}
