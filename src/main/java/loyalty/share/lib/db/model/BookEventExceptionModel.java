package loyalty.share.lib.db.model;

import lombok.Data;

import java.util.Date;

@Data
public class BookEventExceptionModel extends ModelBase {
    private Long id;
    private Long historyId;
    private Long eventId;
    private String eventParams;
    private String eventType;
    private String cellphone;
    private String relatedBookIds;
    private String relatedCurrencyIds;
    private String errMsg;
    private String errDetail;
    private Boolean readyToReprocess;

    private Long retryCount;
    private Date retryTimes;
    private Boolean hasProcessed;

    public BookEventExceptionModel() {
        super.creator = "loyalty-ledger-service";
    }

    public BookEventExceptionModel(Long eventId, String eventParams, String cellphone, String relatedBookIds, String errDetail, String errMsg) {
        this.eventId = eventId;
        this.eventParams = eventParams;
        this.cellphone = cellphone;
        this.relatedBookIds = relatedBookIds;
        this.errDetail = errDetail;
        this.errMsg = errMsg;
    }
}
