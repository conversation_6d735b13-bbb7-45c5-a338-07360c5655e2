package loyalty.share.lib.db.model;

import lombok.Data;

@Data
public class LoyaltyMallOrderPayRecord extends ModelBase{
    private Long id;
    private Boolean isPay;
    private Long orderDetailId;
    private String currencyBookIds;
    private Long currencyId;
    private Float amount;
    private String userIp;
    private String userAgent;

    public LoyaltyMallOrderPayRecord(Boolean isPay,Long orderDetailId, String currencyBookIds, Long currencyId, Float amount) {
        this.isPay=isPay;
        this.orderDetailId = orderDetailId;
        this.currencyBookIds = currencyBookIds;
        this.currencyId = currencyId;
        this.amount = amount;
    }
}
