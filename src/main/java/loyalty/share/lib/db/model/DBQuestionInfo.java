package loyalty.share.lib.db.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
@Data
public class DBQuestionInfo {


    private Integer id;
    private Integer campaignId ;
    private String question ;
    private Integer type ;
    private Integer IsEnabled ;
    private String series  ;
    private String questionAnalysis ;
    private Date createTime;
    private Date updateTime;
    private List<DBAnswer> answerList;//选项




}
