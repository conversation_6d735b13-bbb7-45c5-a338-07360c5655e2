package loyalty.share.lib.db.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyBogofCampaignProductScan extends BaseEntity implements Serializable{
	private Long id;
	private Long ccCampaignId;
	private String ccCampaignInstid;
	private String cellphone;
	private String digitcode;
	private Integer gravida;
	private String traceabilityCode;
	private String productSapid;
	private String productName;
	private Boolean hasQualification;
	private Date triggerTime;

}
