package loyalty.share.lib.db.model;


import lombok.Data;

import java.util.Date;


@Data
public class LoyaltyMallChannelTagResource {
	private Long id;
	private Long resourceId;
	private String resourceType;
	private Long tagId;
	private Long channelId;
	private String createdByUser;
	private String updatedByUser;
	private Date startTime;
	private Date endTime;
	private Date createTime;
	private Date updateTime;

	public LoyaltyMallChannelTagResource(Long resourceId, String resourceType, Long tagId, Long channelId, String createdByUser, String updatedByUser) {
		this.resourceId = resourceId;
		this.resourceType = resourceType;
		this.tagId = tagId;
		this.channelId = channelId;
		this.createdByUser = createdByUser;
		this.updatedByUser = updatedByUser;
	}
}
