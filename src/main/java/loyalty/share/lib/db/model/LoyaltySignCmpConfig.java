package loyalty.share.lib.db.model;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@NoArgsConstructor
public class LoyaltySignCmpConfig {
	private Long id;
	private Long campaignId;
	private Integer signOption; //0：每天积分相同   | 1：每天积分不同
	private Boolean isConsist;
	private Boolean isCycle;
	private Integer signDays;
	private String createdByUser;
	private String updatedByUser;
	private Date createTime;
	private Date updateTime;

	public LoyaltySignCmpConfig(Long campaignId, Integer signOption, Boolean isConsist, Boolean isCycle, String createdByUser, String updatedByUser) {
		this.campaignId = campaignId;
		this.signOption = signOption;
		this.isConsist = isConsist;
		this.isCycle = isCycle;
		this.createdByUser = createdByUser;
		this.updatedByUser = updatedByUser;
	}
}
