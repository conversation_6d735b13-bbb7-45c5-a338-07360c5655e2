package loyalty.share.lib.db.model;



import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyBogofCampaign extends BaseEntity implements Serializable{
	private Integer id;
	private String ccCampaignType;
	private Long ccCampaignId;
	private String templateId;
	private String configProperties;
	private String remark;
	private Integer createdByUser;
	private Integer updatedByUser;
}
