package loyalty.share.lib.db.model;



import lombok.EqualsAndHashCode;
import lombok.ToString;

import loyalty.share.lib.common.model.MainBaseAdminEntity;

import lombok.Data;
import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyMallPageModule extends MainBaseAdminEntity implements Serializable{
	private Long id;
	private String moduleName;
	private Integer moduleGroupId;
	private String moduleType;
	private String moduleTitle;
	private String moduleDesc;
	private String moduleIcon;
	private String moduleSelectedIcon;
	private String mainContent;
	private String backgroundColor;
	private String backgroundImage;
	private String fontColor;
	private String cssStyle;
	private String link;
	private Integer sort;
	private String extra;
	private String remark;
	private Integer isEnabled;
	private String updator;

}
