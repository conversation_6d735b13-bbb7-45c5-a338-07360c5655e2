package loyalty.share.lib.db.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyBogofCampaignGiftDetail extends BaseEntity implements Serializable{
	private Long id;
	private Long loyaltyBogofCampaignRecordId;
	private Long giftId;
	private String giftType;
	private String sendType;
	private String giftInstId;
	private String giftName;
	private String giftValue;
	private Integer giftNum;
	private String supplier;
	private String outboundPosition;
	private String giftImgUrl;
	private String youanOrderSn;
	private String qrCode;
	private Date triggerTime;
}
