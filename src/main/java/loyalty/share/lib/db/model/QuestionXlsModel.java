package loyalty.share.lib.db.model;


import data.model.generic.tool.annotation.DBField;
import data.model.generic.tool.annotation.XlsField;
import lombok.Data;

import java.util.Date;
import java.util.List;
/**
 * excel导出模型
 */
@Data
public class QuestionXlsModel {
    //TODO zyh 与业务相关的所有东西都扔自己的service , share-library 只留DBModel 和mapper
    @DBField(dbcolumn = "id")
    @XlsField(title = "编号",  columnIndex = 0)
    private String id;
    @DBField(dbcolumn = "campaignId")
    @XlsField(title = "品牌id",  columnIndex = 1)
    private String campaignId ;

    @DBField(dbcolumn = "question")
    @XlsField(title = "问题",  columnIndex = 2)
    private String question ;

    @DBField(dbcolumn = "IsEnabled")
    @XlsField(title = "是否启用",  columnIndex = 3)
    private String IsEnabled ;

    @DBField(dbcolumn = "series")
    @XlsField(title = "品牌分类",  columnIndex = 4)
    private String series  ;

    @DBField(dbcolumn = "questionAnalysis")
    @XlsField(title = "答案解析",  columnIndex = 5)
    private String questionAnalysis ;

    @DBField(dbcolumn = "type")
    @XlsField(title = "问题类型",  columnIndex = 6)
    private String type;

    @DBField(dbcolumn = "correctAnswer")
    @XlsField(title = "正确答案",  columnIndex = 7)
    private String correctAnswer;//正确答案

    @DBField(dbcolumn = "Options")
    @XlsField(title = "选项",  columnIndex = 8)
    private String OptionsA;//选项

    @DBField(dbcolumn = "Options")
    @XlsField(title = "选项",  columnIndex = 9)
    private String OptionsB;//选项

   /* @DBField(dbcolumn = "Options")
    @XlsField(title = "选项",  columnIndex = 10)
    private String OptionsC;//选项

    @DBField(dbcolumn = "Options")
    @XlsField(title = "选项",  columnIndex = 11)
    private String OptionsD;//选项*/




    public QuestionXlsModel(QuestionList questionList) {
        this.id = String.valueOf(questionList.getId());
        this.campaignId = String.valueOf(questionList.getCampaignId());
        this.question = questionList.getQuestion();
        if (questionList.getIsEnabled()==1){
            this.IsEnabled="是";
        }else {
            this.IsEnabled="否";
        }
        this.series = questionList.getSeries();
        this.questionAnalysis = questionList.getQuestionAnalysis();
        if (questionList.getType()==1){
            this.type="问卷调查";
        }else {
            this.type="问题";
        }
        questionList.getAnswerList().forEach(e->{
            if (e.getIsTrue()==1){
                this.correctAnswer =e.getAnswer();
            }

        });
        this.OptionsA=questionList.getAnswerList().get(0).getAnswer();
        this.OptionsB=questionList.getAnswerList().get(1).getAnswer();

    }
}
