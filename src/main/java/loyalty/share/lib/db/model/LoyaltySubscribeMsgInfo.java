package loyalty.share.lib.db.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltySubscribeMsgInfo extends BaseEntity implements Serializable{
	private Long id;
	private String cellphone;
	private String unionid;
	private String openid;
	private String sourceTable;
	private Long sourceId;
	private Long campaignId;
	private Long wxmppSubscribeMsgInfoId;
	private Integer isSend;

}
