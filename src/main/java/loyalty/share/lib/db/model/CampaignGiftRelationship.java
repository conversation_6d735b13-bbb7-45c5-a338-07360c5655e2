package loyalty.share.lib.db.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class CampaignGiftRelationship {
    private Long id;
    private Long giftId;
    private Long campaignId;
    private Integer giftCount;
    //todo: remove later, Q4发布后
    private Long giftRuleId;
    //todo: remove later, Q4发布后
    private Integer priority;
    //todo: remove later，配额工具修改后
    private String giftQuotaDimension;
    private String creator;
    private String updator;
    private Date createTime;
    private Date updateTime;
    private String rewardReasonId;
    private Long quotaId;
}
