package loyalty.share.lib.db.model;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Builder
public class LogLoyaltyMallRegister {

    private Long id;
    private String registerCellphone;
    private String registerUnionid;
    private String registerOpenid;
    private String recruitCellphone;
    private String  subChannel;
    private String source;
    private String registerInfo;
    private Date triggerTime;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;




}
