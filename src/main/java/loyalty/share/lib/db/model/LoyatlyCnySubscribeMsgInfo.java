package loyalty.share.lib.db.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyatlyCnySubscribeMsgInfo extends BaseEntity implements Serializable {
    private Long id;
    private String cellphone;
    private String unionid;
    private String openid;
    private String subscribeType;
    private Long shareId;
    private Long campaignId;
    private Long wxmppSubscribeMsgInfoId;
    private Boolean isSend;
}
