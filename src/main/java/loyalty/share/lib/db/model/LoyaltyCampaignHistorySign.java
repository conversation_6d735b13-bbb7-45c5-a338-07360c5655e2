package loyalty.share.lib.db.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoyaltyCampaignHistorySign {
	private Long id;
	private Long campaignId;
	private Long channelId;
	private Long giftId;
	private String cellphone;
	private Date triggerTime;
	private String signKey;
	private String reasonCode;
	private String unionId;
	private Float amount;
	private String createdByUser;
	private Date createTime;
	private Date updateTime;

	public LoyaltyCampaignHistorySign(Long campaignId,Long channelId, Long giftId, String cellphone, Date triggerTime, String signKey, String reasonCode,String unionId, Float amount, String createdByUser) {
		this.campaignId = campaignId;
		this.channelId = channelId;
		this.giftId = giftId;
		this.cellphone = cellphone;
		this.triggerTime = triggerTime;
		this.signKey = signKey;
		this.reasonCode = reasonCode;
		this.unionId = unionId;
		this.amount = amount;
		this.createdByUser = createdByUser;
	}
}
