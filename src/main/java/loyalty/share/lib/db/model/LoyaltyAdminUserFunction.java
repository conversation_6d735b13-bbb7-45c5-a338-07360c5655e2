package loyalty.share.lib.db.model;


import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class LoyaltyAdminUserFunction implements Comparable<LoyaltyAdminUserFunction> {
    /*private Long id;
    private String functionName;
    private String functionCode;
    private String creator;
    private String updator;
    private Date createTime;
    private Date updateTime;
    private String requestPath;*/
    private Long id;
    private String name;
    private String title;
    private String label;
    private Long parentId;
    private Integer sequence;
    private String url;
    private String icon;
    private String status;
    private String component;
    private Integer type;
    private String permission;
    private String creator;
    private String updator;
    private Date createTime;
    private Date updateTime;
    private List<LoyaltyAdminUserFunction> children;

    @Override
    public int compareTo(LoyaltyAdminUserFunction menu) {
        return this.sequence - menu.getSequence();
    }

}
