package loyalty.share.lib.db.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class LoyaltyMallPageComponentLayout implements Serializable {
    private Long id;
    private Integer pageId;
    private String componentTitle;
    private String componentCode;
    private String position;
    private Integer positionX;
    private Integer positionY;
    private Integer width;
    private Integer height;
    private String contentProperties;
    private String styleProperties;
    private Integer sort;

    private String creator;
    private String updator;
    private Date createTime;
    private Date updateTime;

}
