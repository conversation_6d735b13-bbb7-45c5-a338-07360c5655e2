package loyalty.share.lib.db.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.BaseEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/6 下午 03:15
 * @describe
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoyaltyCampaignShareDetail extends BaseEntity {
    private Long id;
    private Long shareId;
    private String assistanceCellphone;
    private Boolean isNua;
    private Date bindTime;
    private String username;
    private String unionid;
    private String openid;
}
