package loyalty.share.lib.db.model;


import java.util.Date;
import lombok.Data;


@Data
public class LoyaltyV2MemberCurrencyAdd {
	private Long id;
	private Long currencyId;
	private String cellphone;
	private Integer memberGrade;
	private Integer currentMemberGrade;
	private Float currencyAmount;
	private Long eventId;
	private String reasonCode;
	private String historyTableName;
	private Long changedByHistoryId;
	private Long changedByCmpId;
	private Long loyaltyChannelId;
	private Date triggerTime;
	private Date expireTime;
	private String creator;
	private String updator;
	private Date createTime;
	private Date updateTime;
	private Long migrateId;
	private String migrateKey;
	private Long changedByReduceId;
	private Float usedAmount;
	private Long currencyBookId;
	private Long userTagId;
	private String userIp;
	private String userAgent;

}
