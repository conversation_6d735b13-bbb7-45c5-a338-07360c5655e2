package loyalty.share.lib.db.model;

import lombok.Data;

import java.util.Date;

@Data
public class DBCommodity {
    private Long id;
    private String commodityCode;//商品编码
    private String commodityName;//商品名称
    private String description;//商品描述
    private Boolean isVisible;//上下架
    private String category;//商品分组
    private String classes;//商品分类
    private String brandName;//品牌名称
    private String model;//商品型号
    private Double marketPrice;//市场价
    private Double purchasePrice;//进货价
    private Boolean isMultiSize;//是否多规格商品
    private Boolean isPricingByMemberlv;//是否按会员等级定价
    private String detailContent;//详情
    private Integer supplierId;//供应商名称
    private String supplierName;//供应商名称
    private String supplierCommodityCode;//供应商产品编码
    private String channelId;
    private String tagList;
    private String virtualType;//虚拟商品类型
    private Integer historyBuyQuantity;
    private Date shelveTime;
    private Date offShelveTime;
    private Long buyLimitMax;
    //虚拟商品的跳转连接
    private String externalLink;

    private Long createdByUser;
    private Long updatedByUser;
    private Date createTime;
    private Date updateTime;
    //额外增加字段
    private String imgFileName;
    private Long accumultateAmount;
    private Long currentAvailableAmount;
    private Long redemptionAmount;
    private Long safeStorage;

    private String currencyContents;
}
