package loyalty.share.lib.db.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoyaltyMallPurchaseOrderModel extends ModelBase {
    private Long id;
    private String cellphone;
    private String payStatus;
    private Boolean isValid;
    private Date orderTime;
    private String externalOrderStatus;
    private String externalOrderNo;
    private String orderUniqueKey;
    private Long externalReduceId;
    private Long currentMemberTagId;
    private Long channelId;
    private String userIp;
    private String userAgent;
    private String orderNo;
    private String openId;


    public LoyaltyMallPurchaseOrderModel(Long currentMemberTagId,String cellphone,String externalOrderNo,String orderUniqueKey, String payStatus, Boolean isValid, Date orderTime,String creator,Long channelId,String userIp,String openId) {
        this.currentMemberTagId=currentMemberTagId;
    	this.cellphone = cellphone;
        this.externalOrderNo=externalOrderNo;
        this.orderUniqueKey=orderUniqueKey;
        this.payStatus = payStatus;
        this.isValid = isValid;
        this.orderTime = orderTime;
        this.channelId=channelId;
        super.creator=creator;
        this.userIp=userIp;
        this.openId=openId;
    }
}
