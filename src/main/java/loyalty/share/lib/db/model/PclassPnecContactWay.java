package loyalty.share.lib.db.model;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import loyalty.share.lib.common.model.MainBaseAdminEntity;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PclassPnecContactWay extends MainBaseAdminEntity implements Serializable{
	private Long id;
	private String userId;
	private String configId;
	private String qrcodeType;
	private Integer type;
	private Integer scene;
	private String qrCode;
	private String state;
	private Integer isTemp;
	private Integer skipVerify;
	private String rawData;

}
