package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.Loyalty3rdpartyQualification;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;

@Mapper
public interface Loyalty3rdpartyQualificationMapper {

    @Insert("INSERT INTO `loyalty_thirdpart_qualification` (`id`, `cellphone`, `campaign_id`, `sub_channel`, `status`, `creator`, `updator`, `create_time`, `update_time`) VALUES " +
            " (#{id}, #{cellphone}, #{campaignId}, #{subChannel}, #{status}, #{creator}, #{updator}, #{createTime}, #{updateTime});")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Loyalty3rdpartyQualification loyalty3rdpartyQualification);
}
