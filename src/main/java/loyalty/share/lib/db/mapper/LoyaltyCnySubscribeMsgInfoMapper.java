package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyCnySubscribeMsgInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltyCnySubscribeMsgInfoMapper {

     @Insert("INSERT IGNORE INTO loyalty_cny_subscribe_msg_info(id,cellphone,unionid,openid,share_id,campaign_id,wxmpp_subscribe_msg_info_id,is_send,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{unionid},#{openid},#{shareId},#{campaignId},#{wxmppSubscribeMsgInfoId},#{isSend},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyCnySubscribeMsgInfo loyaltyCnySubscribeMsgInfo);

    @Insert("INSERT INTO loyalty_cny_subscribe_msg_info(id,cellphone,unionid,openid,share_id,campaign_id,wxmpp_subscribe_msg_info_id,is_send,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{unionid},#{openid},#{shareId},#{campaignId},#{wxmppSubscribeMsgInfoId},#{isSend},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE unionid=VALUES(unionid),openid=VALUES(openid),campaign_id=VALUES(campaign_id),is_send=VALUES(is_send),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyCnySubscribeMsgInfo loyaltyCnySubscribeMsgInfo);

    @Update("UPDATE loyalty_cny_subscribe_msg_info set cellphone=#{cellphone},unionid=#{unionid},openid=#{openid},share_id=#{shareId},campaign_id=#{campaignId},wxmpp_subscribe_msg_info_id=#{wxmppSubscribeMsgInfoId},is_send=#{isSend},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LoyaltyCnySubscribeMsgInfo loyaltyCnySubscribeMsgInfo);

    @Delete("DELETE FROM loyalty_cny_subscribe_msg_info WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_cny_subscribe_msg_info set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,cellphone,unionid,openid,share_id,campaign_id,wxmpp_subscribe_msg_info_id,is_send,creator,updater,create_time,update_time FROM loyalty_cny_subscribe_msg_info WHERE id=#{id} ")
    @Results(id = "loyaltyCnySubscribeMsgInfo-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "unionid", column = "unionid"),
      @Result(property = "openid", column = "openid"),
      @Result(property = "shareId", column = "share_id"),
      @Result(property = "campaignId", column = "campaign_id"),
      @Result(property = "wxmppSubscribeMsgInfoId", column = "wxmpp_subscribe_msg_info_id"),
      @Result(property = "isSend", column = "is_send"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyCnySubscribeMsgInfo getByIdEX(@Param("id") Long id);

    @Select("SELECT id,cellphone,unionid,openid,share_id,campaign_id,wxmpp_subscribe_msg_info_id,is_send,creator,updater,create_time,update_time FROM loyalty_cny_subscribe_msg_info WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyCnySubscribeMsgInfo-mapping")
    LoyaltyCnySubscribeMsgInfo getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys
    @Select("SELECT id,cellphone,unionid,openid,share_id,campaign_id,wxmpp_subscribe_msg_info_id,is_send,creator,updater,create_time,update_time FROM loyalty_cny_subscribe_msg_info WHERE cellphone=#{cellphone} and share_id=#{shareId} and wxmpp_subscribe_msg_info_id=#{wxmppSubscribeMsgInfoId} ")
    @ResultMap(value = "loyaltyCnySubscribeMsgInfo-mapping")
    LoyaltyCnySubscribeMsgInfo getByCellphoneShareIdWxmppSubscribeMsgInfoId(@Param("cellphone") String cellphone,@Param("shareId") Long shareId,@Param("wxmppSubscribeMsgInfoId") Long wxmppSubscribeMsgInfoId);



//update status sqls


//get data by foreign keys

}
