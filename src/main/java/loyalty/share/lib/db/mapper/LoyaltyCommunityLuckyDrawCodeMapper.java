package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyCommunityLuckyDrawCode;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyCommunityLuckyDrawCodeMapper {

    @Insert("INSERT IGNORE INTO loyalty_community_lucky_draw_code(id,redeem_code,code_platform,is_enabled,import_time,user_cellphone,openid,unionid,redeem_time,creator,updator)" +
            " VALUES(#{id},#{redeemCode},#{codePlatform},#{enabled},#{importTime},#{userCellphone},#{openid},#{unionid},#{redeemTime},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(LoyaltyCommunityLuckyDrawCode loyaltyCommunityLuckyDrawCode);

    @Insert("INSERT INTO loyalty_community_lucky_draw_code(id,redeem_code,code_platform,is_enabled,import_time,user_cellphone,openid,unionid,redeem_time,creator,updator)" +
            " VALUES(#{id},#{redeemCode},#{codePlatform},#{enabled},#{importTime},#{userCellphone},#{openid},#{unionid},#{redeemTime},#{creator},#{updator})" +
            " ON DUPLICATE KEY UPDATE redeem_code=VALUES(redeem_code),code_platform=VALUES(code_platform),is_enabled=VALUES(is_enabled),import_time=VALUES(import_time),user_cellphone=VALUES(user_cellphone),openid=VALUES(openid),unionid=VALUES(unionid),redeem_time=VALUES(redeem_time),updator=VALUES(updator)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(LoyaltyCommunityLuckyDrawCode loyaltyCommunityLuckyDrawCode);

    @Update("UPDATE loyalty_community_lucky_draw_code set redeem_code=#{redeemCode},code_platform=#{codePlatform},is_enabled=#{enabled},import_time=#{importTime},user_cellphone=#{userCellphone},openid=#{openid},unionid=#{unionid},redeem_time=#{redeemTime},updator=#{updator} WHERE id=#{id}")
    int updateByEntity(LoyaltyCommunityLuckyDrawCode loyaltyCommunityLuckyDrawCode);

    @Delete("DELETE FROM loyalty_community_lucky_draw_code WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Integer id);


    @Select("SELECT id,redeem_code,code_platform,is_enabled,import_time,user_cellphone,openid,unionid,redeem_time,creator,updator,create_time,update_time FROM loyalty_community_lucky_draw_code WHERE id=#{id} ")
    @Results(id = "loyaltyCommunityLuckyDrawCode-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "redeemCode", column = "redeem_code"),
            @Result(property = "codePlatform", column = "code_platform"),
            @Result(property = "enabled", column = "is_enabled"),
            @Result(property = "importTime", column = "import_time"),
            @Result(property = "userCellphone", column = "user_cellphone"),
            @Result(property = "openid", column = "openid"),
            @Result(property = "unionid", column = "unionid"),
            @Result(property = "redeemTime", column = "redeem_time"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time")
    })
    LoyaltyCommunityLuckyDrawCode getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,redeem_code,code_platform,is_enabled,import_time,user_cellphone,openid,unionid,redeem_time,creator,updator,create_time,update_time FROM loyalty_community_lucky_draw_code WHERE id=#{id} ")
    @ResultMap(value = "loyaltyCommunityLuckyDrawCode-mapping")
    LoyaltyCommunityLuckyDrawCode getByIdFilterIsDeleted(@Param("id") Integer id);


    @Update("UPDATE loyalty_community_lucky_draw_code set user_cellphone=#{userCellphone},openid=#{openid},unionid=#{unionid},redeem_time=#{redeemTime},updator=#{updator} " +
            " WHERE redeem_code=#{redeemCode} AND code_platform=#{codePlatform} and user_cellphone is null and redeem_time is null  ")
    int updateRedeemData(LoyaltyCommunityLuckyDrawCode loyaltyCommunityLuckyDrawCode);

    @Select("SELECT id,redeem_code,code_platform,is_enabled,import_time,user_cellphone,openid,unionid,redeem_time,creator,updator,create_time,update_time,start_time,end_time " +
            " FROM loyalty_community_lucky_draw_code " +
            " WHERE redeem_code=#{redeemCode} and code_platform=#{codePlatform} and is_enabled=1 ")
    @ResultMap(value = "loyaltyCommunityLuckyDrawCode-mapping")
    LoyaltyCommunityLuckyDrawCode getByCodeAndPlatform(@Param("redeemCode") String redeemCode, @Param("codePlatform") String codePlatform);

    @Select("SELECT id,redeem_code,code_platform,is_enabled,import_time,user_cellphone,openid,unionid,redeem_time,creator,updator,create_time,update_time ,start_time,end_time" +
            " FROM loyalty_community_lucky_draw_code " +
            " WHERE user_cellphone=#{userCellphone} and code_platform=#{codePlatform} ")
    @ResultMap(value = "loyaltyCommunityLuckyDrawCode-mapping")
    LoyaltyCommunityLuckyDrawCode getByCellphoneAndPlatform(@Param("userCellphone") String userCellphone, @Param("codePlatform") String codePlatform);
}
