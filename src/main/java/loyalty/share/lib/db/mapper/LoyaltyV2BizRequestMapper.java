package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2BizRequest;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyV2BizRequestMapper {

    @Insert("INSERT INTO loyalty_v2_biz_request(id,request_unique_key,process_data,channel_id,event_id)" +
            " VALUES(#{id},#{requestUniqueKey},#{processData},#{channelId},#{eventId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2BizRequest loyaltyV2BizRequest);

    @Insert("INSERT IGNORE INTO loyalty_v2_biz_request(id,request_unique_key,process_data,channel_id,event_id)" +
            " VALUES(#{id},#{requestUniqueKey},#{processData},#{channelId},#{eventId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyV2BizRequest loyaltyV2BizRequest);

    @Insert("INSERT INTO loyalty_v2_biz_request(id,request_unique_key,process_data,channel_id,event_id)" +
            " VALUES(#{id},#{requestUniqueKey},#{processData},#{channelId},#{eventId})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),request_unique_key=VALUES(request_unique_key),process_data=VALUES(process_data),channel_id=VALUES(channel_id),event_id=VALUES(event_id)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyV2BizRequest loyaltyV2BizRequest);

    @Update("UPDATE loyalty_v2_biz_request set id=#{id},request_unique_key=#{requestUniqueKey},process_data=#{processData},channel_id=#{channelId},event_id=#{eventId} WHERE id=#{id}")
    int updateById(LoyaltyV2BizRequest loyaltyV2BizRequest);

    @Delete("DELETE FROM loyalty_v2_biz_request WHERE id=#{id}")
    int deleteById(LoyaltyV2BizRequest loyaltyV2BizRequest);


    @Select("SELECT * FROM loyalty_v2_biz_request WHERE id=#{id} ")
    @Results(id = "loyaltyV2BizRequest-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "requestUniqueKey", column = "request_unique_key"), @Result(property = "processData", column = "process_data"), @Result(property = "channelId", column = "channel_id"), @Result(property = "eventId", column = "event_id"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyV2BizRequest getById(@Param("id") Long id);
}
