package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2MemberCurrencyAdd;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyV2MemberCurrencyAddMapper {

    @Insert("INSERT INTO loyalty_v2_member_currency_add(currency_id,cellphone,member_grade,current_member_grade,currency_amount,event_id,reason_code,history_table_name,changed_by_history_id,changed_by_cmp_id,loyalty_channel_id,trigger_time,expire_time,creator,updator,migrate_id,migrate_key,changed_by_reduce_id,used_amount,user_tag_id,currency_book_id,user_ip,user_agent)" +
            " VALUES(#{currencyId},#{cellphone},#{memberGrade},#{currentMemberGrade},#{currencyAmount},#{eventId},#{reasonCode},#{historyTableName},#{changedByHistoryId},#{changedByCmpId},#{loyaltyChannelId},#{triggerTime},#{expireTime},#{creator},#{creator},#{migrateId},#{migrateKey},#{changedByReduceId},#{usedAmount},#{userTagId},#{currencyBookId},#{userIp},#{userAgent})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2MemberCurrencyAdd loyaltyV2MemberCurrencyAdd);

    @Insert("INSERT IGNORE INTO loyalty_v2_member_currency_add(currency_id,cellphone,member_grade,current_member_grade,currency_amount,event_id,reason_code,history_table_name,changed_by_history_id,changed_by_cmp_id,loyalty_channel_id,trigger_time,expire_time,creator,updator,migrate_id,migrate_key,changed_by_reduce_id,used_amount,user_tag_id,currency_book_id,user_ip,user_agent)" +
            " VALUES(#{currencyId},#{cellphone},#{memberGrade},#{currentMemberGrade},#{currencyAmount},#{eventId},#{reasonCode},#{historyTableName},#{changedByHistoryId},#{changedByCmpId},#{loyaltyChannelId},#{triggerTime},#{expireTime},#{creator},#{creator},#{migrateId},#{migrateKey},#{changedByReduceId},#{usedAmount},#{userTagId},#{currencyBookId},#{userIp},#{userAgent})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyV2MemberCurrencyAdd loyaltyV2MemberCurrencyAdd);

    @Insert("INSERT INTO loyalty_v2_member_currency_add(currency_id,cellphone,member_grade,current_member_grade,currency_amount,event_id,reason_code,history_table_name,changed_by_history_id,changed_by_cmp_id,loyalty_channel_id,trigger_time,expire_time,creator,updator,migrate_id,migrate_key,changed_by_reduce_id,used_amount,user_tag_id,currency_book_id)" +
            " VALUES(#{currencyId},#{cellphone},#{memberGrade},#{currentMemberGrade},#{currencyAmount},#{eventId},#{reasonCode},#{historyTableName},#{changedByHistoryId},#{changedByCmpId},#{loyaltyChannelId},#{triggerTime},#{expireTime},#{creator},#{updator},#{migrateId},#{migrateKey},#{changedByReduceId},#{usedAmount},#{userTagId},#{currencyBookId})" +
		    " ON DUPLICATE KEY UPDATE id=VALUES(id),currency_id=VALUES(currency_id),cellphone=VALUES(cellphone),member_grade=VALUES(member_grade),current_member_grade=VALUES(current_member_grade),currency_amount=VALUES(currency_amount),event_id=VALUES(event_id),reason_code=VALUES(reason_code),history_table_name=VALUES(history_table_name),changed_by_history_id=VALUES(changed_by_history_id),changed_by_cmp_id=VALUES(changed_by_cmp_id)," +
		    "loyalty_channel_id=VALUES(loyalty_channel_id),trigger_time=VALUES(trigger_time),expire_time=VALUES(expire_time),updator=VALUES(updator),migrate_id=VALUES(migrate_id),migrate_key=VALUES(migrate_key),changed_by_reduce_id=VALUES(changed_by_reduce_id),used_amount=VALUES(used_amount),user_tag_id=VALUES(user_tag_id),currency_book_id=VALUES(currency_book_id)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyV2MemberCurrencyAdd loyaltyV2MemberCurrencyAdd);

    @Update("UPDATE loyalty_v2_member_currency_add set currency_id=#{currencyId},cellphone=#{cellphone},member_grade=#{memberGrade},current_member_grade=#{currentMemberGrade},currency_amount=#{currencyAmount},event_id=#{eventId},reason_code=#{reasonCode},history_table_name=#{historyTableName},changed_by_history_id=#{changedByHistoryId}," +
		    " changed_by_cmp_id=#{changedByCmpId},loyalty_channel_id=#{loyaltyChannelId},trigger_time=#{triggerTime},expire_time=#{expireTime},updator=#{updator},migrate_id=#{migrateId},migrate_key=#{migrateKey},changed_by_reduce_id=#{changedByReduceId},used_amount=#{usedAmount},user_tag_id=#{userTagId},currency_book_id=#{currencyBookId} WHERE id=#{id}")
    int updateById(LoyaltyV2MemberCurrencyAdd loyaltyV2MemberCurrencyAdd);

    @Delete("DELETE FROM loyalty_v2_member_currency_add WHERE id=#{id}" )
    int deleteById(LoyaltyV2MemberCurrencyAdd loyaltyV2MemberCurrencyAdd);


    @Select("SELECT * FROM loyalty_v2_member_currency_add WHERE id=#{id} ")
    @Results(id = "loyaltyV2MemberCurrencyAdd-mapping", value = {
    @Result(property = "id", column = "id"),@Result(property = "currencyId", column = "currency_id"),@Result(property = "cellphone", column = "cellphone"),@Result(property = "memberGrade", column = "member_grade"),@Result(property = "currentMemberGrade", column = "current_member_grade"),@Result(property = "currencyAmount", column = "currency_amount"),@Result(property = "eventId", column = "event_id"),@Result(property = "reasonCode", column = "reason_code"),@Result(property = "historyTableName", column = "history_table_name"),@Result(property = "changedByHistoryId", column = "changed_by_history_id"),@Result(property = "changedByCmpId", column = "changed_by_cmp_id"),@Result(property = "loyaltyChannelId", column = "loyalty_channel_id"),@Result(property = "triggerTime", column = "trigger_time"),@Result(property = "expireTime", column = "expire_time"),@Result(property = "creator", column = "creator"),@Result(property = "updator", column = "updator"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time"),@Result(property = "migrateId", column = "migrate_id"),@Result(property = "migrateKey", column = "migrate_key"),@Result(property = "changedByReduceId", column = "changed_by_reduce_id"),@Result(property = "usedAmount", column = "used_amount")
    })
    LoyaltyV2MemberCurrencyAdd getById(@Param("id") Long id);

	@Select({"<script>" +
			" select a.cellphone,a.currency_amount,a.trigger_time,r.reason_remark as reasonCode from " +
			" loyalty_v2_member_currency_add a LEFT JOIN loyalty_v2_dict_currency_reason r " +
			" on a.reason_code=r.reason_code where  1=1 "+
			" <when test=\"cellphone!=null and cellphone !='' \">",
			"  and a.cellphone=#{cellphone} ",
			" </when>",
			" <when test=\"giftId!=null and giftId !='' \">",
			"  and a.currency_id =#{giftId}  ",
			" </when>",
			" <when test=\"startAt !=null and startAt !='' \">",
			"  and a.trigger_time &gt;=#{startAt} ",
			" </when>",
			" <when test=\"endAt!=null and endAt !='' \">",
			"  and a.trigger_time &lt;= #{endAt} ",
			" </when>",
			" </script>"})
	@ResultMap({"loyaltyV2MemberCurrencyAdd-mapping"})
	List<LoyaltyV2MemberCurrencyAdd> findMemberCurrencyAddList(@Param("cellphone") String cellphone, @Param("giftId")Long giftId,@Param("startAt")String startAt,@Param("endAt")String endAt);
}
