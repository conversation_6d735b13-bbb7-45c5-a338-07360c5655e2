package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyBogofCampaignRecord;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltyBogofCampaignRecordMapper {

     @Insert("INSERT IGNORE INTO loyalty_bogof_campaign_record(id,cellphone,openid,unionid,cc_campaign_id,cc_campaign_instId,cc_campaign_type,cc_campaign_code,cc_participated_time,order_no,gravida,longitude,latitude,trigger_time,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{openid},#{unionid},#{ccCampaignId},#{ccCampaignInstId},#{ccCampaignType},#{ccCampaignCode},#{ccParticipatedTime},#{orderNo},#{gravida},#{longitude},#{latitude},#{triggerTime},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyBogofCampaignRecord loyaltyBogofCampaignRecord);

    @Insert("INSERT INTO loyalty_bogof_campaign_record(id,cellphone,openid,unionid,cc_campaign_id,cc_campaign_instId,cc_campaign_type,cc_campaign_name,cc_campaign_code,cc_participated_time,order_no,gravida,longitude,latitude,trigger_time,creator,updater)" +
            " VALUES(#{id},#{cellphone},#{openid},#{unionid},#{ccCampaignId},#{ccCampaignInstId},#{ccCampaignType},#{ccCampaignName},#{ccCampaignCode},#{ccParticipatedTime},#{orderNo},#{gravida},#{longitude},#{latitude},#{triggerTime},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertEntity(LoyaltyBogofCampaignRecord loyaltyBogofCampaignRecord);

    @Insert("INSERT INTO loyalty_bogof_campaign_record_black(id,cellphone,openid,unionid,cc_campaign_id,cc_campaign_instId,cc_campaign_type,cc_campaign_name,cc_campaign_code,cc_participated_time,order_no,gravida,longitude,latitude,trigger_time,creator,updater)" +
            " VALUES(#{id},#{cellphone},#{openid},#{unionid},#{ccCampaignId},#{ccCampaignInstId},#{ccCampaignType},#{ccCampaignName},#{ccCampaignCode},#{ccParticipatedTime},#{orderNo},#{gravida},#{longitude},#{latitude},#{triggerTime},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertBlackEntity(LoyaltyBogofCampaignRecord loyaltyBogofCampaignRecord);

    @Insert("INSERT INTO loyalty_bogof_campaign_record(id,cellphone,openid,unionid,cc_campaign_id,cc_campaign_instId,cc_campaign_type,cc_campaign_code,cc_participated_time,order_no,gravida,longitude,latitude,trigger_time,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{openid},#{unionid},#{ccCampaignId},#{ccCampaignInstId},#{ccCampaignType},#{ccCampaignCode},#{ccParticipatedTime},#{orderNo},#{gravida},#{longitude},#{latitude},#{triggerTime},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE cellphone=VALUES(cellphone),openid=VALUES(openid),unionid=VALUES(unionid),cc_campaign_id=VALUES(cc_campaign_id),cc_campaign_instId=VALUES(cc_campaign_instId),cc_campaign_type=VALUES(cc_campaign_type),cc_campaign_code=VALUES(cc_campaign_code),cc_participated_time=VALUES(cc_participated_time),gravida=VALUES(gravida),longitude=VALUES(longitude),latitude=VALUES(latitude),trigger_time=VALUES(trigger_time),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyBogofCampaignRecord loyaltyBogofCampaignRecord);

    @Update("UPDATE loyalty_bogof_campaign_record set cellphone=#{cellphone},openid=#{openid},unionid=#{unionid},cc_campaign_id=#{ccCampaignId},cc_campaign_instId=#{ccCampaignInstId},cc_campaign_type=#{ccCampaignType},cc_campaign_code=#{ccCampaignCode},cc_participated_time=#{ccParticipatedTime},order_no=#{orderNo},gravida=#{gravida},longitude=#{longitude},latitude=#{latitude},trigger_time=#{triggerTime},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LoyaltyBogofCampaignRecord loyaltyBogofCampaignRecord);

    @Delete("DELETE FROM loyalty_bogof_campaign_record WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_bogof_campaign_record set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,cellphone,openid,unionid,cc_campaign_id,cc_campaign_instId,cc_campaign_type,cc_campaign_code,cc_participated_time,order_no,gravida,longitude,latitude,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_record WHERE id=#{id} ")
    @Results(id = "loyaltyBogofCampaignRecord-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "openid", column = "openid"),
      @Result(property = "unionid", column = "unionid"),
      @Result(property = "ccCampaignId", column = "cc_campaign_id"),
      @Result(property = "ccCampaignInstId", column = "cc_campaign_instId"),
      @Result(property = "ccCampaignType", column = "cc_campaign_type"),
      @Result(property = "ccCampaignCode", column = "cc_campaign_code"),
      @Result(property = "ccParticipatedTime", column = "cc_participated_time"),
      @Result(property = "orderNo", column = "order_no"),
      @Result(property = "gravida", column = "gravida"),
      @Result(property = "longitude", column = "longitude"),
      @Result(property = "latitude", column = "latitude"),
      @Result(property = "triggerTime", column = "trigger_time"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyBogofCampaignRecord getByIdEX(@Param("id") Long id);

    @Select("SELECT id,cellphone,openid,unionid,cc_campaign_id,cc_campaign_instId,cc_campaign_type,cc_campaign_code,cc_participated_time,order_no,gravida,longitude,latitude,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_record WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyBogofCampaignRecord-mapping")
    LoyaltyBogofCampaignRecord getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys
    @Select("SELECT id,cellphone,openid,unionid,cc_campaign_id,cc_campaign_instId,cc_campaign_type,cc_campaign_code,cc_participated_time,order_no,gravida,longitude,latitude,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_record WHERE order_no=#{orderNo} ")
    @ResultMap(value = "loyaltyBogofCampaignRecord-mapping")
    LoyaltyBogofCampaignRecord getByOrderNo(@Param("orderNo") String orderNo);



//update status sqls


//get data by foreign keys

}
