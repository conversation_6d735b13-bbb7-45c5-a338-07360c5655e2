package loyalty.share.lib.db.mapper;


import java.util.Date;

import loyalty.share.lib.db.model.PclassPnecContactWay;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface PclassPnecContactWayMapper  {

     @Insert("INSERT IGNORE INTO pclass_pnec_contact_way(id,user_id,config_id,qrcode_type,type,scene,qr_code,state,is_temp,skip_verify,raw_data,creator,updater,creator,updater)" +
                " VALUES(#{id},#{userId},#{configId},#{qrcodeType},#{type},#{scene},#{qrCode},#{state},#{isTemp},#{skipVerify},#{rawData},#{creator},#{updater},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(PclassPnecContactWay pclassPnecContactWay);

    @Insert("INSERT INTO pclass_pnec_contact_way(id,user_id,config_id,qrcode_type,type,scene,qr_code,state,is_temp,skip_verify,raw_data,creator,updater,creator,updater)" +
                " VALUES(#{id},#{userId},#{configId},#{qrcodeType},#{type},#{scene},#{qrCode},#{state},#{isTemp},#{skipVerify},#{rawData},#{creator},#{updater},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE qrcode_type=VALUES(qrcode_type),type=VALUES(type),scene=VALUES(scene),qr_code=VALUES(qr_code),state=VALUES(state),is_temp=VALUES(is_temp),skip_verify=VALUES(skip_verify),raw_data=VALUES(raw_data),updater=VALUES(updater),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(PclassPnecContactWay pclassPnecContactWay);

    @Update("UPDATE pclass_pnec_contact_way set user_id=#{userId},config_id=#{configId},qrcode_type=#{qrcodeType},type=#{type},scene=#{scene},qr_code=#{qrCode},state=#{state},is_temp=#{isTemp},skip_verify=#{skipVerify},raw_data=#{rawData},updater=#{updater},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(PclassPnecContactWay pclassPnecContactWay);

    @Delete("DELETE FROM pclass_pnec_contact_way WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update pclass_pnec_contact_way set is_deleted=true, updater=#{userKey}, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,user_id,config_id,qrcode_type,type,scene,qr_code,state,is_temp,skip_verify,raw_data,creator,updater,creator,updater,create_time,update_time FROM pclass_pnec_contact_way WHERE id=#{id} ")
    @Results(id = "pclassPnecContactWay-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "userId", column = "user_id"),
      @Result(property = "configId", column = "config_id"),
      @Result(property = "qrcodeType", column = "qrcode_type"),
      @Result(property = "type", column = "type"),
      @Result(property = "scene", column = "scene"),
      @Result(property = "qrCode", column = "qr_code"),
      @Result(property = "state", column = "state"),
      @Result(property = "isTemp", column = "is_temp"),
      @Result(property = "skipVerify", column = "skip_verify"),
      @Result(property = "rawData", column = "raw_data"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    PclassPnecContactWay getByIdEX(@Param("id") Long id);

    @Select("SELECT id,user_id,config_id,qrcode_type,type,scene,qr_code,state,is_temp,skip_verify,raw_data,creator,updater,creator,updater,create_time,update_time FROM pclass_pnec_contact_way WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "pclassPnecContactWay-mapping")
    PclassPnecContactWay getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys
    @Select("SELECT id,user_id,config_id,qrcode_type,type,scene,qr_code,state,is_temp,skip_verify,raw_data,creator,updater,creator,updater,create_time,update_time FROM pclass_pnec_contact_way WHERE user_id=#{userId} and config_id=#{configId} ")
    @ResultMap(value = "pclassPnecContactWay-mapping")
    PclassPnecContactWay getByUserIdConfigId(@Param("userId") String userId,@Param("configId") String configId);



//update status sqls


//get data by foreign keys

}
