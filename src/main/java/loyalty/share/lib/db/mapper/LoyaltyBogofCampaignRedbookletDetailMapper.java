package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyBogofCampaignRedbookletDetail;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;


@Mapper
public interface LoyaltyBogofCampaignRedbookletDetailMapper {

    @Insert("INSERT INTO loyalty_bogof_campaign_redbooklet_detail(id,loyalty_bogof_campaign_record_id,cc_campaign_instId,red_booklet_img_url,red_booklet_link,audit_status,trigger_time,creator,updater)" +
            " VALUES(#{id},#{loyaltyBogofCampaignRecordId},#{ccCampaignInstId},#{redBookletImgUrl},#{redBookletLink},#{auditStatus},#{triggerTime},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertEntity(LoyaltyBogofCampaignRedbookletDetail loyaltyBogofCampaignRedbookletDetail);

    @Update("UPDATE loyalty_bogof_campaign_redbooklet_detail SET loyalty_bogof_campaign_record_id = #{loyaltyBogofCampaignRecordId} WHERE id = #{id}")
    int updateLoyaltyBogofCampaignRecordIdById(@Param("loyaltyBogofCampaignRecordId")Long loyaltyBogofCampaignRecordId,@Param("id")Integer id);

    @Insert("INSERT INTO loyalty_bogof_campaign_redbooklet_detail(id,loyalty_bogof_campaign_record_id,cc_campaign_instId,red_booklet_img_url,red_booklet_link,audit_status,trigger_time,creator,updater)" +
            " VALUES(#{id},#{loyaltyBogofCampaignRecordId},#{ccCampaignInstId},#{redBookletImgUrl},#{redBookletLink},#{auditStatus},#{triggerTime},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE red_booklet_img_url=VALUES(red_booklet_img_url),audit_status=VALUES(audit_status),trigger_time=VALUES(trigger_time)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(LoyaltyBogofCampaignRedbookletDetail loyaltyBogofCampaignRedbookletDetail);

    @Select("SELECT id,loyalty_bogof_campaign_record_id,cc_campaign_instId,red_booklet_img_url,red_booklet_link,audit_status,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_redbooklet_detail WHERE cc_campaign_instId = #{campaignInstId}")
    @Results(id = "LoyaltyBogofCampaignRedbookletDetail-mapping",value = {
            @Result(property = "id",column = "id"),
            @Result(property = "loyaltyBogofCampaignRecordId",column = "loyalty_bogof_campaign_record_id"),
            @Result(property = "ccCampaignInstId",column = "cc_campaign_instId"),
            @Result(property = "redBookletImgUrl",column = "red_booklet_img_url"),
            @Result(property = "redBookletLink",column = "red_booklet_link"),
            @Result(property = "auditStatus",column = "audit_status"),
            @Result(property = "triggerTime",column = "trigger_time"),
            @Result(property = "creator",column = "creator"),
            @Result(property = "updater",column = "updater"),
            @Result(property = "createTime",column = "create_time"),
            @Result(property = "updateTime",column = "update_time")
        }
    )
    LoyaltyBogofCampaignRedbookletDetail getByCampaignInstId(@Param("campaignInstId")String campaignInstId);
}
