package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyWxmppSubscribeMsgInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltyWxmppSubscribeMsgInfoMapper {

     @Insert("INSERT IGNORE INTO loyalty_wxmpp_subscribe_msg_info(id,appid,cellphone,unionid,openid,event,template_id,subscribe_status,subscribe_time,creator,updater)" +
                " VALUES(#{id},#{appid},#{cellphone},#{unionid},#{openid},#{event},#{templateId},#{subscribeStatus},#{subscribeTime},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyWxmppSubscribeMsgInfo loyaltyWxmppSubscribeMsgInfo);

    @Insert("INSERT INTO loyalty_wxmpp_subscribe_msg_info(id,appid,cellphone,unionid,openid,event,template_id,subscribe_status,subscribe_time,creator,updater)" +
                " VALUES(#{id},#{appid},#{cellphone},#{unionid},#{openid},#{event},#{templateId},#{subscribeStatus},#{subscribeTime},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE appid=VALUES(appid),cellphone=VALUES(cellphone),unionid=VALUES(unionid),openid=VALUES(openid),event=VALUES(event),template_id=VALUES(template_id),subscribe_status=VALUES(subscribe_status),subscribe_time=VALUES(subscribe_time),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyWxmppSubscribeMsgInfo loyaltyWxmppSubscribeMsgInfo);

    @Update("UPDATE loyalty_wxmpp_subscribe_msg_info set appid=#{appid},cellphone=#{cellphone},unionid=#{unionid},openid=#{openid},event=#{event},template_id=#{templateId},subscribe_status=#{subscribeStatus},subscribe_time=#{subscribeTime},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LoyaltyWxmppSubscribeMsgInfo loyaltyWxmppSubscribeMsgInfo);

    @Delete("DELETE FROM loyalty_wxmpp_subscribe_msg_info WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_wxmpp_subscribe_msg_info set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,appid,cellphone,unionid,openid,event,template_id,subscribe_status,subscribe_time,creator,updater,create_time,update_time FROM loyalty_wxmpp_subscribe_msg_info WHERE id=#{id} ")
    @Results(id = "loyaltyWxmppSubscribeMsgInfo-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "appid", column = "appid"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "unionid", column = "unionid"),
      @Result(property = "openid", column = "openid"),
      @Result(property = "event", column = "event"),
      @Result(property = "templateId", column = "template_id"),
      @Result(property = "subscribeStatus", column = "subscribe_status"),
      @Result(property = "subscribeTime", column = "subscribe_time"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyWxmppSubscribeMsgInfo getByIdEX(@Param("id") Long id);

    @Select("SELECT id,appid,cellphone,unionid,openid,event,template_id,subscribe_status,subscribe_time,creator,updater,create_time,update_time FROM loyalty_wxmpp_subscribe_msg_info WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyWxmppSubscribeMsgInfo-mapping")
    LoyaltyWxmppSubscribeMsgInfo getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys


//update status sqls


//get data by foreign keys

}
