package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.*;
import org.apache.ibatis.annotations.*;

import java.util.List;


@Mapper
public interface LoyaltyMallCommodityMapper {

    @Select({"<script>" +
            "select *,case when ( (shelve_time is null or shelve_time &lt;=now()) and (off_shelve_time is null or off_shelve_time &gt;now())) then is_visible else FALSE END new_is_visible from ("+
            "SELECT loyalty_mall_commodity.*, " +
            "(select loyalty_mall_dict_cmmosupplier.corporation from loyalty_mall_dict_cmmosupplier where loyalty_mall_commodity.supplier_id=loyalty_mall_dict_cmmosupplier.id )corporation,"+
            "(select loyalty_mall_commodity_imgresource.img_file_name from loyalty_mall_commodity_imgresource where loyalty_mall_commodity.id=loyalty_mall_commodity_imgresource.commodity_id AND is_cover IS TRUE limit 1)img_file_name,"+
            "(select sum(accumultate_amount) - sum(current_available_amount) from loyalty_mall_commodity_storage where loyalty_mall_commodity_storage.commodity_id = loyalty_mall_commodity.id)+history_buy_quantity redemptionAmount,"+
            " ( " +
            "  select SUM(loyalty_mall_commodity_storage.accumultate_amount)from loyalty_mall_commodity_storage where loyalty_mall_commodity_storage.commodity_id = loyalty_mall_commodity.id " +
            " ) accumultate_amount, " +
            " ( " +
            "  select SUM(loyalty_mall_commodity_storage.current_available_amount)from loyalty_mall_commodity_storage where loyalty_mall_commodity_storage.commodity_id = loyalty_mall_commodity.id " +
            " ) current_available_amount, " +
            "(SELECT currency_details FROM loyalty_mall_commodity_price_detail WHERE loyalty_mall_commodity_price_detail.commodity_id=loyalty_mall_commodity.id ORDER BY rank_id LIMIT 1) AS currencyContents " +
            "FROM loyalty_mall_commodity " +
		    " INNER JOIN loyalty_mall_channeltag_resource ON loyalty_mall_commodity.id = loyalty_mall_channeltag_resource.resource_id and loyalty_mall_channeltag_resource.resource_type='COMMODITY'"+
            "WHERE loyalty_mall_commodity.is_visible IS TRUE" +
            " AND (shelve_time is null or shelve_time &lt;=now()) and (off_shelve_time is null or off_shelve_time &gt;=now()) "+
            " AND loyalty_mall_channeltag_resource.tag_id =#{tagId}  " +
		    " AND loyalty_mall_channeltag_resource.channel_id =#{channelId} " +
            "<if test='classes != null and classes !=&apos;&apos; '> and loyalty_mall_commodity.classes =#{classes} </if> ",
            "<if test='category != null and category !=&apos;&apos; '> and loyalty_mall_commodity.category =#{category} </if>",
            "<if test='notClasses != null and notClasses !=&apos;&apos; '> and loyalty_mall_commodity.classes !=#{notClasses} </if>",
            "<if test='key != null and key !=&apos;&apos; '> and loyalty_mall_commodity.commodity_name like '%${key}%' </if>",
            ")t where 1=1 order by current_available_amount = 0 asc"+
            "<if test='sortFiled != null and sortFiled !=&apos;&apos; '> ,  ${sortFiled} ${sortType} </if>",
            "<if test='sortFiled == null or sortFiled ==&apos;&apos; '>",
            " , t.sort desc,t.create_time desc ",
            "</if>",
            "</script>"})
    @Results(id = "resultCommodity",value = {
            @Result(column = "id",property = "id"),
            @Result(column = "commodity_code",property = "commodityCode"),
            @Result(column = "commodity_name",property = "commodityName"),
            @Result(column = "description",property = "description"),
            @Result(column = "new_is_visible",property = "isVisible"),
            @Result(column = "category",property = "category"),
            @Result(column = "classes",property = "classes"),
            @Result(column = "brand_name",property = "brandName"),
            @Result(column = "model",property = "model"),
            @Result(column = "market_price",property = "marketPrice"),
            @Result(column = "purchase_price",property = "purchasePrice"),
            @Result(column = "is_multi_size",property = "isMultiSize"),
            @Result(column = "is_pricing_by_memberlv",property = "isPricingByMemberlv"),
            @Result(column = "detail_content",property = "detailContent"),
            @Result(column = "supplier_id",property = "supplierId"),
            @Result(column = "supplier_name",property = "supplierName"),
            @Result(column = "supplier_commodity_code",property = "supplierCommodityCode"),
            @Result(column = "created_by_user",property = "createdByUser"),
            @Result(column = "updated_by_user",property = "updatedByUser"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time",property = "updateTime"),
            @Result(column = "img_file_name",property = "imgFileName"),
            @Result(column = "accumultate_amount",property = "accumultateAmount"),
            @Result(column = "current_available_amount",property = "currentAvailableAmount"),
            @Result(column = "currencyContents",property = "currencyContents"),
            @Result(column = "redemptionAmount",property = "redemptionAmount"),
            @Result(column = "virtual_type",property = "virtualType"),
            @Result(column = "history_buy_quantity",property = "historyBuyQuantity"),
            @Result(column = "shelve_time",property = "shelveTime"),
            @Result(column = "off_shelve_time",property = "offShelveTime"),
            @Result(column = "buy_limit_max",property = "buyLimitMax"),
    })
    List<DBCommodity> queryList(@Param("classes") String classes,@Param("key") String key,@Param("tagId")Long tagId,
                                @Param("channelId") String channelId,
                                @Param("category") String category,@Param("notClasses") String notClasses,
                                @Param("sortFiled") String sortFiled,@Param("sortType") String sortType);


    @Select({"<script>" +
            "select *,case when ( (shelve_time is null or shelve_time &lt;=now()) and (off_shelve_time is null or off_shelve_time &gt;now())) then is_visible else FALSE END new_is_visible from ("+
            "SELECT loyalty_mall_commodity.*, " +
            "(select loyalty_mall_dict_cmmosupplier.corporation from loyalty_mall_dict_cmmosupplier where loyalty_mall_commodity.supplier_id=loyalty_mall_dict_cmmosupplier.id )corporation,"+
            "(select loyalty_mall_commodity_imgresource.img_file_name from loyalty_mall_commodity_imgresource where loyalty_mall_commodity.id=loyalty_mall_commodity_imgresource.commodity_id AND is_cover IS TRUE limit 1)img_file_name,"+
            "(select sum(accumultate_amount) - sum(current_available_amount) from loyalty_mall_commodity_storage where loyalty_mall_commodity_storage.commodity_id = loyalty_mall_commodity.id)+history_buy_quantity redemptionAmount,"+
            " ( " +
            "  select SUM(loyalty_mall_commodity_storage.accumultate_amount)from loyalty_mall_commodity_storage where loyalty_mall_commodity_storage.commodity_id = loyalty_mall_commodity.id " +
            " ) accumultate_amount, " +
            " ( " +
            "  select SUM(loyalty_mall_commodity_storage.current_available_amount)from loyalty_mall_commodity_storage where loyalty_mall_commodity_storage.commodity_id = loyalty_mall_commodity.id " +
            " ) current_available_amount, " +
            "(SELECT currency_details FROM loyalty_mall_commodity_price_detail WHERE loyalty_mall_commodity_price_detail.commodity_id=loyalty_mall_commodity.id ORDER BY rank_id LIMIT 1) AS currencyContents " +
            "FROM loyalty_mall_commodity " +
            " INNER JOIN loyalty_mall_channeltag_resource ON loyalty_mall_commodity.id = loyalty_mall_channeltag_resource.resource_id and loyalty_mall_channeltag_resource.resource_type='COMMODITY'"+
            "WHERE loyalty_mall_commodity.is_visible IS TRUE" +
            " AND (shelve_time is null or shelve_time &lt;=now()) and (off_shelve_time is null or off_shelve_time &gt;=now()) "+
            " AND loyalty_mall_channeltag_resource.tag_id =#{tagId}  " +
            " AND loyalty_mall_channeltag_resource.channel_id =#{channelId} " +
            " and loyalty_mall_commodity.classes='campaign_special' ",
            "<if test='category != null and category !=&apos;&apos; '> and loyalty_mall_commodity.category =#{category} </if>",
            ")t where 1=1 "+
            " order by t.sort desc,t.create_time desc ",
            "</script>"})
    @ResultMap(value = "resultCommodity")
    List<DBCommodity> queryCampaignCommodityList(@Param("tagId")Long tagId,@Param("channelId") Long channelId, @Param("category") String category);


    @Select("select *,case when ( (shelve_time is null or shelve_time <=now()) and (off_shelve_time is null or off_shelve_time >=now())) then is_visible else FALSE END new_is_visible" +
            " from  loyalty_mall_commodity where id=#{id}")
    @ResultMap(value = "resultCommodity")
    DBCommodity queryById(@Param("id") int id);

    @Select("select *,case when ( (shelve_time is null or shelve_time <=now()) and (off_shelve_time is null or off_shelve_time >=now())) then is_visible else FALSE END new_is_visible" +
            " from  loyalty_mall_commodity where commodity_code=#{code}")
    @ResultMap(value = "resultCommodity")
    DBCommodity queryByCode(@Param("code") String code);

    @Select("select loyalty_mall_commodity.classes from  loyalty_mall_commodity " +
		    " INNER JOIN loyalty_mall_channeltag_resource ON loyalty_mall_commodity.id = loyalty_mall_channeltag_resource.resource_id and loyalty_mall_channeltag_resource.resource_type='COMMODITY'" +
		    " where is_visible = 1 and loyalty_mall_channeltag_resource.channel_id =#{channelId} " +
            "  AND loyalty_mall_channeltag_resource.tag_id =#{tagId}  GROUP BY classes ORDER BY MIN(loyalty_mall_commodity.create_time)")
    List<String> queryCommodityClasses(@Param("channelId") String channelId, @Param("tagId")Long tagId);

    @Select("select category from  loyalty_mall_commodity " +
		    " INNER JOIN loyalty_mall_channeltag_resource ON loyalty_mall_commodity.id = loyalty_mall_channeltag_resource.resource_id and loyalty_mall_channeltag_resource.resource_type='COMMODITY'" +
		    " where is_visible = 1 and  loyalty_mall_channeltag_resource.channel_id =#{channelId} " +
            "  AND loyalty_mall_channeltag_resource.tag_id =#{tagId}  GROUP BY category")
    List<String> queryCommodityCategory(@Param("channelId") String channelId, @Param("tagId")Long tagId);

    @Select("select * from  loyalty_mall_commodity_sizekv where commodity_id=#{commodity_id} order by key_sequency,value_sequency asc")
    @Results(id = "resultCommoditySize",value = {
            @Result(column = "id",property = "id"),
            @Result(column = "commodity_id",property = "commodityId"),
            @Result(column = "option_key",property = "optionKey"),
            @Result(column = "option_value",property = "optionValue"),
            @Result(column = "key_sequency",property = "keySequency"),
            @Result(column = "value_sequency",property = "valueSequency"),
            @Result(column = "created_by_user",property = "createdByUser"),
            @Result(column = "updated_by_user",property = "updatedByUser"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time",property = "updateTime"),
    })
    List<DBCommoditySize> querySizeByCommodityId(@Param("commodity_id") int commodityId);

    @Select("select *," +
            " (accumultate_amount-current_available_amount)redemptionAmount " +
            " from  loyalty_mall_commodity_storage where commodity_id=#{commodity_id}")
    @Results(id = "resultCommodityStorage",value = {
            @Result(column = "id",property = "id"),
            @Result(column = "commodity_id",property = "commodityId"),
            @Result(column = "size_comb_id",property = "sizeCombId"),
            @Result(column = "accumultate_amount",property = "accumultateAmount"),
            @Result(column = "current_available_amount",property = "currentAvailableAmount"),
            @Result(column = "redemptionAmount",property = "redemptionAmount"),
            @Result(column = "safe_storage",property = "safeStorage"),
            @Result(column = "last_update_by_orderid",property = "lastUpdateByOrderId"),
            @Result(column = "last_update_event",property = "lastUpdateEvent"),
            @Result(column = "creator",property = "createdByUser"),
            @Result(column = "updator",property = "updatedByUser"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time",property = "updateTime"),
    })
    List<DBCommodityStorage> queryStorageByCommodityId(@Param("commodity_id") int commodityId);

    @Select("select *from  loyalty_mall_commo_size_combine where commodity_id=#{commodity_id}")
    @Results(id = "resultCommoditySizeCombine",value = {
            @Result(column = "id",property = "id"),
            @Result(column = "commodity_id",property = "commodityId"),
            @Result(column = "combined_kv_ids",property = "combinedKvIds"),
            @Result(column = "combined_desc",property = "combinedDesc"),
            @Result(column = "created_by_user",property = "createdByUser"),
            @Result(column = "updated_by_user",property = "updatedByUser"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time",property = "updateTime"),
    })
    List<DBCommoditySizeCombine> querySizeCombineByCommodityId(@Param("commodity_id") int commodityId);

    @Select("select *from  loyalty_mall_commodity_price_detail where commodity_id=#{commodity_id} order by rank_id")
    @Results(id = "resultCommodityPrice",value = {
            @Result(column = "id",property = "id"),
            @Result(column = "commodity_id",property = "commodityId"),
            @Result(column = "rank_id",property = "rankId"),
            @Result(column = "currency_details",property = "currencyDetails"),
            @Result(column = "sequency",property = "sequency"),
            @Result(column = "created_by_user",property = "createdByUser"),
            @Result(column = "updated_by_user",property = "updatedByUser"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time",property = "updateTime"),
    })
    List<DBCommodityPrice> queryPriceByCommodityId(@Param("commodity_id") int commodityId);

    @Select("select *from  loyalty_mall_commodity_imgresource where commodity_id=#{commodity_id} order by sequency asc")
    @Results(id = "resultCommodityImgResource",value = {
            @Result(column = "id",property = "id"),
            @Result(column = "commodity_id",property = "commodityId"),
            @Result(column = "is_cover",property = "isCover"),
            @Result(column = "img_file_name",property = "imgFileName"),
            @Result(column = "sequency",property = "sequency"),
            @Result(column = "created_by_user",property = "createdByUser"),
            @Result(column = "updated_by_user",property = "updatedByUser"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time",property = "updateTime"),
    })
    List<DBCommodityImgResource> queryImgResourceByCommodityId(@Param("commodity_id") int commodityId);
}
