package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyBogofCampaignProductScan;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltyBogofCampaignProductScanMapper {

     @Insert("INSERT IGNORE INTO loyalty_bogof_campaign_product_scan(id,cc_campaign_id,cc_campaign_instId,cellphone,digitcode,gravida,traceability_code,product_sapid,product_name,has_qualification,trigger_time,creator,updater)" +
                " VALUES(#{id},#{ccCampaignId},#{ccCampaignInstid},#{cellphone},#{digitcode},#{gravida},#{traceabilityCode},#{productSapid},#{productName},#{hasQualification},#{triggerTime},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyBogofCampaignProductScan loyaltyBogofCampaignProductScan);

    @Insert("INSERT INTO loyalty_bogof_campaign_product_scan(id,cc_campaign_id,cc_campaign_instId,cellphone,digitcode,gravida,traceability_code,product_sapid,product_name,has_qualification,trigger_time,creator,updater)" +
                " VALUES(#{id},#{ccCampaignId},#{ccCampaignInstid},#{cellphone},#{digitcode},#{gravida},#{traceabilityCode},#{productSapid},#{productName},#{hasQualification},#{triggerTime},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE cc_campaign_id=VALUES(cc_campaign_id),cc_campaign_instId=VALUES(cc_campaign_instId),cellphone=VALUES(cellphone),digitcode=VALUES(digitcode),gravida=VALUES(gravida),traceability_code=VALUES(traceability_code),product_sapid=VALUES(product_sapid),product_name=VALUES(product_name),has_qualification=VALUES(has_qualification),trigger_time=VALUES(trigger_time),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyBogofCampaignProductScan loyaltyBogofCampaignProductScan);

    @Update("UPDATE loyalty_bogof_campaign_product_scan set cc_campaign_id=#{ccCampaignId},cc_campaign_instId=#{ccCampaignInstid},cellphone=#{cellphone},digitcode=#{digitcode},gravida=#{gravida},traceability_code=#{traceabilityCode},product_sapid=#{productSapid},product_name=#{productName},has_qualification=#{hasQualification},trigger_time=#{triggerTime},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LoyaltyBogofCampaignProductScan loyaltyBogofCampaignProductScan);

    @Delete("DELETE FROM loyalty_bogof_campaign_product_scan WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_bogof_campaign_product_scan set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,cc_campaign_id,cc_campaign_instId,cellphone,digitcode,gravida,traceability_code,product_sapid,product_name,has_qualification,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_product_scan WHERE id=#{id} ")
    @Results(id = "loyaltyBogofCampaignProductScan-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "ccCampaignId", column = "cc_campaign_id"),
      @Result(property = "ccCampaignInstid", column = "cc_campaign_instId"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "digitcode", column = "digitcode"),
      @Result(property = "gravida", column = "gravida"),
      @Result(property = "traceabilityCode", column = "traceability_code"),
      @Result(property = "productSapid", column = "product_sapid"),
      @Result(property = "productName", column = "product_name"),
      @Result(property = "hasQualification", column = "has_qualification"),
      @Result(property = "triggerTime", column = "trigger_time"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyBogofCampaignProductScan getByIdEX(@Param("id") Long id);

    @Select("SELECT id,cc_campaign_id,cc_campaign_instId,cellphone,digitcode,gravida,traceability_code,product_sapid,product_name,has_qualification,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_product_scan WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyBogofCampaignProductScan-mapping")
    LoyaltyBogofCampaignProductScan getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys


//update status sqls


//get data by foreign keys

}
