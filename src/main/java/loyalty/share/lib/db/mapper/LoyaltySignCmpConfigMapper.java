package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltySignCmpConfig;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltySignCmpConfigMapper {

    @Update("UPDATE loyalty_v2_signcmp_config SET campaign_id=#{campaignId},sign_option=#{signOption},is_consist=#{isConsist},is_cycle=#{isCycle},updated_by_user=#{updatedByUser},sign_days=#{sign_days} WHERE ID=#{id}")
    void updateLoyaltySignCmpConfig(LoyaltySignCmpConfig model);


    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("INSERT INTO loyalty_v2_signcmp_config(campaign_id,sign_option,is_consist,is_cycle,created_by_user,updated_by_user) VALUES " +
            "(#{campaignId},#{signOption},#{isConsist},#{isCycle},#{createdByUser},#{updatedByUser},#{signDays}) ")
    int addLoyaltySignCmpConfig(LoyaltySignCmpConfig model);

    @Select("select * from loyalty_v2_signcmp_config where id =#{id}")
    @Results(id="LoyaltySignCmpConfigMap", value = {
            @Result(column = "id",property = "id"),
            @Result(column = "campaign_id",property = "campaignId"),
            @Result(column = "sign_option",property = "signOption"),
            @Result(column = "sign_days",property = "signDays"),
            @Result(column = "is_consist",property = "isConsist"),
            @Result(column = "is_cycle",property = "isCycle"),
            @Result(column = "created_by_user",property = "createdByUser"),
            @Result(column = "updated_by_user",property = "updatedByUser"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time",property = "updateTime"),
            @Result(column = "campaign_id",
                    property = "campaign",
                    one = @One(select="loyalty.share.lib.db.mapper.CampaignMapper.getCampaignById")
            ),

    })
    LoyaltySignCmpConfig getById(@Param("id") Long id);

    @Select("select * from loyalty_v2_signcmp_config where campaign_id =#{campaignId} limit 1")
    @Results( value = {
            @Result(column = "id",property = "id"),
            @Result(column = "campaign_id",property = "campaignId"),
            @Result(column = "sign_option",property = "signOption"),
            @Result(column = "sign_days",property = "signDays"),
            @Result(column = "is_consist",property = "isConsist"),
            @Result(column = "is_cycle",property = "isCycle"),
            @Result(column = "created_by_user",property = "createdByUser"),
            @Result(column = "updated_by_user",property = "updatedByUser"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time", property = "updateTime")
            /*,
            @Result(column = "campaign_id",
                    property = "campaign",
                    one = @One(select="loyalty.share.lib.db.mapper.CampaignMapper.getCampaignById")
            ),*/

    })
    LoyaltySignCmpConfig getByCampaignId(@Param("campaignId") Long campaignId);


    @Delete("delete from loyalty_v2_signcmp_config where id =#{id}")
    int deleteById(@Param("id")Long id);




}
