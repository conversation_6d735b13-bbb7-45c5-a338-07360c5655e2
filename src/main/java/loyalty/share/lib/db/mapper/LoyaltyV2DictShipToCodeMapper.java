package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyMallDictChanneltag;
import loyalty.share.lib.db.model.LoyaltyV2DictShipToCode;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyV2DictShipToCodeMapper {

    @Insert("INSERT INTO loyalty_v2_dict_shiptocode(id,shiptocode,owner_code,distributor_name,creator,updator)" +
            " VALUES(#{id},#{shipToCode},#{ownerCode},#{distributorName},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2DictShipToCode loyaltyV2DictShipToCode);

    @Insert("INSERT INTO loyalty_v2_dict_shiptocode(id,shiptocode,owner_code,distributor_name,creator,updator)" +
		    " VALUES(#{id},#{shipToCode},#{ownerCode},#{distributorName},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyV2DictShipToCode loyaltyV2DictShipToCode);

    @Insert("INSERT INTO loyalty_v2_dict_shiptocode(id,shiptocode,owner_code,distributor_name,creator,updator)" +
            " VALUES(#{id},#{shipToCode},#{ownerCode},#{distributorName},#{creator},#{updator})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),shiptocode=VALUES(shiptocode),owner_code=VALUES(owner_code),distributor_name=VALUES(distributor_name),updator=VALUES(updator)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyV2DictShipToCode loyaltyV2DictShipToCode);

    @Update("UPDATE loyalty_v2_dict_shiptocode set id=#{id},shiptocode=#{shipToCode},owner_code=#{ownerCode},distributor_name=#{distributorName},updator=#{updator} WHERE id=#{id}")
    int updateById(LoyaltyV2DictShipToCode loyaltyV2DictShipToCode);

    @Delete("DELETE FROM loyalty_v2_dict_shiptocode WHERE id=#{id}")
    int deleteById(LoyaltyV2DictShipToCode loyaltyV2DictShipToCode);


    @Select("SELECT * FROM loyalty_v2_dict_shiptocode WHERE id=#{id} ")
    @Results(id = "LoyaltyV2DictShipToCode-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "shipToCode", column = "shiptocode"), @Result(property = "ownerCode", column = "owner_code"), @Result(property = "distributorName", column = "distributor_name"), @Result(property = "creator", column = "creator"),@Result(property = "updator", column = "updator"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyV2DictShipToCode getById(@Param("id") Long id);

	@Select("SELECT * FROM loyalty_v2_dict_shiptocode where shiptocode=#{shipToCode} limit 1 ")
	@ResultMap({"LoyaltyV2DictShipToCode-mapping"})
    LoyaltyV2DictShipToCode queryByShipToCode(@Param("shipToCode")String shipToCode);
}
