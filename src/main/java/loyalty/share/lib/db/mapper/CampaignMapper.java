package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.Campaign;
import loyalty.share.lib.db.model.CampaignGiftRelationship;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

@Mapper
public interface CampaignMapper {

    @Select("select * from campaign where id= #{id}")
    @Results(id = "campaign-mapping", value = {
            @Result(property = "campaignName", column = "campaign_name"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "campaignSeries", column = "campaign_series"),
            @Result(property = "checkProperties", column = "check_properties"),
            @Result(property = "quotaDimension", column = "quota_dimension"),
            @Result(property = "disQuotaSenario", column = "dis_quota_senario"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "isDeleted", column = "is_deleted"),
            @Result(property = "campaignSenario", column = "campaign_senario"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    Campaign getCampaignById(@Param("id") Long id);

    @Select("select * from campaign where platform=#{platform} order by id asc")
    @ResultMap(value = "campaign-mapping")
    List<Campaign> findAllCampaignByPlatform(@Param("platform") String platform);

    @Select("select * from campaign where platform=#{platform} and campaign_name=#{campaignName} order by id desc")
    @ResultMap(value = "campaign-mapping")
    Campaign findAllCampaignByCampaignNamePlatform(@Param("campaignName") String campaignName, @Param("platform") String platform);

    @Select("select * from campaign where platform=#{platform} and campaign_series=#{series} order by id desc")
    @ResultMap(value = "campaign-mapping")
    List<Campaign> findAllCampaignByPlatformSeries(@Param("platform") String platform, @Param("series") String series);

    @Select("select * from campaign where platform=#{platform} and is_enabled=true and is_deleted=false and (start_time is null or start_time <=now()) and (end_time is null or end_time>=now()) order by id asc")
    @ResultMap(value = "campaign-mapping")
    List<Campaign> findAllValidCampaign(@Param("platform") String platform);

    @Select("select * from campaign where platform=#{platform} and campaign_series=#{series} and is_enabled=true and is_deleted=false and (start_time is null or start_time <=#{campaignTime}) and (end_time is null or end_time>=#{campaignTime}) order by id asc")
    @ResultMap(value = "campaign-mapping")
    List<Campaign> findAllValidCampaignByPlatformSeries(@Param("platform") String platform, @Param("series") String series, @Param("campaignTime") Date campaignTime);

    @Select("SELECT * FROM campaign WHERE campaign_name = #{campaignName}")
    @ResultMap(value = "campaign-mapping")
    Campaign findCampaignByCampaignName(@Param("campaignName") String campaignName);


    @Select("SELECT c.id,c.campaign_name ,CONCAT(c.PLATFORM,'-',c.DESCRIPTION) AS DESCRIPTION " +
            "FROM campaign_gift_relationship cgr INNER JOIN campaign c ON cgr.campaign_id=c.id " +
            "WHERE c.start_time< now() AND c.end_time> now() AND c.is_enabled=true and is_deleted=false  and  cgr.gift_quota_dimension = #{dimension} " +
            "GROUP BY c.id ")
    @ResultMap(value = "campaign-mapping")
    List<Campaign> findCampaignByDimension(@Param("dimension") String dimension);


    @Select("select * FROM campaign_gift_relationship WHERE campaign_id=#{campaignId}  and gift_id=#{giftId}")
    @Results(id = "relationship-map", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "giftId", column = "gift_id"),
            @Result(property = "campaignId", column = "campaign_id"),
            @Result(property = "giftQuotaDimension", column = "gift_quota_dimension"),
            @Result(property = "giftCount", column = "gift_number"),
            @Result(property = "quotaGiftId", column = "quota_gift_id")
    })
    List<CampaignGiftRelationship> findCampaingGiftRelationship(@Param("giftId") Long giftId, @Param("campaignId") Long campaignId);

    @Select("select * FROM campaign_gift_relationship WHERE id=#{id}  and gift_id=#{giftId}")
    @ResultMap(value = "relationship-map")
    CampaignGiftRelationship findCampaingGiftRelationshipById(@Param("id") Long id);


}