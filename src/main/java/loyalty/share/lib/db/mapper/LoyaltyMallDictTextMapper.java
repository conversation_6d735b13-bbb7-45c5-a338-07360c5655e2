package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyMallDictText;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyMallDictTextMapper {

    @Insert("INSERT INTO loyalty_mall_dict_text(id,channel_id,text_area,int_code,ext_code,ext_desc,int_desc,priority,is_enabled)" +
            " VALUES(#{id},#{channelId},#{textArea},#{intCode},#{extCode},#{extDesc},#{intDesc},#{priority},#{isEnabled})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyMallDictText loyaltyMallDictText);

    @Insert("INSERT IGNORE INTO loyalty_mall_dict_text(id,channel_id,text_area,int_code,ext_code,ext_desc,int_desc,priority,is_enabled)" +
            " VALUES(#{id},#{channelId},#{textArea},#{intCode},#{extCode},#{extDesc},#{intDesc},#{priority},#{isEnabled})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyMallDictText loyaltyMallDictText);

    @Insert("INSERT INTO loyalty_mall_dict_text(id,channel_id,text_area,int_code,ext_code,ext_desc,int_desc,priority,is_enabled)" +
            " VALUES(#{id},#{channelId},#{textArea},#{intCode},#{extCode},#{extDesc},#{intDesc},#{priority},#{isEnabled})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),channel_id=VALUES(channel_id),text_area=VALUES(text_area),int_code=VALUES(int_code),ext_code=VALUES(ext_code),ext_desc=VALUES(ext_desc),int_desc=VALUES(int_desc),priority=VALUES(priority),is_enabled=VALUES(is_enabled)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyMallDictText loyaltyMallDictText);

    @Update("UPDATE loyalty_mall_dict_text set id=#{id},channel_id=#{channelId},text_area=#{textArea},int_code=#{intCode},ext_code=#{extCode},ext_desc=#{extDesc},int_desc=#{intDesc},priority=#{priority},is_enabled=#{isEnabled} WHERE id=#{id}")
    int updateById(LoyaltyMallDictText loyaltyMallDictText);

    @Delete("DELETE FROM loyalty_mall_dict_text WHERE id=#{id}")
    int deleteById(LoyaltyMallDictText loyaltyMallDictText);


    @Select("SELECT * FROM loyalty_mall_dict_text WHERE id=#{id} ")
    @Results(id = "loyaltyMallDictText-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "channelId", column = "channel_id"), @Result(property = "textArea", column = "text_area"), @Result(property = "intCode", column = "int_code"), @Result(property = "extCode", column = "ext_code"), @Result(property = "extDesc", column = "ext_desc"), @Result(property = "intDesc", column = "int_desc"), @Result(property = "priority", column = "priority"), @Result(property = "isEnabled", column = "is_enabled"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyMallDictText getById(@Param("id") Long id);


    @Select({"SELECT * FROM loyalty_mall_dict_text WHERE is_enabled= true "})
    @ResultMap(value = "loyaltyMallDictText-mapping")
    List<LoyaltyMallDictText> getAllEnabledText();

}
