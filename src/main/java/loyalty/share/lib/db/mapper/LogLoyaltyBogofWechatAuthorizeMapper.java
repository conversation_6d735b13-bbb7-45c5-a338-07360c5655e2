package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LogLoyaltyBogofWechatAuthorize;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LogLoyaltyBogofWechatAuthorizeMapper {

     @Insert("INSERT IGNORE INTO log_loyalty_bogof_wechat_authorize(id,cellphone,cc_campaign_id,openid,unionid,auth_type,is_auth,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{ccCampaignId},#{openid},#{unionid},#{authType},#{isAuth},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LogLoyaltyBogofWechatAuthorize logLoyaltyBogofWechatAuthorize);

    @Insert("INSERT INTO log_loyalty_bogof_wechat_authorize(id,cellphone,cc_campaign_id,openid,unionid,auth_type,is_auth,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{ccCampaignId},#{openid},#{unionid},#{authType},#{isAuth},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE cellphone=VALUES(cellphone),cc_campaign_id=VALUES(cc_campaign_id),openid=VALUES(openid),unionid=VALUES(unionid),auth_type=VALUES(auth_type),is_auth=VALUES(is_auth),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LogLoyaltyBogofWechatAuthorize logLoyaltyBogofWechatAuthorize);

    @Update("UPDATE log_loyalty_bogof_wechat_authorize set cellphone=#{cellphone},cc_campaign_id=#{ccCampaignId},openid=#{openid},unionid=#{unionid},auth_type=#{authType},is_auth=#{isAuth},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LogLoyaltyBogofWechatAuthorize logLoyaltyBogofWechatAuthorize);

    @Delete("DELETE FROM log_loyalty_bogof_wechat_authorize WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update log_loyalty_bogof_wechat_authorize set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,cellphone,cc_campaign_id,openid,unionid,auth_type,is_auth,creator,updater,create_time,update_time FROM log_loyalty_bogof_wechat_authorize WHERE id=#{id} ")
    @Results(id = "logLoyaltyBogofWechatAuthorize-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "ccCampaignId", column = "cc_campaign_id"),
      @Result(property = "openid", column = "openid"),
      @Result(property = "unionid", column = "unionid"),
      @Result(property = "authType", column = "auth_type"),
      @Result(property = "isAuth", column = "is_auth"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LogLoyaltyBogofWechatAuthorize getByIdEX(@Param("id") Long id);

    @Select("SELECT id,cellphone,cc_campaign_id,openid,unionid,auth_type,is_auth,creator,updater,create_time,update_time FROM log_loyalty_bogof_wechat_authorize WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "logLoyaltyBogofWechatAuthorize-mapping")
    LogLoyaltyBogofWechatAuthorize getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys


//update status sqls
    @Update("update log_loyalty_bogof_wechat_authorize set is_auth=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsAuth( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater );




//get data by foreign keys

}
