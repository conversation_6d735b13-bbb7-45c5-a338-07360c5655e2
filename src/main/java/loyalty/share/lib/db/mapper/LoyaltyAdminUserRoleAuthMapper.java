package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyAdminUserRoleAuth;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface LoyaltyAdminUserRoleAuthMapper {

    @Insert("INSERT INTO loyalty_admin_user_role_auth(id,role_id,function_id,authorized_by_user)" +
            " VALUES(#{id},#{roleId},#{functionId},#{authorizedByUser})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyAdminUserRoleAuth loyaltyAdminUserRoleAuth);

     @Insert("INSERT IGNORE INTO loyalty_admin_user_role_auth(id,role_id,function_id,authorized_by_user)" +
                " VALUES(#{id},#{roleId},#{functionId},#{authorizedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyAdminUserRoleAuth loyaltyAdminUserRoleAuth);

    @Insert("INSERT INTO loyalty_admin_user_role_auth(id,role_id,function_id,authorized_by_user)" +
                " VALUES(#{id},#{roleId},#{functionId},#{authorizedByUser})" +
                " ON DUPLICATE KEY UPDATE id=VALUES(id),role_id=VALUES(role_id),function_id=VALUES(function_id),authorized_by_user=VALUES(authorized_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyAdminUserRoleAuth loyaltyAdminUserRoleAuth);

    @Update("UPDATE loyalty_admin_user_role_auth set id=#{id},role_id=#{roleId},function_id=#{functionId},authorized_by_user=#{authorizedByUser} WHERE id=#{id}" )
    int updateById(LoyaltyAdminUserRoleAuth loyaltyAdminUserRoleAuth);

    @Delete("DELETE FROM loyalty_admin_user_role_auth WHERE id=#{id}" )
    int deleteById(LoyaltyAdminUserRoleAuth loyaltyAdminUserRoleAuth);


    @Select("SELECT * FROM loyalty_admin_user_role_auth WHERE id=#{id} ")
    @Results(id = "loyaltyAdminUserRoleAuth-mapping", value = {
    @Result(property = "id", column = "id"),@Result(property = "roleId", column = "role_id"),@Result(property = "functionId", column = "function_id"),@Result(property = "authorizedByUser", column = "authorized_by_user"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time")
    })
    LoyaltyAdminUserRoleAuth getById(@Param("id") Long id);
    /*@Insert("insert into loyalty_admin_user_role_auth_yang(menu_id,role_id) values(#{menuId},#{roleId})")
    public int insert(@Param("roleId") Long roleId, @Param("menuId") Long menuId);

    @Delete("delete from loyalty_admin_user_role_auth_yang where role_id=#{roleId}")
    public int delete(@Param("roleId") Long roleId);*/

    @Delete("delete from loyalty_admin_user_role_auth where role_id=#{roleId}")
    public int delete(@Param("roleId") Long roleId);

    @Select("select t2.component from loyalty_admin_user_role_auth AS t1 LEFT JOIN loyalty_admin_user_function AS t2 ON t2.id = t1.function_id where role_id=#{roleId}")
    public List<String> getMenuListByRole(Map params);

    @Select("select r.name from loyalty_admin_user_role r left join loyalty_admin_user_auth ru on r.id=ru.role_id where ru.user_id=#{userId} " +
            "union all " +
            "select  m.permission  from loyalty_admin_user_role r left join loyalty_admin_user_auth ru on r.id=ru.role_id  left join loyalty_admin_user_role_auth rm on ru.role_id=rm.role_id left join loyalty_admin_user_function m on rm.menu_id=m.id where ru.user_id=#{userId} and m.permission is not null")
    List<String> getRolePermission(@Param("userId") Long userId);
}
