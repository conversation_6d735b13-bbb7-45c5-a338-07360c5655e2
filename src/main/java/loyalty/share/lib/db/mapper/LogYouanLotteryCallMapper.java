package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LogYouanLotteryCall;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;

/**
 * @description 佑安增加抽奖次数记录表Mapper
 * <AUTHOR>
 * @date 2022-02-25
 */
@Mapper
public interface LogYouanLotteryCallMapper {


    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into log_youan_lottery_call" +
            " (cellphone,req_successed,req_path,req_body,result_body,req_id,caller)" +
            " values(#{cellphone},#{reqSucceed},#{reqPath},#{reqBody},#{resultBody},#{reqId},#{caller})")
    Integer insert(LogYouanLotteryCall logYouanLotteryCall);

}