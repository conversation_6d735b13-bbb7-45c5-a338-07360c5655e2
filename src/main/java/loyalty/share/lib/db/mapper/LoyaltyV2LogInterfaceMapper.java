package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2LogInterface;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyV2LogInterfaceMapper {

    @Insert("INSERT INTO loyalty_v2_log_interface(id,platform,campaign_id, campaign_name, channel_id,cellphone,product_info,result_code,request_path,request_body,result_body,sender)" +
            " VALUES(#{id},#{platform},#{campaignId}, #{campaignName}, #{channelId},#{cellphone},#{productInfo},#{resultCode},#{requestPath},#{requestBody},#{resultBody},#{sender})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2LogInterface loyaltyV2LogInterface);

    @Insert("INSERT IGNORE INTO loyalty_v2_log_interface(id,platform,campaign_id, campaign_name,channel_id,cellphone,product_info,result_code,request_path,request_body,result_body,sender)" +
            " VALUES(#{id},#{platform},#{campaignId}, #{campaignName} ,#{channelId},#{cellphone},#{productInfo},#{resultCode},#{requestPath},#{requestBody},#{resultBody},#{sender})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyV2LogInterface loyaltyV2LogInterface);

    @Insert("INSERT INTO loyalty_v2_log_interface(id,platform,campaign_id, campaign_name,channel_id,cellphone,product_info,result_code,request_path,request_body,result_body,sender)" +
            " VALUES(#{id},#{platform},#{campaignId}, #{campaignName} ,#{channelId},#{cellphone},#{productInfo},#{resultCode},#{requestPath},#{requestBody},#{resultBody},#{sender})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),platform=VALUES(platform),campaign_id=VALUES(campaign_id),channel_id=VALUES(channel_id),cellphone=VALUES(cellphone),product_info=VALUES(product_info),result_code=VALUES(result_code),request_path=VALUES(request_path),request_body=VALUES(request_body),result_body=VALUES(result_body),sender=VALUES(sender)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyV2LogInterface loyaltyV2LogInterface);

    @Update("UPDATE loyalty_v2_log_interface set id=#{id},platform=#{platform},campaign_id=#{campaignId},channel_id=#{channelId},cellphone=#{cellphone},product_info=#{productInfo},result_code=#{resultCode},request_path=#{requestPath},request_body=#{requestBody},result_body=#{resultBody},sender=#{sender} WHERE id=#{id}")
    int updateById(LoyaltyV2LogInterface loyaltyV2LogInterface);

    @Delete("DELETE FROM loyalty_v2_log_interface WHERE id=#{id}")
    int deleteById(LoyaltyV2LogInterface loyaltyV2LogInterface);


    @Select("SELECT * FROM loyalty_v2_log_interface WHERE id=#{id} ")
    @Results(id = "loyaltyV2LogInterface-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "platform", column = "platform"), @Result(property = "campaignId", column = "campaign_id"), @Result(property = "campaignName", column = "campaign_name"), @Result(property = "channelId", column = "channel_id"), @Result(property = "cellphone", column = "cellphone"), @Result(property = "productInfo", column = "product_info"), @Result(property = "resultCode", column = "result_code"), @Result(property = "requestPath", column = "request_path"), @Result(property = "requestBody", column = "request_body"), @Result(property = "resultBody", column = "result_body"), @Result(property = "sender", column = "sender"), @Result(property = "createTime", column = "create_time")
    })
    LoyaltyV2LogInterface getById(@Param("id") Long id);
}
