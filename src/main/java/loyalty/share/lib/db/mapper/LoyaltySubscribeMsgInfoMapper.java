package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltySubscribeMsgInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltySubscribeMsgInfoMapper  {

     @Insert("INSERT IGNORE INTO loyalty_subscribe_msg_info(id,cellphone,unionid,openid,source_table,source_id,campaign_id,wxmpp_subscribe_msg_info_id,is_send,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{unionid},#{openid},#{sourceTable},#{sourceId},#{campaignId},#{wxmppSubscribeMsgInfoId},#{isSend},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltySubscribeMsgInfo loyaltySubscribeMsgInfo);

    @Insert("INSERT INTO loyalty_subscribe_msg_info(id,cellphone,unionid,openid,source_table,source_id,campaign_id,wxmpp_subscribe_msg_info_id,is_send,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{unionid},#{openid},#{sourceTable},#{sourceId},#{campaignId},#{wxmppSubscribeMsgInfoId},#{isSend},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE cellphone=VALUES(cellphone),unionid=VALUES(unionid),openid=VALUES(openid),source_table=VALUES(source_table),source_id=VALUES(source_id),campaign_id=VALUES(campaign_id),wxmpp_subscribe_msg_info_id=VALUES(wxmpp_subscribe_msg_info_id),is_send=VALUES(is_send),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltySubscribeMsgInfo loyaltySubscribeMsgInfo);

    @Update("UPDATE loyalty_subscribe_msg_info set cellphone=#{cellphone},unionid=#{unionid},openid=#{openid},source_table=#{sourceTable},source_id=#{sourceId},campaign_id=#{campaignId},wxmpp_subscribe_msg_info_id=#{wxmppSubscribeMsgInfoId},is_send=#{isSend},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LoyaltySubscribeMsgInfo loyaltySubscribeMsgInfo);

    @Delete("DELETE FROM loyalty_subscribe_msg_info WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_subscribe_msg_info set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,cellphone,unionid,openid,source_table,source_id,campaign_id,wxmpp_subscribe_msg_info_id,is_send,creator,updater,create_time,update_time FROM loyalty_subscribe_msg_info WHERE id=#{id} ")
    @Results(id = "loyaltySubscribeMsgInfo-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "unionid", column = "unionid"),
      @Result(property = "openid", column = "openid"),
      @Result(property = "sourceTable", column = "source_table"),
      @Result(property = "sourceId", column = "source_id"),
      @Result(property = "campaignId", column = "campaign_id"),
      @Result(property = "wxmppSubscribeMsgInfoId", column = "wxmpp_subscribe_msg_info_id"),
      @Result(property = "isSend", column = "is_send"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltySubscribeMsgInfo getByIdEX(@Param("id") Long id);

    @Select("SELECT id,cellphone,unionid,openid,source_table,source_id,campaign_id,wxmpp_subscribe_msg_info_id,is_send,creator,updater,create_time,update_time FROM loyalty_subscribe_msg_info WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltySubscribeMsgInfo-mapping")
    LoyaltySubscribeMsgInfo getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys


//update status sqls


//get data by foreign keys

}
