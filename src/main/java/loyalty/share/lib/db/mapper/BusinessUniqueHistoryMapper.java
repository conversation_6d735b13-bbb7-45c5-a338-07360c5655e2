package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.BusinessUniqueHistory;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface BusinessUniqueHistoryMapper {
    @Insert("INSERT INTO business_unique_history(id,biz_unique_key,business_type,business_table,remark,create_time,update_time) VALUES " +
            "(#{id},#{bizUniqueKey},#{businessType},#{businessTable},#{remark},#{createTime},#{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(BusinessUniqueHistory businessUniqueHistory);

    @Select("SELECT id,biz_unique_key,business_type,business_table,remark,create_time,update_time FROM business_unique_history WHERE biz_unique_key = #{bizUniqueKey} AND business_type = #{businessType}")
    BusinessUniqueHistory selectByUniqueKey(@Param("bizUniqueKey")String bizUniqueKey,@Param("businessType")String businessType);
}
