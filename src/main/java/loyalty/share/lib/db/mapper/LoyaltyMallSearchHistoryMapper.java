package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.DBCommoditySearchHistory;
import org.apache.ibatis.annotations.*;

import java.util.*;

@Mapper
public interface LoyaltyMallSearchHistoryMapper {

    @Insert("INSERT INTO `loyalty_mall_search_history` ( `union_id`,`keyword`, `trigger_time`, `creator`, `updator`,channel_id,user_ip,user_agent)" +
            " VALUES ( #{unionId}, #{keyword}, #{triggerTime}, #{creator}, #{creator},#{channelId},#{userIp},#{userAgent})" +
            " ON DUPLICATE KEY UPDATE " +
            " trigger_time=#{triggerTime}")
    void add(DBCommoditySearchHistory dbCommoditySearchHistory);

    @Select("select *from loyalty_mall_search_history where union_id=#{unionId} and channel_id in(${channelId}) order by trigger_time desc limit 10")
    @Results(value = {
            @Result(property = "id",column = "id"),
            @Result(property = "unionId",column = "union_id"),
            @Result(property = "channelId",column = "channel_id"),
            @Result(property = "keyword",column = "keyword"),
            @Result(property = "triggerTime",column = "trigger_time"),
            @Result(property = "creator",column = "creator"),
            @Result(property = "updator",column = "updator"),
            @Result(property = "createTime",column = "create_time"),
            @Result(property = "updateTime",column = "update_time"),
    })
    List<DBCommoditySearchHistory> queryDBCommoditySearchHistory(@Param("unionId")String unionId,@Param("channelId") String channelId);
}
