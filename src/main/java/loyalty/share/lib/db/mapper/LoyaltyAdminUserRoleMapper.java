package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyAdminUserRole;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface LoyaltyAdminUserRoleMapper {

    /*@Insert("INSERT INTO loyalty_admin_user_role(id,role_name,creator,updator)" +
            " VALUES(#{id},#{roleName},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyAdminUserRole loyaltyAdminUserRole);

     @Insert("INSERT IGNORE INTO loyalty_admin_user_role(id,role_name,creator,updator)" +
                " VALUES(#{id},#{roleName},#{creator},#{updator})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyAdminUserRole loyaltyAdminUserRole);

    @Insert("INSERT INTO loyalty_admin_user_role(id,role_name,creator,updator)" +
                " VALUES(#{id},#{roleName},#{creator},#{updator})" +
                " ON DUPLICATE KEY UPDATE id=VALUES(id),role_name=VALUES(role_name),updator=VALUES(updator)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyAdminUserRole loyaltyAdminUserRole);

    @Update("UPDATE loyalty_admin_user_role set id=#{id},role_name=#{roleName},updator=#{updator} WHERE id=#{id}" )
    int updateById(LoyaltyAdminUserRole loyaltyAdminUserRole);

    @Delete("DELETE FROM loyalty_admin_user_role WHERE id=#{id}" )
    int deleteById(LoyaltyAdminUserRole loyaltyAdminUserRole);


    @Select("SELECT * FROM loyalty_admin_user_role WHERE id=#{id} ")
    @Results(id = "loyaltyAdminUserRole-mapping", value = {
    @Result(property = "id", column = "id"),@Result(property = "roleName", column = "role_name"),@Result(property = "creator", column = "creator"),@Result(property = "updator", column = "updator"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time")
    })
    LoyaltyAdminUserRole getById(@Param("id") Long id);

	@Select("SELECT * FROM loyalty_admin_user_role  ")
	@ResultMap({"loyaltyAdminUserRole-mapping"})
    List<LoyaltyAdminUserRole> getAllUserRole();*/


    @Select("SELECT * FROM loyalty_admin_user_role WHERE id=#{id} ")
    LoyaltyAdminUserRole getById(@Param("id") Long id);

    @Select("select r.* from loyalty_admin_user_auth ru left join loyalty_admin_user_role r on ru.role_id=r.id where ru.user_id=#{userId}")
    List<LoyaltyAdminUserRole> getRoleListByUserId(Map params);

    @Select("select * from loyalty_admin_user_role where name=#{roleName} and id != #{id}")
    List<LoyaltyAdminUserRole> getRoleByName(@Param("roleName") String roleName, @Param("id") long id);


    @Insert("insert into loyalty_admin_user_role(name,status,description,remark,creator,updator,create_time) values(#{name},1,#{description},#{remark},#{creator},#{updator},now())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void createRole(LoyaltyAdminUserRole role);

    @Update("update loyalty_admin_user_role set name = #{name},updator = #{updator},status=#{status},description=#{description},remark=#{remark},update_time=now() where id = #{id}")
    void updateRole(LoyaltyAdminUserRole role);

    @Delete("delete from loyalty_admin_user_role_auth where role_id = #{roleId}")
    void cleanRoleMenu(@Param("roleId") Long roleId);

    @Update("update loyalty_admin_user_role set status = 0 where id = #{roleId}")
    void deleteRole(@Param("roleId") Long roleId);

    @Select("select loyalty_admin_user_role.id,loyalty_admin_user_role.name,loyalty_admin_user_role.status from loyalty_admin_user_role join loyalty_admin_user_auth ru on loyalty_admin_user_role.id = ru.role_id where user_id = #{userId} and status = 1")
    List<LoyaltyAdminUserRole> queryRoleById(Long userId);

    @Insert("insert into loyalty_admin_user_auth(user_id,role_id) values(#{userId},#{roleId})")
    void createRoleUser(@Param("userId") Long userId, @Param("roleId") long roleId);

    @Delete("delete from loyalty_admin_user_auth where user_id = #{userId}")
    void cleanRoleUser(Long userId);

    @Delete("delete from loyalty_admin_user_role where id = #{id}")
    void delete(Long id);

    @Select("select * from loyalty_admin_user_role where status = 1")
    List<LoyaltyAdminUserRole> queryRoleList();

    @Update({" update loyalty_admin_user_role set status = #{status}, updator=#{updator} where id=#{roleId}"})
    void updateRoleStatus(@Param("roleId") Long roleId, @Param("updator") String updator, @Param("status") Integer status);


}
