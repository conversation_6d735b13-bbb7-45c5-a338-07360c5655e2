package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallMemberOtherChannel;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyMallChannelOtherMapper {

    @Select("SELECT * FROM loyalty_mall_member_other_channel WHERE cellphone=#{cellphone} limit 1")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "type", column = "type"),
            @Result(property = "status", column = "status"),
            @Result(property = "syncTagId", column = "sync_tag_id"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyMallMemberOtherChannel getMemberOtherChannelByCellphone(@Param("cellphone") String cellphone);

}
