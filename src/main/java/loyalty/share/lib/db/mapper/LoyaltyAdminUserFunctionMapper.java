package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyAdminUserFunction;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyAdminUserFunctionMapper {

    /*@Insert("INSERT INTO loyalty_admin_user_function(id,function_name,function_code,creator,updator,request_path)" +
            " VALUES(#{id},#{functionName},#{functionCode},#{creator},#{updator},#{requestPath})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyAdminUserFunction loyaltyAdminUserFunction);

     @Insert("INSERT IGNORE INTO loyalty_admin_user_function(id,function_name,function_code,creator,updator,request_path)" +
                " VALUES(#{id},#{functionName},#{functionCode},#{creator},#{updator},#{requestPath})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyAdminUserFunction loyaltyAdminUserFunction);

    @Insert("INSERT INTO loyalty_admin_user_function(id,function_name,function_code,creator,updator,request_path)" +
                " VALUES(#{id},#{functionName},#{functionCode},#{creator},#{updator},#{requestPath})" +
                " ON DUPLICATE KEY UPDATE id=VALUES(id),function_name=VALUES(function_name),function_code=VALUES(function_code),updator=VALUES(updator),request_path=VALUES(request_path)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyAdminUserFunction loyaltyAdminUserFunction);

    @Update("UPDATE loyalty_admin_user_function set id=#{id},function_name=#{functionName},function_code=#{functionCode},updator=#{updator},request_path=#{requestPath} WHERE id=#{id}" )
    int updateById(LoyaltyAdminUserFunction loyaltyAdminUserFunction);

    @Delete("DELETE FROM loyalty_admin_user_function WHERE id=#{id}" )
    int deleteById(LoyaltyAdminUserFunction loyaltyAdminUserFunction);


    @Select("SELECT * FROM loyalty_admin_user_function WHERE id=#{id} ")
    @Results(id = "loyaltyAdminUserFunction-mapping", value = {
    @Result(property = "id", column = "id"),@Result(property = "functionName", column = "function_name"),@Result(property = "functionCode", column = "function_code"),@Result(property = "creator", column = "creator"),@Result(property = "updator", column = "updator"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time"),@Result(property = "requestPath", column = "request_path")
    })
    LoyaltyAdminUserFunction getById(@Param("id") Long id);

	@Select(" SELECT * from loyalty_admin_user_function where id in (SELECT distinct system_function.id FROM loyalty_admin_user INNER JOIN loyalty_admin_user_auth ON loyalty_admin_user_auth.`user_id` = loyalty_admin_user.`id` " +
			" INNER JOIN loyalty_admin_user_role ON loyalty_admin_user_role.id= loyalty_admin_user_auth.`role_id` " +
			" INNER JOIN loyalty_admin_user_role_auth ON loyalty_admin_user_role.id= loyalty_admin_user_role_auth.`role_id` " +
			" INNER JOIN loyalty_admin_user_function ON loyalty_admin_user_function.id= loyalty_admin_user_role_auth.`function_id` " +
			" WHERE loyalty_admin_user.`id` = #{userId} )")
	@ResultType(LoyaltyAdminUserFunction.class)
	List<LoyaltyAdminUserFunction> getFunctionsByUserId(@Param("userId") Long userId);*/



    @Select({"<script>",
            "select distinct m.* from loyalty_admin_user_function m left join loyalty_admin_user_role_auth rm on m.id=rm.function_id left join loyalty_admin_user_auth ru on rm.role_id=ru.role_id where status=1 ",
            "<if test='userId != null'>",
            " and ru.user_id=#{userId} ",
            "</if>",
            "order by sequence asc",
            "</script>"
    })
    @ResultType(LoyaltyAdminUserFunction.class)
    List<LoyaltyAdminUserFunction> listAllEnable(@Param("userId") Long userId);

    @Select("select id ,name,parent_id parentId,sequence,url,`status` from loyalty_admin_user_function where parent_id = #{menuId} order by sequence asc")
    List<LoyaltyAdminUserFunction> queryMenuList(@Param("menuId") Long menuId);

    @Update("update loyalty_admin_user_function set sequence = sequence + 1 where `status` = 1 and parent_id = 0 and sequence >= #{sequence}")
    void updateParentSeq(@Param("sequence") int sequence);

    @Insert("insert into loyalty_admin_user_function(name,parent_id,sequence,`status`) values(#{name},#{parentId},#{sequence},1)")
    void createParentMenu(LoyaltyAdminUserFunction menu);

    @Update("update loyalty_admin_user_function set sequence = sequence + 1 where `status` = 1 and parent_id = #{parentId} and sequence >= #{sequence}")
    void updateSeq(@Param("sequence") int sequence, @Param("parentId") long parentId);

    @Insert("insert into loyalty_admin_user_function(name,parent_id,sequence,url,`status`,create_time,update_time,icon,component,type,permission) " +
            "values(#{name},#{parentId},#{sequence},#{url},#{status},now(),now(),#{icon},#{component},#{type},#{permission})")
    void createMenu(LoyaltyAdminUserFunction menu);

    @Update("update loyalty_admin_user_function set sequence = sequence - 1 where `status` = 1 and parent_id = #{parentId} and sequence > #{oldSeq} and sequence <= #{sequence}")
    void behindSeq(@Param("sequence") int sequence, @Param("oldSeq") Integer oldSeq, @Param("parentId") long parentId);

    @Update("update loyalty_admin_user_function set sequence = sequence + 1 where `status` = 1 and parent_id = #{parentId} and sequence >= #{sequence} and sequence < #{oldSeq}")
    void beforeSeq(@Param("sequence") int sequence, @Param("oldSeq") Integer oldSeq, @Param("parentId") long parentId);

    @Update("update loyalty_admin_user_function set name = #{name},sequence = #{sequence} where id = #{id}")
    void updateParentMenu(LoyaltyAdminUserFunction menu);

    @Update("update loyalty_admin_user_function set name = #{name},sequence = #{sequence},url = #{url},parent_id=#{parentId},icon=#{icon},component=#{component},type=#{type},permission=#{permission},status=#{status},update_time=now() where id = #{id} ")
    void updateMenu(LoyaltyAdminUserFunction menu);

    @Delete("delete from loyalty_admin_user_role_auth function_id in (select id from loyalty_admin_user_function where parent_id = #{parentId})")
    void batchDeleteRelation(@Param("parentId") long parentId);

    @Delete("update loyalty_admin_user_function set `status` = 0 where parent_id = #{parentId}")
    void batchDeleteMenu(@Param("parentId") long parentId);

    @Update("update loyalty_admin_user_function set `status` = 0 where id = #{menuId}")
    void deleteMenu(@Param("menuId") long menuId);

    @Delete("delete from loyalty_admin_user_role_auth where menu_id = #{menuId}")
    void deleteRelation(@Param("menuId") long menuId);

    @Select("select * from loyalty_admin_user_function where component = #{component} and `status` = 1 LIMIT 1")
    LoyaltyAdminUserFunction getLoyaltyAdminUserFunctionByComponent(String component);


    @Select("select * from loyalty_admin_user_function")
    @ResultType(LoyaltyAdminUserFunction.class)
    List<LoyaltyAdminUserFunction> list();
}
