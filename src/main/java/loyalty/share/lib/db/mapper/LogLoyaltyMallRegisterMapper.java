package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LogLoyaltyMallRegister;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;

@Mapper
public interface LogLoyaltyMallRegisterMapper {

    @Insert("insert into log_loyalty_mall_register(register_cellphone,register_unionid,register_openid,recruit_cellphone,sub_channel,source,register_info,creator,updater) " +
            "values(#{registerCellphone},#{registerUnionid},#{registerOpenid},#{recruitCellphone},#{subChannel},#{source},#{registerInfo},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    public int insert(LogLoyaltyMallRegister logLoyaltyMallRegister);
}
