package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallMember;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyMallMemberMapper {

    @Insert("INSERT INTO loyalty_mall_member (cellphone,openid,rank_id,last_login_time,created_by_user,updated_by_user,update_time, unionid,recruit_by,reg_channel) " +
            "VALUE (#{cellphone},#{openid},#{rankId},#{lastLoginTime},#{createdByUser},#{updatedByUser},now(), #{unionid},#{recruitBy},#{regChannel})" +
            "ON DUPLICATE KEY UPDATE id=VALUES(id),cellphone=VALUES(cellphone),openid=VALUES(openid),rank_id=VALUES(rank_id)," +
            "last_login_time=VALUES(last_login_time),created_by_user=VALUES(created_by_user),updated_by_user=VALUES(updated_by_user),create_time=VALUES(create_time)," +
            "update_time=VALUES(update_time),unionid=VALUES(unionid),recruit_by=VALUES(recruit_by),reg_channel=VALUES(reg_channel)")
    int insertOrUpdate(LoyaltyMallMember mallMember);

    @Select("select count(*) from loyalty_mall_member where cellphone = #{cellphone}")
    Integer getEbRegistByCellphone(@Param("cellphone") String cellphone);

    @Select("select * from loyalty_mall_member where cellphone = #{cellphone}")
    LoyaltyMallMember getMallMemberByPhone(@Param("cellphone") String cellphone);

    @Select("SELECT cellphone FROM loyalty_mall_member WHERE recruit_by = #{cellphone} AND reg_channel = 'EB' ORDER BY create_time LIMIT 9;")
    List<String> getEBRecruitMemberList(String cellphone);

    @Select("select count(*) from campaign_history_bxgx where USER_CELLPHONE = #{cellphone} and campaign_id = #{campaignId}")
    Integer getMemberInfoPoint(@Param("cellphone") String cellphone,@Param("campaignId") Long campaignId);
}
