package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.MemberAddBookModel;
import loyalty.share.lib.db.model.MemberReduceBookModel;
import org.apache.ibatis.annotations.*;

@Mapper
public interface MemberBookMapper {

    @Options(keyColumn = "id", useGeneratedKeys = true)
    @Insert("INSERT INTO loyalty_v2_member_add_book(book_id,cellphone,currency_amount,event_id,changed_by_add_id,trigger_time,expire_time,close_expend_id,inherit_parent_id,create_operation_id,close_operation_id,creator,updator) VALUES " +
            "(#{bookId},#{cellphone},#{currencyAmount},#{eventId},#{changedByAddId},#{triggerTime},#{expireTime},#{closeExpendId},#{inheritParentId},#{createOperationId},#{closeOperationId},'loyalty-ledger-service','loyalty-ledger-service') ")
    void addMemberAddBook(MemberAddBookModel model);

    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("INSERT INTO loyalty_v2_member_reduce_book(book_id,cellphone,currency_amount,event_id,changed_by_reduce_id,trigger_time,close_operation_id,creator,updator,close_time) VALUES " +
            "(#{bookId},#{cellphone},#{currencyAmount},#{eventId},#{changedByReduceId},#{triggerTime},#{closeOperationId},'loyalty-ledger-service','loyalty-ledger-service',#{closeTime} ) ")
    int addMemberReduceBook(MemberReduceBookModel model);

    @Select("SELECT loyalty_v2_currency_book.ID " +
            "FROM (SELECT gift_id,book_owner_id FROM loyalty_v2_currency_book WHERE id=#{bookId}) AS tmp " +
            "INNER JOIN loyalty_v2_currency_book ON tmp.gift_id=loyalty_v2_currency_book.gift_id AND tmp.book_owner_id=loyalty_v2_currency_book.book_owner_id AND is_internal_book IS TRUE ")
    Long queryInternalBookId(Long bookId);
}
