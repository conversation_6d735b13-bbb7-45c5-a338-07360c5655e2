package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallPageGroupRel;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 */
@Mapper
public interface LoyaltyMallPageGroupRelMapper {
    /**
     * 添加数据
     *
     * @param loyaltyMallPageGroupRel loyaltyMallPageGroupRel
     */
    @Options(useGeneratedKeys = true)
    @Insert("INSERT INTO loyalty_mall_page_group_rel(group_id,page_id,theme_id,sort,creator,updator) " +
            "VALUE (#{groupId},#{pageId},#{themeId},#{sort},#{creator},#{updator})")
    void addLoyaltyMallPageGroupRel(LoyaltyMallPageGroupRel loyaltyMallPageGroupRel);

    /**
     * 修改数据
     *
     * @param loyaltyMallPageGroupRel LoyaltyMallPageGroupRel
     * @return 修改的条数
     */
    @Update("UPDATE loyalty_mall_page_group_rel set group_id=#{groupId},page_id=#{pageId},theme_id=#{themeId},sort=#{sort},updator=#{updator} WHERE id=#{id}")
    int updateByEntity(LoyaltyMallPageGroupRel loyaltyMallPageGroupRel);

    /**
     * 通过id 查询数据
     *
     * @param id 主键id
     * @return LoyaltyMallPageModuleGroup
     */
    @Select("SELECT id,group_id,page_id,theme_id,sort,creator,updator,create_time,update_time FROM loyalty_mall_page_group_rel WHERE id = #{id}")
    LoyaltyMallPageGroupRel getById(@Param("id") Long id);

    /**
     * 通过page_id 查询数据
     *
     * @param pageId 页面id
     * @return LoyaltyMallPageModuleGroup
     */
    @Results(id = "LoyaltyMallPageGroupRel-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "groupId", column = "group_id"),
            @Result(property = "pageId", column = "page_id"),
            @Result(property = "themeId", column = "theme_id"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    @Select("SELECT id,group_id,page_id,theme_id,sort,creator,updator,create_time,update_time FROM loyalty_mall_page_group_rel WHERE page_id = #{pageId} LIMIT 1")
    LoyaltyMallPageGroupRel getByPageId(@Param("pageId") Long pageId);

    /**
     * 删除数据
     *
     * @param pageId 页面id
     */
    @Delete("DELETE FROM loyalty_mall_page_group_rel WHERE page_id = #{pageId}")
    void deleteByPageId(@Param("pageId") Long pageId);

    /**
     * 获取groupId条数
     *
     * @param groupId 导航id
     * @return 条数
     */
    @Select("SELECT COUNT(*) FROM `loyalty_mall_page_group_rel` WHERE group_id = #{groupId}")
    Integer getByGroupIdCount(@Param("groupId") Long groupId);
}
