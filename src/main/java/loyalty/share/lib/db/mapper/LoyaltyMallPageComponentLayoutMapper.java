package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallPageComponentLayout;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface LoyaltyMallPageComponentLayoutMapper {
    /**
     * 添加数据
     *
     * @param loyaltyMallPageComponentLayout LoyaltyMallPageComponentLayout
     */
    @Options(useGeneratedKeys = true)
    @Insert("INSERT INTO loyalty_mall_page_component_layout(page_id,component_title,component_code,`position`,position_x,position_y,width,height,content_properties,style_properties,sort,creator,updator) " +
            "VALUE (#{pageId},#{componentTitle},#{componentCode},#{position},#{positionX},#{positionY},#{width},#{height},#{contentProperties},#{styleProperties},#{sort},#{creator},#{updator})")
    void addLoyaltyMallPageComponentLayout(LoyaltyMallPageComponentLayout loyaltyMallPageComponentLayout);

    /**
     * 批量增加
     *
     * @param list 数据集合
     * @return 更新条数
     */
    @Insert({
            "<script>",
            "INSERT INTO loyalty_mall_page_component_layout (",
            "page_id, component_title, component_code, position, ",
            "position_x, position_y, width, height, ",
            "content_properties, style_properties, sort, ",
            "creator, updator, create_time, update_time",
            ") VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(",
            "#{item.pageId}, #{item.componentTitle}, #{item.componentCode}, #{item.position}, ",
            "#{item.positionX}, #{item.positionY}, #{item.width}, #{item.height}, ",
            "#{item.contentProperties}, #{item.styleProperties}, #{item.sort}, ",
            "#{item.creator}, #{item.updator}, #{item.createTime}, #{item.updateTime}",
            ")",
            "</foreach>",
            "</script>"
    })
    int batchInsert(@Param("list") List<LoyaltyMallPageComponentLayout> list);

    /**
     * 修改数据
     *
     * @param loyaltyMallPageComponentLayout LoyaltyMallPageComponentLayout
     * @return 修改的条数
     */
    @Update("UPDATE loyalty_mall_page_component_layout set page_id=#{pageId},component_title=#{componentTitle},component_code=#{componentCode},`position`=#{position},position_x=#{positionX},position_y=#{positionY},width=#{width},height=#{height},content_properties=#{contentProperties},style_properties=#{styleProperties},sort=#{sort},updator=#{updator} WHERE id=#{id}")
    int updateByEntity(LoyaltyMallPageComponentLayout loyaltyMallPageComponentLayout);

    /**
     * 通过pageId获取数据
     *
     * @param pageId pageId
     * @return LoyaltyMallPageComponentLayout
     */
    @Select("SELECT id,page_id,component_title,component_code,`position`,position_x,position_y,width,height,content_properties,style_properties,sort,creator,updator FROM loyalty_mall_page_component_layout WHERE page_id = #{pageId} ORDER BY sort")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "pageId", column = "page_id"),
            @Result(property = "componentTitle", column = "component_title"),
            @Result(property = "componentCode", column = "component_code"),
            @Result(property = "position", column = "position"),
            @Result(property = "positionX", column = "position_x"),
            @Result(property = "positionY", column = "position_y"),
            @Result(property = "width", column = "width"),
            @Result(property = "height", column = "height"),
            @Result(property = "contentProperties", column = "content_properties"),
            @Result(property = "styleProperties", column = "style_properties"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),

    })
    List<LoyaltyMallPageComponentLayout> getByPageId(@Param("pageId") Integer pageId);

    /**
     * 批量删除
     *
     * @param ids 要删除的id集合
     * @return 删除的条数
     */
    @Delete({
            "<script>",
            "DELETE FROM loyalty_mall_page_component_layout WHERE id IN",
            "<foreach item='id' collection='ids' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    int deleteByIdsEX(@Param("ids") List<Long> ids);

    /**
     * 根据页面id删除数据
     * @param pageId
     */
    @Delete("DELETE FROM loyalty_mall_page_component_layout WHERE page_id = #{pageId}")
    void deleteByPageId(@Param("pageId") Long pageId);
}
