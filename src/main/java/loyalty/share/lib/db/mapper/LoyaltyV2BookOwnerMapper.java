package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2BookOwner;

import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface LoyaltyV2BookOwnerMapper {

    @Insert("INSERT INTO loyalty_v2_book_owner(id,owner_code,owner_name,remark,created_by_user,updated_by_user)" +
            " VALUES(#{id},#{ownerCode},#{ownerName},#{remark},#{createdByUser},#{updatedByUser})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2BookOwner loyaltyV2BookOwner);

     @Insert("INSERT IGNORE INTO loyalty_v2_book_owner(id,owner_code,owner_name,remark,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{ownerCode},#{ownerName},#{remark},#{createdByUser},#{updatedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyV2BookOwner loyaltyV2BookOwner);

    @Insert("INSERT INTO loyalty_v2_book_owner(id,owner_code,owner_name,remark,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{ownerCode},#{ownerName},#{remark},#{createdByUser},#{updatedByUser})" +
                " ON DUPLICATE KEY UPDATE id=VALUES(id),owner_code=VALUES(owner_code),owner_name=VALUES(owner_name),remark=VALUES(remark),updated_by_user=VALUES(updated_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyV2BookOwner loyaltyV2BookOwner);

    @Update("UPDATE loyalty_v2_book_owner set id=#{id},owner_code=#{ownerCode},owner_name=#{ownerName},remark=#{remark},updated_by_user=#{updatedByUser} WHERE id=#{id}" )
    int updateById(LoyaltyV2BookOwner loyaltyV2BookOwner);

    @Delete("DELETE FROM loyalty_v2_book_owner WHERE id=#{id}" )
    int deleteById(LoyaltyV2BookOwner loyaltyV2BookOwner);


    @Select("SELECT * FROM loyalty_v2_book_owner WHERE id=#{id} ")
    @Results(id = "loyaltyV2BookOwner-mapping", value = {
    @Result(property = "id", column = "id"),@Result(property = "ownerCode", column = "owner_code"),@Result(property = "ownerName", column = "owner_name"),@Result(property = "remark", column = "remark"),@Result(property = "createdByUser", column = "created_by_user"),@Result(property = "updatedByUser", column = "updated_by_user"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time")
    })
    LoyaltyV2BookOwner getById(@Param("id") Long id);

	@Select("select * from loyalty_v2_book_owner ")
	@ResultMap({"loyaltyV2BookOwner-mapping"})
	List<LoyaltyV2BookOwner> getBookOwnerAll();
}
