package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyV2CampaignGiftPrice;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyV2CampaignGiftPriceMapper {

    @Insert("INSERT INTO `loyalty_v2_campaign_gift_price` (`gift_id`, `currency_id`, `currency_price`, `created_by_user`, `updated_by_user`)" +
            " VALUES (#{giftId}, #{currencyId}, #{currencyPrice}, #{createdByUser}, #{updatedByUser}) " +
            " ON DUPLICATE KEY UPDATE " +
            " currency_id=#{currencyId},currency_price=#{currencyPrice},updated_by_user=#{updatedByUser} ")
    void saveOrUpdate(LoyaltyV2CampaignGiftPrice loyaltyV2CampaignGiftPrice);


    @Select("select *from loyalty_v2_campaign_gift_price where gift_id=#{giftId}")
    @Results(value = {
            @Result(column ="id" ,property = "id"),
            @Result(column ="gift_id" ,property = "giftId"),
            @Result(column ="currency_id" ,property = "currencyId"),
            @Result(column ="currency_price" ,property = "currencyPrice"),
            @Result(column ="created_by_user" ,property = "createdByUser"),
            @Result(column ="updated_by_user" ,property = "updatedByUser"),
            @Result(column ="create_time" ,property = "createTime"),
            @Result(column ="update_time" ,property = "updateTime"),
    })
    LoyaltyV2CampaignGiftPrice queryByGiftId(long giftId);


    @Delete("delete from loyalty_v2_campaign_gift_price where gift_id=#{giftId}")
    void delLoyaltyV2CampaignGiftPrice(long giftId);

}
