package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2LogisticsData;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyV2LogisticsDataMapper {

    @Insert("INSERT INTO loyalty_v2_logistics_data(id,campaign_history_bxgxid,mall_order_id,country,province,city,area,road,add_detail,room_number,recv_cellphone,from_cellphone, channel_id, recv_name,creator,updator,user_ip,user_agent,logistics_warning_type)" +
            " VALUES(#{id},#{campaignHistoryBxgxid},#{mallOrderId},#{country},#{province},#{city},#{area},#{road},#{addDetail},#{roomNumber},#{recvCellphone},#{fromCellphone},#{channelId},#{recvName},#{creator},#{updator},#{userIp},#{userAgent},#{logisticsWarningType})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2LogisticsData loyaltyV2LogisticsData);

    @Insert("INSERT IGNORE INTO loyalty_v2_logistics_data(id,campaign_history_bxgxid,mall_order_id,country,province,city,area,road,add_detail,room_number,recv_cellphone,from_cellphone,channel_id,  recv_name,creator,updator,user_ip,user_agent,logistics_warning_type)" +
            " VALUES(#{id},#{campaignHistoryBxgxid},#{mallOrderId},#{country},#{province},#{city},#{area},#{road},#{addDetail},#{roomNumber},#{recvCellphone},#{fromCellphone},#{channelId},#{recvName},#{creator},#{updator},#{userIp},#{userAgent},#{logisticsWarningType})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyV2LogisticsData loyaltyV2LogisticsData);

    @Insert("INSERT INTO loyalty_v2_logistics_data(id,campaign_history_bxgxid,mall_order_id,country,province,city,area,road,add_detail,room_number,recv_cellphone,recv_name,from_cellphone,channel_id,  creator,updator,user_ip,user_agent,logistics_warning_type)" +
            " VALUES(#{id},#{campaignHistoryBxgxid},#{mallOrderId},#{country},#{province},#{city},#{area},#{road},#{addDetail},#{roomNumber},#{recvCellphone},#{fromCellphone},#{channelId},#{recvName},#{creator},#{updator},#{userIp},#{userAgent},#{logisticsWarningType})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),campaign_history_bxgxid=VALUES(campaign_history_bxgxid),mall_order_id=VALUES(mall_order_id),country=VALUES(country),province=VALUES(province),city=VALUES(city),area=VALUES(area),road=VALUES(road),add_detail=VALUES(add_detail),room_number=VALUES(room_number),recv_cellphone=VALUES(recv_cellphone),logistics_warning_type=VALUES(logistics_warning_type)" +
            " ,recv_name=VALUES(recv_name),updator=VALUES(updator),user_ip=VALUES(user_ip),user_agent=VALUES(user_agent)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyV2LogisticsData loyaltyV2LogisticsData);

    @Update("UPDATE loyalty_v2_logistics_data set id=#{id},campaign_history_bxgxid=#{campaignHistoryBxgxid},mall_order_id=#{mallOrderId},country=#{country},province=#{province},city=#{city},area=#{area},road=#{road},add_detail=#{addDetail},room_number=#{roomNumber},recv_cellphone=#{recvCellphone},from_cellphone=#{fromCellphone},recv_name=#{recvName},updator=#{updator},logistics_warning_type={logisticsWarningType} WHERE id=#{id}")
    int updateById(LoyaltyV2LogisticsData loyaltyV2LogisticsData);

    @Delete("DELETE FROM loyalty_v2_logistics_data WHERE id=#{id}")
    int deleteById(LoyaltyV2LogisticsData loyaltyV2LogisticsData);


    @Select("SELECT * FROM loyalty_v2_logistics_data WHERE id=#{id} ")
    @Results(id = "loyaltyV2LogisticsData-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "campaignHistoryBxgxid", column = "campaign_history_bxgxid"), @Result(property = "mallOrderId", column = "mall_order_id"), @Result(property = "country", column = "country"), @Result(property = "province", column = "province"), @Result(property = "city", column = "city"), @Result(property = "area", column = "area"), @Result(property = "road", column = "road"), @Result(property = "addDetail", column = "add_detail"),@Result(property = "roomNumber", column = "room_number"), @Result(property = "recvCellphone", column = "recv_cellphone"), @Result(property = "fromCellphone", column = "from_cellphone"), @Result(property = "channelId", column = "channel_id"), @Result(property = "recvName", column = "recv_name"), @Result(property = "creator", column = "creator"), @Result(property = "updator", column = "updator"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyV2LogisticsData getById(@Param("id") Long id);

    @Insert("INSERT IGNORE INTO loyalty_v2_logistics_data(id,campaign_history_bxgxid,mall_order_id,country,province," +
        "city,area,road,add_detail,room_number,recv_cellphone,from_cellphone,channel_id,  recv_name,creator,updator," +
        "user_ip,user_agent,history_instid,history_table_name,logistics_warning_type)" +
        " VALUES(#{id},#{campaignHistoryBxgxid},#{mallOrderId},#{country},#{province},#{city},#{area},#{road}," +
        "#{addDetail},#{roomNumber},#{recvCellphone},#{fromCellphone},#{channelId},#{recvName},#{creator},#{updator}," +
        "#{userIp},#{userAgent},#{historyInstid},#{historyTableName},#{logisticsWarningType})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreV2(LoyaltyV2LogisticsData loyaltyV2LogisticsData);

}
