package loyalty.share.lib.db.mapper;


import java.util.Date;

import loyalty.share.lib.db.model.PclassUserSignDetail;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface PclassUserSignDetailMapper {

     @Insert("INSERT IGNORE INTO pclass_user_sign_detail(id,pclass_code,cellphone,pnec_id,trigger_time,current_longitude,current_latitude,creator,updater)" +
                " VALUES(#{id},#{pclassCode},#{cellphone},#{pnecId},#{triggerTime},#{currentLongitude},#{currentLatitude},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(PclassUserSignDetail pclassUserSignDetail);

    @Insert("INSERT INTO pclass_user_sign_detail(id,pclass_code,cellphone,pnec_id,trigger_time,current_longitude,current_latitude,creator,updater)" +
                " VALUES(#{id},#{pclassCode},#{cellphone},#{pnecId},#{triggerTime},#{currentLongitude},#{currentLatitude},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE pnec_id=VALUES(pnec_id),trigger_time=VALUES(trigger_time),current_longitude=VALUES(current_longitude),current_latitude=VALUES(current_latitude),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(PclassUserSignDetail pclassUserSignDetail);

    @Update("UPDATE pclass_user_sign_detail set pclass_code=#{pclassCode},cellphone=#{cellphone},pnec_id=#{pnecId},trigger_time=#{triggerTime},current_longitude=#{currentLongitude},current_latitude=#{currentLatitude},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(PclassUserSignDetail pclassUserSignDetail);

    @Delete("DELETE FROM pclass_user_sign_detail WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update pclass_user_sign_detail set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,pclass_code,cellphone,pnec_id,trigger_time,current_longitude,current_latitude,creator,updater,create_time,update_time FROM pclass_user_sign_detail WHERE id=#{id} ")
    @Results(id = "pclassUserSignDetail-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "pclassCode", column = "pclass_code"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "pnecId", column = "pnec_id"),
      @Result(property = "triggerTime", column = "trigger_time"),
      @Result(property = "currentLongitude", column = "current_longitude"),
      @Result(property = "currentLatitude", column = "current_latitude"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    PclassUserSignDetail getByIdEX(@Param("id") Long id);

    @Select("SELECT id,pclass_code,cellphone,pnec_id,trigger_time,current_longitude,current_latitude,creator,updater,create_time,update_time FROM pclass_user_sign_detail WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "pclassUserSignDetail-mapping")
    PclassUserSignDetail getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys
    @Select("SELECT id,pclass_code,cellphone,pnec_id,trigger_time,current_longitude,current_latitude,creator,updater,create_time,update_time FROM pclass_user_sign_detail WHERE pclass_code=#{pclassCode} and cellphone=#{cellphone} ")
    @ResultMap(value = "pclassUserSignDetail-mapping")
    PclassUserSignDetail getByPclassCodeCellphone(@Param("pclassCode") String pclassCode,@Param("cellphone") String cellphone);



//update status sqls


//get data by foreign keys

}
