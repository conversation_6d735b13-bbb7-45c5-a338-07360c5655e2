package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2LogisticsDataDetail;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyV2LogisticsDataDetailMapper {

    @Insert("INSERT INTO loyalty_v2_logistics_data_detail(id,logistics_data_id,order_detail_id,gift_id,good_count,logistics_status,logistics_party,logistics_no,logistics_deliver_time,logistics_received_time,creator,updator)" +
            " VALUES(#{id},#{logisticsDataId},#{orderDetailId},#{giftId},#{goodCount},#{logisticsStatus},#{logisticsParty},#{logisticsNo},#{logisticsDeliverTime},#{logisticsReceivedTime},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2LogisticsDataDetail loyaltyV2LogisticsDataDetail);

    @Insert("INSERT IGNORE INTO loyalty_v2_logistics_data_detail(id,logistics_data_id,order_detail_id,gift_id,good_count,logistics_status,logistics_party,logistics_no,logistics_deliver_time,logistics_received_time,creator,updator)" +
            " VALUES(#{id},#{logisticsDataId},#{orderDetailId},#{giftId},#{goodCount},#{logisticsStatus},#{logisticsParty},#{logisticsNo},#{logisticsDeliverTime},#{logisticsReceivedTime},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyV2LogisticsDataDetail loyaltyV2LogisticsDataDetail);

    @Insert("INSERT INTO loyalty_v2_logistics_data_detail(id,logistics_data_id,order_detail_id,gift_id,good_count,logistics_status,logistics_party,logistics_no,logistics_deliver_time,logistics_received_time,creator,updator)" +
            " VALUES(#{id},#{logisticsDataId},#{orderDetailId},#{giftId},#{goodCount},#{logisticsStatus},#{logisticsParty},#{logisticsNo},#{logisticsDeliverTime},#{logisticsReceivedTime},#{creator},#{updator})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),logistics_data_id=VALUES(logistics_data_id),order_detail_id=VALUES(order_detail_id),gift_id=VALUES(gift_id),logistics_status=VALUES(logistics_status),logistics_party=VALUES(logistics_party),logistics_no=VALUES(logistics_no),logistics_deliver_time=VALUES(logistics_deliver_time),logistics_received_time=VALUES(logistics_received_time),updator=VALUES(updator)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyV2LogisticsDataDetail loyaltyV2LogisticsDataDetail);

    @Update("UPDATE loyalty_v2_logistics_data_detail set id=#{id},logistics_data_id=#{logisticsDataId},order_detail_id=#{orderDetailId},gift_id=#{giftId},good_count=#{goodCount},logistics_status=#{logisticsStatus},logistics_party=#{logisticsParty},logistics_no=#{logisticsNo},logistics_deliver_time=#{logisticsDeliverTime},logistics_received_time=#{logisticsReceivedTime},updator=#{updator} WHERE id=#{id}")
    int updateById(LoyaltyV2LogisticsDataDetail loyaltyV2LogisticsDataDetail);

    @Delete("DELETE FROM loyalty_v2_logistics_data_detail WHERE id=#{id}")
    int deleteById(LoyaltyV2LogisticsDataDetail loyaltyV2LogisticsDataDetail);


    @Select("SELECT * FROM loyalty_v2_logistics_data_detail WHERE id=#{id} ")
    @Results(id = "loyaltyV2LogisticsDataDetail-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "logisticsDataId", column = "logistics_data_id"), @Result(property = "orderDetailId", column = "order_detail_id"), @Result(property = "giftId", column = "gift_id"), @Result(property = "goodCount", column = "good_count"), @Result(property = "logisticsStatus", column = "logistics_status"), @Result(property = "logisticsParty", column = "logistics_party"), @Result(property = "logisticsNo", column = "logistics_no"), @Result(property = "logisticsDeliverTime", column = "logistics_deliver_time"), @Result(property = "logisticsReceivedTime", column = "logistics_received_time"), @Result(property = "creator", column = "creator"), @Result(property = "updator", column = "updator"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyV2LogisticsDataDetail getById(@Param("id") Long id);
}
