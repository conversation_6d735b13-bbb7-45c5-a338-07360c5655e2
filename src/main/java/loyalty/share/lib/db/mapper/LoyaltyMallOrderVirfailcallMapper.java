package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallOrderVirfailcallMode;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;

@Mapper
public interface LoyaltyMallOrderVirfailcallMapper {
    @Options(useGeneratedKeys = true)
    @Insert("INSERT INTO loyalty_mall_order_virfailcall(order_detail_id,cellphone,user_ip,failed_index,has_suc,fail_reason,fail_reason_code,retry_times,CREATOR,UPDATOR) " +
            "VALUE (#{orderDetailId},#{cellphone},#{userIp},#{failedIndex},#{hasSuc},#{failReason},#{failReasonCode},#{retryTimes},#{creator},#{updator})")
    void add(LoyaltyMallOrderVirfailcallMode mallOrderVirfailcallMode);
}
