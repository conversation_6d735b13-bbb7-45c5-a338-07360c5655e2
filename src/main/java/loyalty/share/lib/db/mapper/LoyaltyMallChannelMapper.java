package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallChannel;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.*;

public interface LoyaltyMallChannelMapper{

   @Select("select *from loyalty_mall_channel")
    @Results(value = {
            @Result(column = "id",property = "id"),
            @Result(column = "channel_code",property = "channelCode"),
            @Result(column = "channel_name",property = "channelName"),
            @Result(column = "created_by_user",property = "createdByUser"),
            @Result(column = "updated_by_user",property = "updatedByUser"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time",property = "updateTime"),
    })
   List<LoyaltyMallChannel> queryList();

}
