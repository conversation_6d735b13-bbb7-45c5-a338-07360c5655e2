package loyalty.share.lib.db.mapper;


import java.util.Date;

import loyalty.share.lib.db.model.PclassUserExpectCityRecord;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface PclassUserExpectCityRecordMapper  {

     @Insert("INSERT IGNORE INTO pclass_user_expect_city_record(id,cellphone,select_province,select_city,expect_city,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{selectProvince},#{selectCity},#{expectCity},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(PclassUserExpectCityRecord pclassUserExpectCityRecord);

    @Insert("INSERT INTO pclass_user_expect_city_record(id,cellphone,select_province,select_city,expect_city,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{selectProvince},#{selectCity},#{expectCity},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE select_province=VALUES(select_province),select_city=VALUES(select_city),expect_city=VALUES(expect_city),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(PclassUserExpectCityRecord pclassUserExpectCityRecord);

    @Update("UPDATE pclass_user_expect_city_record set cellphone=#{cellphone},select_province=#{selectProvince},select_city=#{selectCity},expect_city=#{expectCity},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(PclassUserExpectCityRecord pclassUserExpectCityRecord);

    @Delete("DELETE FROM pclass_user_expect_city_record WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update pclass_user_expect_city_record set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,cellphone,select_province,select_city,expect_city,creator,updater,create_time,update_time FROM pclass_user_expect_city_record WHERE id=#{id} ")
    @Results(id = "pclassUserExpectCityRecord-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "selectProvince", column = "select_province"),
      @Result(property = "selectCity", column = "select_city"),
      @Result(property = "expectCity", column = "expect_city"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    PclassUserExpectCityRecord getByIdEX(@Param("id") Long id);

    @Select("SELECT id,cellphone,select_province,select_city,expect_city,creator,updater,create_time,update_time FROM pclass_user_expect_city_record WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "pclassUserExpectCityRecord-mapping")
    PclassUserExpectCityRecord getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys
    @Select("SELECT id,cellphone,select_province,select_city,expect_city,creator,updater,create_time,update_time FROM pclass_user_expect_city_record WHERE cellphone=#{cellphone} ")
    @ResultMap(value = "pclassUserExpectCityRecord-mapping")
    PclassUserExpectCityRecord getByCellphone(@Param("cellphone") String cellphone);



//update status sqls


//get data by foreign keys

}
