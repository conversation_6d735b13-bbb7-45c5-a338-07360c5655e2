package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2Log3rdpartycall;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyV2Log3rdpartycallMapper {

    @Insert("INSERT INTO loyalty_v2_log_3rdpartycall(id,fail_record_table,fail_record_id,request_senario,request_body,request_url,response_body,creator,updator)" +
            " VALUES(#{id},#{failRecordTable},#{failRecordId},#{requestSenario},#{requestBody},#{requestUrl},#{responseBody},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2Log3rdpartycall loyaltyV2Log3rdpartycall);

    @Insert("INSERT IGNORE INTO loyalty_v2_log_3rdpartycall(id,fail_record_table,fail_record_id,request_senario,request_body,request_url,response_body,creator,updator)" +
            " VALUES(#{id},#{failRecordTable},#{failRecordId},#{requestSenario},#{requestBody},#{requestUrl},#{responseBody},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyV2Log3rdpartycall loyaltyV2Log3rdpartycall);

    @Insert("INSERT INTO loyalty_v2_log_3rdpartycall(id,fail_record_table,fail_record_id,request_senario,request_body,request_url,response_body,creator,updator)" +
            " VALUES(#{id},#{failRecordTable},#{failRecordId},#{requestSenario},#{requestBody},#{requestUrl},#{responseBody},#{creator},#{updator})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),fail_record_table=VALUES(fail_record_table),fail_record_id=VALUES(fail_record_id),request_senario=VALUES(request_senario),request_body=VALUES(request_body),request_url=VALUES(request_url),response_body=VALUES(response_body),updator=VALUES(updator)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyV2Log3rdpartycall loyaltyV2Log3rdpartycall);

    @Update("UPDATE loyalty_v2_log_3rdpartycall set id=#{id},fail_record_table=#{failRecordTable},fail_record_id=#{failRecordId},request_senario=#{requestSenario},request_body=#{requestBody},request_url=#{requestUrl},response_body=#{responseBody},updator=#{updator} WHERE id=#{id}")
    int updateById(LoyaltyV2Log3rdpartycall loyaltyV2Log3rdpartycall);

    @Delete("DELETE FROM loyalty_v2_log_3rdpartycall WHERE id=#{id}")
    int deleteById(LoyaltyV2Log3rdpartycall loyaltyV2Log3rdpartycall);


    @Select("SELECT * FROM loyalty_v2_log_3rdpartycall WHERE id=#{id} ")
    @Results(id = "loyaltyV2Log3rdpartycall-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "failRecordTable", column = "fail_record_table"), @Result(property = "failRecordId", column = "fail_record_id"), @Result(property = "requestSenario", column = "request_senario"), @Result(property = "requestBody", column = "request_body"), @Result(property = "requestUrl", column = "request_url"), @Result(property = "responseBody", column = "response_body"), @Result(property = "creator", column = "creator"), @Result(property = "updator", column = "updator"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyV2Log3rdpartycall getById(@Param("id") Long id);

    @Insert("INSERT INTO loyalty_v2_log_3rdpartycall(fail_record_table,fail_record_id,request_senario,request_url,request_body,response_body,creator) VALUE " +
            "(#{failRecordTable},#{failRecordId},#{requestSenario},#{requestUrl},#{requestBody},#{responseBody},#{creator}) ")
    void add(LoyaltyV2Log3rdpartycall model);
}
