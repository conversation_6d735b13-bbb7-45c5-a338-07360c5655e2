package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.Log3rdPartyEBCallModel;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyV2Log3rdpartyebcallMapper {

    @Insert("INSERT INTO loyalty_v2_log_3rdpartyebcall(id,req_desc,cellphone,request_senario,request_body,request_url,response_body,creator,updator)" +
            " VALUES(#{id},#{reqDesc},#{cellphone},#{requestSenario},#{requestBody},#{requestUrl},#{responseBody},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Log3rdPartyEBCallModel log3rdPartyEBCallModel);

    @Insert("INSERT IGNORE INTO loyalty_v2_log_3rdpartyebcall(id,req_desc,cellphone,request_senario,request_body,request_url,response_body,creator,updator,send_successed, nc_code, store_id, store_name,  client_name)" +
            " VALUES(#{id},#{reqDesc},#{cellphone},#{requestSenario},#{requestBody},#{requestUrl},#{responseBody},#{creator},#{updator},#{sendSuccessed},#{ncCode},#{storeId},#{storeName},#{clientName})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(Log3rdPartyEBCallModel log3rdPartyEBCallModel);

    @Insert("INSERT INTO loyalty_v2_log_3rdpartyebcall(id,req_desc,cellphone,request_senario,request_body,request_url,response_body,creator,updator)" +
            " VALUES(#{id},#{reqDesc},#{cellphone},#{requestSenario},#{requestBody},#{requestUrl},#{responseBody},#{creator},#{updator})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),req_desc=VALUES(req_desc),cellphone=VALUES(cellphone),request_senario=VALUES(request_senario),request_body=VALUES(request_body),request_url=VALUES(request_url),response_body=VALUES(response_body),updator=VALUES(updator)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(Log3rdPartyEBCallModel log3rdPartyEBCallModel);

    @Update("UPDATE loyalty_v2_log_3rdpartyebcall set id=#{id},req_desc=#{reqDesc},cellphone=#{cellphone},request_senario=#{requestSenario},request_body=#{requestBody},request_url=#{requestUrl},response_body=#{responseBody},updator=#{updator},send_successed=#{sendSuccessed},update_successed=#{updateSuccessed},update_time=NOW() WHERE id=#{id}")
    int updateById(Log3rdPartyEBCallModel log3rdPartyEBCallModel);

    @Select("SELECT * FROM loyalty_v2_log_3rdpartyebcall WHERE request_senario=#{requestSenario} AND send_successed = 0 AND (update_successed is NULL OR update_successed = 0)  ORDER BY create_time ASC;")
    @Results(value = {
            @Result(column = "id",property = "id"),
            @Result(column = "req_desc",property = "reqDesc"),
            @Result(column = "cellphone",property = "cellphone"),
            @Result(column = "send_successed",property = "sendSuccessed"),
            @Result(column = "update_successed",property = "updateSuccessed"),
            @Result(column = "request_senario",property = "requestSenario"),
            @Result(column = "request_url",property = "requestUrl"),
            @Result(column = "request_body",property = "requestBody"),
            @Result(column = "response_body",property = "responseBody"),
            @Result(column = "creator",property = "creator"),
            @Result(column = "updator",property = "updator"),
    })
    List<Log3rdPartyEBCallModel> findAllFailedLog3rdPartyEBCallModel(String requestSenario);

    @Select("SELECT eb.* FROM loyalty_mall_member m,loyalty_v2_log_3rdpartyebcall eb WHERE m.cellphone = #{cellphone} AND  m.cellphone = eb.cellphone ORDER BY eb.create_time DESC LIMIT 1;")
    @Results(id = "loyaltyV2Log3rdpartyebcall-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "ncCode", column = "nc_code"),
            @Result(property = "storeId", column = "store_id"),
            @Result(property = "storeName", column = "store_name"),
            @Result(property = "clientName", column = "client_name"),
    }
    )
    Log3rdPartyEBCallModel getRegByEBInfo(String cellphone);
}
