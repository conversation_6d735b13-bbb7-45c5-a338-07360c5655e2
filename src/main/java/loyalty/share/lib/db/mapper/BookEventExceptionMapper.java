package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.BookEventExceptionModel;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface BookEventExceptionMapper {

    @Insert("INSERT INTO loyalty_v2_book_event_exception(event_id,event_type,event_params,cellphone,related_book_ids,related_currency_ids,err_msg,err_detail,ready_to_reprocess,creator,updator) VALUES " +
            "(#{eventId},#{eventType},#{eventParams},#{cellphone},#{relatedBookIds},#{relatedCurrencyIds},#{errMsg},#{errDetail},#{readyToReprocess},#{creator},#{creator})")
    void addBookEventException(BookEventExceptionModel model);
}
