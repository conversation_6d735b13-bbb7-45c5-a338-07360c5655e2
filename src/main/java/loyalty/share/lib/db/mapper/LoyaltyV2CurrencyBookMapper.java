package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2CurrencyBook;

import org.apache.ibatis.annotations.*;

import java.util.*;

@Mapper
public interface LoyaltyV2CurrencyBookMapper {

    @Insert("INSERT INTO loyalty_v2_currency_book(gift_id,book_owner_id,remark,book_name,is_enabled,is_internal_book,created_by_user,updated_by_user)" +
            " VALUES(,#{giftId},#{bookOwnerId},#{remark},#{bookName},#{isEnabled},#{isInternalBook},#{createdByUser},#{updatedByUser})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2CurrencyBook loyaltyV2CurrencyBook);

     @Insert("INSERT IGNORE INTO loyalty_v2_currency_book(id,gift_id,book_owner_id,remark,book_name,is_enabled,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{giftId},#{bookOwnerId},#{remark},#{bookName},#{isEnabled},#{createdByUser},#{updatedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyV2CurrencyBook loyaltyV2CurrencyBook);

    @Insert("INSERT INTO loyalty_v2_currency_book(id,gift_id,book_owner_id,remark,book_name,is_enabled,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{giftId},#{bookOwnerId},#{remark},#{bookName},#{isEnabled},#{createdByUser},#{updatedByUser})" +
                " ON DUPLICATE KEY UPDATE id=VALUES(id),gift_id=VALUES(gift_id),book_owner_id=VALUES(book_owner_id),remark=VALUES(remark),book_name=VALUES(book_name),is_enabled=VALUES(is_enabled),updated_by_user=VALUES(updated_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyV2CurrencyBook loyaltyV2CurrencyBook);

    @Update("UPDATE loyalty_v2_currency_book set id=#{id},gift_id=#{giftId},book_owner_id=#{bookOwnerId},remark=#{remark},book_name=#{bookName},is_enabled=#{isEnabled},updated_by_user=#{updatedByUser} WHERE id=#{id}" )
    int updateById(LoyaltyV2CurrencyBook loyaltyV2CurrencyBook);

    @Delete("DELETE FROM loyalty_v2_currency_book WHERE id=#{id}" )
    int deleteById(LoyaltyV2CurrencyBook loyaltyV2CurrencyBook);


    @Select("SELECT * FROM loyalty_v2_currency_book WHERE id=#{id} ")
    @Results(id = "loyaltyV2CurrencyBook-mapping", value = {
    @Result(property = "id", column = "id"),
		    @Result(property = "giftId", column = "gift_id"),
		    @Result(property = "bookOwnerId", column = "book_owner_id"),@Result(property = "remark", column = "remark"),@Result(property = "bookName", column = "book_name"),@Result(property = "isEnabled", column = "is_enabled"),@Result(property = "createdByUser", column = "created_by_user"),@Result(property = "updatedByUser", column = "updated_by_user"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time")
    })
    LoyaltyV2CurrencyBook getById(@Param("id") Long id);

	@Select("select * from loyalty_v2_currency_book where book_name = #{bookName} ")
	@ResultMap({"loyaltyV2CurrencyBook-mapping"})
	LoyaltyV2CurrencyBook getBookByBookName(@Param("bookName") String bookName);

    @Select("select * from loyalty_v2_currency_book ")
    @ResultMap({"loyaltyV2CurrencyBook-mapping"})
    List<LoyaltyV2CurrencyBook> getBookAll();

	@Select("select id from loyalty_v2_currency_book ")
	@ResultType(Long.class)
	List<Long> getAllBookId();

    @Select("select * from loyalty_v2_currency_book where gift_id=#{giftId}")
    @ResultMap({"loyaltyV2CurrencyBook-mapping"})
    List<LoyaltyV2CurrencyBook> getBookByGiftId(@Param("giftId") int giftId);

}
