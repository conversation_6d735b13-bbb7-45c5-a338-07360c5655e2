package loyalty.share.lib.db.mapper;


import java.util.Date;

import loyalty.share.lib.db.model.PclassInfo;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface PclassInfoMapper  {

     @Insert("INSERT IGNORE INTO pclass_info(id,classes_code,topic,start_time,end_time,owner_name,owner_mobile,owner_employee_num,director,director_mobile,director_exmployee_num,is_enabled,remark,province,city,place,address,longitude,latitude,expert_name,expert_introduce,hotline,qrcode_url,limit_count,survey_template,pclass_type,pclass_property,pclass_type_2,course_name,other_course_name,invited_family_count,nc_code,last_modify_time,upload_time,upload_status,status,online_channel,hospital_code,hospital_name,is_godeep_city,is_send_coupon,business_reason,is_online_activity,activity_type,action_type,is_deleted,creator,updater,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{classesCode},#{topic},#{startTime},#{endTime},#{ownerName},#{ownerMobile},#{ownerEmployeeNum},#{director},#{directorMobile},#{directorExmployeeNum},#{isEnabled},#{remark},#{province},#{city},#{place},#{address},#{longitude},#{latitude},#{expertName},#{expertIntroduce},#{hotline},#{qrcodeUrl},#{limitCount},#{surveyTemplate},#{pclassType},#{pclassProperty},#{pclassType2},#{courseName},#{otherCourseName},#{invitedFamilyCount},#{ncCode},#{lastModifyTime},#{uploadTime},#{uploadStatus},#{status},#{onlineChannel},#{hospitalCode},#{hospitalName},#{isGodeepCity},#{isSendCoupon},#{businessReason},#{isOnlineActivity},#{activityType},#{actionType},#{isDeleted},#{creator},#{updater},#{createdByUser},#{updatedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(PclassInfo pclassInfo);

    @Insert("INSERT INTO pclass_info(id,classes_code,topic,start_time,end_time,owner_name,owner_mobile,owner_employee_num,director,director_mobile,director_exmployee_num,is_enabled,remark,province,city,place,address,longitude,latitude,expert_name,expert_introduce,hotline,qrcode_url,limit_count,survey_template,pclass_type,pclass_property,pclass_type_2,course_name,other_course_name,invited_family_count,nc_code,last_modify_time,upload_time,upload_status,status,online_channel,hospital_code,hospital_name,is_godeep_city,is_send_coupon,business_reason,is_online_activity,activity_type,action_type,is_deleted,creator,updater,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{classesCode},#{topic},#{startTime},#{endTime},#{ownerName},#{ownerMobile},#{ownerEmployeeNum},#{director},#{directorMobile},#{directorExmployeeNum},#{isEnabled},#{remark},#{province},#{city},#{place},#{address},#{longitude},#{latitude},#{expertName},#{expertIntroduce},#{hotline},#{qrcodeUrl},#{limitCount},#{surveyTemplate},#{pclassType},#{pclassProperty},#{pclassType2},#{courseName},#{otherCourseName},#{invitedFamilyCount},#{ncCode},#{lastModifyTime},#{uploadTime},#{uploadStatus},#{status},#{onlineChannel},#{hospitalCode},#{hospitalName},#{isGodeepCity},#{isSendCoupon},#{businessReason},#{isOnlineActivity},#{activityType},#{actionType},#{isDeleted},#{creator},#{updater},#{createdByUser},#{updatedByUser})" +
                " ON DUPLICATE KEY UPDATE topic=VALUES(topic),start_time=VALUES(start_time),end_time=VALUES(end_time),owner_name=VALUES(owner_name),owner_mobile=VALUES(owner_mobile),owner_employee_num=VALUES(owner_employee_num),director=VALUES(director),director_mobile=VALUES(director_mobile),director_exmployee_num=VALUES(director_exmployee_num),is_enabled=VALUES(is_enabled),remark=VALUES(remark),province=VALUES(province),city=VALUES(city),place=VALUES(place),address=VALUES(address),longitude=VALUES(longitude),latitude=VALUES(latitude),expert_name=VALUES(expert_name),expert_introduce=VALUES(expert_introduce),hotline=VALUES(hotline),qrcode_url=VALUES(qrcode_url),limit_count=VALUES(limit_count),survey_template=VALUES(survey_template),pclass_type=VALUES(pclass_type),pclass_property=VALUES(pclass_property),pclass_type_2=VALUES(pclass_type_2),course_name=VALUES(course_name),other_course_name=VALUES(other_course_name),invited_family_count=VALUES(invited_family_count),nc_code=VALUES(nc_code),last_modify_time=VALUES(last_modify_time),upload_time=VALUES(upload_time),upload_status=VALUES(upload_status),status=VALUES(status),online_channel=VALUES(online_channel),hospital_code=VALUES(hospital_code),hospital_name=VALUES(hospital_name),is_godeep_city=VALUES(is_godeep_city),is_send_coupon=VALUES(is_send_coupon),business_reason=VALUES(business_reason),is_online_activity=VALUES(is_online_activity),activity_type=VALUES(activity_type),action_type=VALUES(action_type),is_deleted=VALUES(is_deleted),updater=VALUES(updater),updated_by_user=VALUES(updated_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(PclassInfo pclassInfo);

    @Update("UPDATE pclass_info set classes_code=#{classesCode},topic=#{topic},start_time=#{startTime},end_time=#{endTime},owner_name=#{ownerName},owner_mobile=#{ownerMobile},owner_employee_num=#{ownerEmployeeNum},director=#{director},director_mobile=#{directorMobile},director_exmployee_num=#{directorExmployeeNum},is_enabled=#{isEnabled},remark=#{remark},province=#{province},city=#{city},place=#{place},address=#{address},longitude=#{longitude},latitude=#{latitude},expert_name=#{expertName},expert_introduce=#{expertIntroduce},hotline=#{hotline},qrcode_url=#{qrcodeUrl},limit_count=#{limitCount},survey_template=#{surveyTemplate},pclass_type=#{pclassType},pclass_property=#{pclassProperty},pclass_type_2=#{pclassType2},course_name=#{courseName},other_course_name=#{otherCourseName},invited_family_count=#{invitedFamilyCount},nc_code=#{ncCode},last_modify_time=#{lastModifyTime},upload_time=#{uploadTime},upload_status=#{uploadStatus},status=#{status},online_channel=#{onlineChannel},hospital_code=#{hospitalCode},hospital_name=#{hospitalName},is_godeep_city=#{isGodeepCity},is_send_coupon=#{isSendCoupon},business_reason=#{businessReason},is_online_activity=#{isOnlineActivity},activity_type=#{activityType},action_type=#{actionType},is_deleted=#{isDeleted},updater=#{updater},updated_by_user=#{updatedByUser} WHERE id=#{id}" )
    int updateByEntity(PclassInfo pclassInfo);

    @Delete("DELETE FROM pclass_info WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") String id);

    @Delete("Update pclass_info set is_deleted=true, updater=#{userKey}, updated_by_user=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") String id);

    @Select("SELECT id,classes_code,topic,start_time,end_time,owner_name,owner_mobile,owner_employee_num,director,director_mobile,director_exmployee_num,is_enabled,remark,province,city,place,address,longitude,latitude,expert_name,expert_introduce,hotline,qrcode_url,limit_count,survey_template,pclass_type,pclass_property,pclass_type_2,course_name,other_course_name,invited_family_count,nc_code,last_modify_time,upload_time,upload_status,status,online_channel,hospital_code,hospital_name,is_godeep_city,is_send_coupon,business_reason,is_online_activity,activity_type,action_type,is_deleted,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_info WHERE id=#{id} ")
    @Results(id = "pclassInfo-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "classesCode", column = "classes_code"),
      @Result(property = "topic", column = "topic"),
      @Result(property = "startTime", column = "start_time"),
      @Result(property = "endTime", column = "end_time"),
      @Result(property = "ownerName", column = "owner_name"),
      @Result(property = "ownerMobile", column = "owner_mobile"),
      @Result(property = "ownerEmployeeNum", column = "owner_employee_num"),
      @Result(property = "director", column = "director"),
      @Result(property = "directorMobile", column = "director_mobile"),
      @Result(property = "directorExmployeeNum", column = "director_exmployee_num"),
      @Result(property = "isEnabled", column = "is_enabled"),
      @Result(property = "remark", column = "remark"),
      @Result(property = "province", column = "province"),
      @Result(property = "city", column = "city"),
      @Result(property = "place", column = "place"),
      @Result(property = "address", column = "address"),
      @Result(property = "longitude", column = "longitude"),
      @Result(property = "latitude", column = "latitude"),
      @Result(property = "expertName", column = "expert_name"),
      @Result(property = "expertIntroduce", column = "expert_introduce"),
      @Result(property = "hotline", column = "hotline"),
      @Result(property = "qrcodeUrl", column = "qrcode_url"),
      @Result(property = "limitCount", column = "limit_count"),
      @Result(property = "surveyTemplate", column = "survey_template"),
      @Result(property = "pclassType", column = "pclass_type"),
      @Result(property = "pclassProperty", column = "pclass_property"),
      @Result(property = "pclassType2", column = "pclass_type_2"),
      @Result(property = "courseName", column = "course_name"),
      @Result(property = "otherCourseName", column = "other_course_name"),
      @Result(property = "invitedFamilyCount", column = "invited_family_count"),
      @Result(property = "ncCode", column = "nc_code"),
      @Result(property = "lastModifyTime", column = "last_modify_time"),
      @Result(property = "uploadTime", column = "upload_time"),
      @Result(property = "uploadStatus", column = "upload_status"),
      @Result(property = "status", column = "status"),
      @Result(property = "onlineChannel", column = "online_channel"),
      @Result(property = "hospitalCode", column = "hospital_code"),
      @Result(property = "hospitalName", column = "hospital_name"),
      @Result(property = "isGodeepCity", column = "is_godeep_city"),
      @Result(property = "isSendCoupon", column = "is_send_coupon"),
      @Result(property = "businessReason", column = "business_reason"),
      @Result(property = "isOnlineActivity", column = "is_online_activity"),
      @Result(property = "activityType", column = "activity_type"),
      @Result(property = "actionType", column = "action_type"),
      @Result(property = "isDeleted", column = "is_deleted"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    PclassInfo getByIdEX(@Param("id") String id);

    @Select("SELECT id,classes_code,topic,start_time,end_time,owner_name,owner_mobile,owner_employee_num,director,director_mobile,director_exmployee_num,is_enabled,remark,province,city,place,address,longitude,latitude,expert_name,expert_introduce,hotline,qrcode_url,limit_count,survey_template,pclass_type,pclass_property,pclass_type_2,course_name,other_course_name,invited_family_count,nc_code,last_modify_time,upload_time,upload_status,status,online_channel,hospital_code,hospital_name,is_godeep_city,is_send_coupon,business_reason,is_online_activity,activity_type,action_type,is_deleted,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_info WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "pclassInfo-mapping")
    PclassInfo getByIdFilterIsDeleted(@Param("id") String id);

//get data by unique keys
    @Select("SELECT id,classes_code,topic,start_time,end_time,owner_name,owner_mobile,owner_employee_num,director,director_mobile,director_exmployee_num,is_enabled,remark,province,city,place,address,longitude,latitude,expert_name,expert_introduce,hotline,qrcode_url,limit_count,survey_template,pclass_type,pclass_property,pclass_type_2,course_name,other_course_name,invited_family_count,nc_code,last_modify_time,upload_time,upload_status,status,online_channel,hospital_code,hospital_name,is_godeep_city,is_send_coupon,business_reason,is_online_activity,activity_type,action_type,is_deleted,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_info WHERE classes_code=#{classesCode} ")
    @ResultMap(value = "pclassInfo-mapping")
    PclassInfo getByClassesCode(@Param("classesCode") String classesCode);

    @Select("SELECT id,classes_code,topic,start_time,end_time,owner_name,owner_mobile,owner_employee_num,director,director_mobile,director_exmployee_num,is_enabled,remark,province,city,place,address,longitude,latitude,expert_name,expert_introduce,hotline,qrcode_url,limit_count,survey_template,pclass_type,pclass_property,pclass_type_2,course_name,other_course_name,invited_family_count,nc_code,last_modify_time,upload_time,upload_status,status,online_channel,hospital_code,hospital_name,is_godeep_city,is_send_coupon,business_reason,is_online_activity,activity_type,action_type,is_deleted,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_info WHERE classes_code=#{classesCode} and is_enabled = true and is_deleted = false ")
    @ResultMap(value = "pclassInfo-mapping")
    PclassInfo getValidByClassesCode(@Param("classesCode") String classesCode);



//update status sqls
    @Update("update pclass_info set is_enabled=#{input} , updater=#{updater}, updated_by_user=#{updatedByUser} WHERE id=#{id}")
    int updateIsEnabled( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater ,@Param("updatedByUser") Integer updatedByUser );


    @Update("update pclass_info set is_godeep_city=#{input} , updater=#{updater}, updated_by_user=#{updatedByUser} WHERE id=#{id}")
    int updateIsGodeepCity( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater ,@Param("updatedByUser") Integer updatedByUser );


    @Update("update pclass_info set is_send_coupon=#{input} , updater=#{updater}, updated_by_user=#{updatedByUser} WHERE id=#{id}")
    int updateIsSendCoupon( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater ,@Param("updatedByUser") Integer updatedByUser );


    @Update("update pclass_info set is_online_activity=#{input} , updater=#{updater}, updated_by_user=#{updatedByUser} WHERE id=#{id}")
    int updateIsOnlineActivity( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater ,@Param("updatedByUser") Integer updatedByUser );


    @Update("update pclass_info set is_deleted=#{input} , updater=#{updater}, updated_by_user=#{updatedByUser} WHERE id=#{id}")
    int updateIsDeleted( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater ,@Param("updatedByUser") Integer updatedByUser );




//get data by foreign keys

}
