package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.SubChannelInfo;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltySubChannelMapper {
    @Select("select * from loyalty_register_subchannel where client_name =#{clientName} and is_enabled=#{isEnabled}")
    @Results(id = "result", value = {@Result(
            property = "id",
            column = "id"
    ), @Result(
            property = "clientName",
            column = "client_name"
    ), @Result(
            property = "registerSubChannelId",
            column = "register_sub_channelId"
    ), @Result(
            property = "logoImgFileName",
            column = "logo_img_file_name"
    ), @Result(
            property = "userAgreementFileName",
            column = "user_agreement_file_name"
    ), @Result(
            property = "isEnabled",
            column = "is_enabled"
    ), @Result(
            property = "creator",
            column = "created_by_user"
    ), @Result(
            property = "updater",
            column = "updated_by_user"
    ), @Result(
            property = "createTime",
            column = "create_time"
    ), @Result(
            property = "updateTime",
            column = "update_time"
    )})
    SubChannelInfo getSubChannelInfoByClientName(@Param("clientName") String clientName, @Param("isEnabled") String isEnabled);

    @Select("select * from loyalty_register_subchannel where client_name =#{clientName}")
    @ResultMap("result")
    SubChannelInfo getSubChannelIdByClientName(String clientName);
}
