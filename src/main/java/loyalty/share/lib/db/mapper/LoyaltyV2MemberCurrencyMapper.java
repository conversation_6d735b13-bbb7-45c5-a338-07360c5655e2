package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2MemberCurrency;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyV2MemberCurrencyMapper {

    @Insert("INSERT INTO loyalty_v2_member_currency(gift_id,cellphone,currency_amount,creator,updator,last_changed_by_add_id,last_changed_by_reduce_id)" +
            " VALUES(#{giftId},#{cellphone},#{currencyAmount},#{creator},#{creator},#{lastChangedByAddId},#{lastChangedByReduceId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2MemberCurrency loyaltyV2MemberCurrency);

    @Insert("INSERT IGNORE INTO loyalty_v2_member_currency(gift_id,cellphone,currency_amount,creator,updator,last_changed_by_add_id,last_changed_by_reduce_id)" +
            " VALUES(#{giftId},#{cellphone},#{currencyAmount},#{creator},#{updator},#{lastChangedByAddId},#{lastChangedByReduceId})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyV2MemberCurrency loyaltyV2MemberCurrency);

    @Insert("INSERT INTO loyalty_v2_member_currency(gift_id,cellphone,currency_amount,creator,updator,last_changed_by_add_id,last_changed_by_reduce_id)" +
            " VALUES(#{giftId},#{cellphone},#{currencyAmount},#{creator},#{updator},#{lastChangedByAddId},#{lastChangedByReduceId})" +
            " ON DUPLICATE KEY UPDATE gift_id=VALUES(gift_id),cellphone=VALUES(cellphone),currency_amount=currency_amount+#{currencyAmount},updator=VALUES(updator),last_changed_by_add_id=VALUES(last_changed_by_add_id),last_changed_by_reduce_id=VALUES(last_changed_by_reduce_id)  ")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyV2MemberCurrency loyaltyV2MemberCurrency);

    @Update("UPDATE loyalty_v2_member_currency set gift_id=#{giftId},cellphone=#{cellphone},currency_amount=#{currencyAmount},updator=#{updator},last_changed_by_add_id=#{lastChangedByAddId},last_changed_by_reduce_id=#{lastChangedByReduceId} WHERE id=#{id}")
    int updateById(LoyaltyV2MemberCurrency loyaltyV2MemberCurrency);

    @Delete("DELETE FROM loyalty_v2_member_currency WHERE id=#{id}" )
    int deleteById(LoyaltyV2MemberCurrency loyaltyV2MemberCurrency);


    @Select("SELECT * FROM loyalty_v2_member_currency WHERE id=#{id} ")
    @Results(id = "loyaltyV2MemberCurrency-mapping", value = {
    @Result(property = "id", column = "id"),@Result(property = "giftId", column = "gift_id"),@Result(property = "cellphone", column = "cellphone"),@Result(property = "currencyAmount", column = "currency_amount"),@Result(property = "creator", column = "creator"),@Result(property = "updator", column = "updator"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time"),@Result(property = "lastChangedByAddId", column = "last_changed_by_add_id"),@Result(property = "lastChangedByReduceId", column = "last_changed_by_reduce_id")
    })
    LoyaltyV2MemberCurrency getById(@Param("id") Long id);

    @Select("SELECT * FROM loyalty_v2_member_currency WHERE gift_id=#{giftId} and cellphone=#{userCellphone} ")
    @ResultMap("loyaltyV2MemberCurrency-mapping")
    LoyaltyV2MemberCurrency getMemberCurrencyByUserCellphone(@Param("giftId") Long giftId, @Param("userCellphone") String userCellphone);

	@Select("select * FROM loyalty_v2_member_currency WHERE cellphone=#{cellphone} and gift_id =#{giftId} limit 1 ")
	@ResultMap({"loyaltyV2MemberCurrency-mapping"})
	LoyaltyV2MemberCurrency findMemberCurrency(@Param("cellphone") String cellphone,@Param("giftId")Long giftId);
}
