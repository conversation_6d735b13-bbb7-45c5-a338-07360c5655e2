package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyBogofCampaignGiftDetail;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltyBogofCampaignGiftDetailMapper  {

     @Insert("INSERT IGNORE INTO loyalty_bogof_campaign_gift_detail(id,loyalty_bogof_campaign_record_id,gift_id,gift_inst_id,gift_name,gift_value,gift_img_url,gift_type,youan_order_sn,qr_code,trigger_time,creator,updater)" +
                " VALUES(#{id},#{loyaltyBogofCampaignRecordId},#{giftId},#{giftInstId},#{giftName},#{giftValue},#{giftImgUrl},#{giftType},#{youanOrderSn},#{qrCode},#{triggerTime},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyBogofCampaignGiftDetail loyaltyBogofCampaignGiftDetail);

    @Insert("INSERT INTO loyalty_bogof_campaign_gift_detail(id,loyalty_bogof_campaign_record_id,gift_id,gift_inst_id,gift_name,gift_value,gift_num,gift_img_url,gift_type,supplier,outbound_position,youan_order_sn,qr_code,trigger_time,creator,updater)" +
            " VALUES(#{id},#{loyaltyBogofCampaignRecordId},#{giftId},#{giftInstId},#{giftName},#{giftValue},#{giftNum},#{giftImgUrl},#{giftType},#{supplier},#{outboundPosition},#{youanOrderSn},#{qrCode},#{triggerTime},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertEntity(LoyaltyBogofCampaignGiftDetail loyaltyBogofCampaignGiftDetail);

    @Insert("INSERT INTO loyalty_bogof_campaign_gift_detail_black(id,loyalty_bogof_campaign_record_id,gift_id,gift_inst_id,gift_name,gift_value,gift_num,gift_img_url,gift_type,supplier,outbound_position,youan_order_sn,qr_code,trigger_time,creator,updater)" +
            " VALUES(#{id},#{loyaltyBogofCampaignRecordId},#{giftId},#{giftInstId},#{giftName},#{giftValue},#{giftNum},#{giftImgUrl},#{giftType},#{supplier},#{outboundPosition},#{youanOrderSn},#{qrCode},#{triggerTime},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertBlackEntity(LoyaltyBogofCampaignGiftDetail loyaltyBogofCampaignGiftDetail);

    @Insert("INSERT INTO loyalty_bogof_campaign_gift_detail(id,loyalty_bogof_campaign_record_id,gift_id,gift_inst_id,gift_name,gift_value,gift_img_url,youan_order_sn,qr_code,trigger_time,creator,updater)" +
                " VALUES(#{id},#{loyaltyBogofCampaignRecordId},#{giftId},#{giftInstId},#{giftName},#{giftValue},#{giftImgUrl},#{youanOrderSn},#{qrCode},#{triggerTime},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE gift_name=VALUES(gift_name),gift_value=VALUES(gift_value),gift_img_url=VALUES(gift_img_url),youan_order_sn=VALUES(youan_order_sn),qr_code=VALUES(qr_code),trigger_time=VALUES(trigger_time),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyBogofCampaignGiftDetail loyaltyBogofCampaignGiftDetail);

    @Update("UPDATE loyalty_bogof_campaign_gift_detail set loyalty_bogof_campaign_record_id=#{loyaltyBogofCampaignRecordId},gift_id=#{giftId},gift_inst_id=#{giftInstId},gift_name=#{giftName},gift_value=#{giftValue},gift_img_url=#{giftImgUrl},youan_order_sn=#{youanOrderSn},qr_code=#{qrCode},trigger_time=#{triggerTime},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LoyaltyBogofCampaignGiftDetail loyaltyBogofCampaignGiftDetail);

    @Delete("DELETE FROM loyalty_bogof_campaign_gift_detail WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_bogof_campaign_gift_detail set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,loyalty_bogof_campaign_record_id,gift_id,gift_inst_id,gift_name,gift_value,gift_img_url,youan_order_sn,qr_code,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_gift_detail WHERE id=#{id} ")
    @Results(id = "loyaltyBogofCampaignGiftDetail-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "loyaltyBogofCampaignRecordId", column = "loyalty_bogof_campaign_record_id"),
      @Result(property = "giftId", column = "gift_id"),
      @Result(property = "giftInstId", column = "gift_inst_id"),
      @Result(property = "giftName", column = "gift_name"),
      @Result(property = "giftValue", column = "gift_value"),
      @Result(property = "giftImgUrl", column = "gift_img_url"),
      @Result(property = "youanOrderSn", column = "youan_order_sn"),
      @Result(property = "qrCode", column = "qr_code"),
      @Result(property = "triggerTime", column = "trigger_time"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyBogofCampaignGiftDetail getByIdEX(@Param("id") Long id);

    @Select("SELECT id,loyalty_bogof_campaign_record_id,gift_id,gift_inst_id,gift_name,gift_value,gift_img_url,youan_order_sn,qr_code,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_gift_detail WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyBogofCampaignGiftDetail-mapping")
    LoyaltyBogofCampaignGiftDetail getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys
    @Select("SELECT id,loyalty_bogof_campaign_record_id,gift_id,gift_inst_id,gift_name,gift_value,gift_img_url,youan_order_sn,qr_code,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_gift_detail WHERE loyalty_bogof_campaign_record_id=#{loyaltyBogofCampaignRecordId} and gift_id=#{giftId} ")
    @ResultMap(value = "loyaltyBogofCampaignGiftDetail-mapping")
    LoyaltyBogofCampaignGiftDetail getByLoyaltyBogofCampaignRecordIdGiftId(@Param("loyaltyBogofCampaignRecordId") Long loyaltyBogofCampaignRecordId,@Param("giftId") Long giftId);



//update status sqls


//get data by foreign keys

}
