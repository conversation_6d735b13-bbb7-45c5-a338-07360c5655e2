package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.Log3rdPartyYouAnModel;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;

@Mapper
public interface Log3rdPartyYouAnMapper {

    @Options(useGeneratedKeys = true)
    @Insert(" INSERT INTO `loyalty_v2_log_3rdpartyyouancall` (`id`, `fail_record_table`, `fail_record_id`, `send_successed`, `update_successed`, `request_senario`, `request_url`, `request_body`, `response_body`, `creator`, `updator`)" +
            "  VALUES (#{id}, #{failRecordTable}, #{failRecordId}, #{sendSuccessed}, #{updateSuccessed}, #{requestSenario}, #{requestUrl}," +
            "  #{requestBody}, #{responseBody}, 'loyalty-mall-service', 'loyalty-mall-service'); ")
    void addLog3rdParty(Log3rdPartyYouAnModel model);
}
