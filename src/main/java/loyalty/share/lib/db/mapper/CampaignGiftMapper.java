package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.CampaignGift;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CampaignGiftMapper {

    @Select("SELECT * FROM campaign_gift where id=#{id} ")
    @Results(id = "giftMapping" , value = {
            @Result(column = "gift_name" , property = "giftName"),
            @Result(column = "gift_value" , property = "giftValue"),
            @Result(column = "create_time" , property = "createTime"),
            @Result(column = "update_time" , property = "updateTime"),
            @Result(column = "gift_series" , property = "series"),
            @Result(column = "external_gift_code", property = "externalGiftCode"),
            @Result(column = "sap_id" , property = "sapId")
    })
    List<CampaignGift> getById(@Param("id") Long id);

    @Select("SELECT * FROM campaign_gift where type=#{type} ")
    @ResultMap(value = "giftMapping" )
    List<CampaignGift> getByType(@Param("type") String type);


    @Select("SELECT * FROM campaign_gift where platform=#{platform} and GIFT_VALUE=#{giftValue} ")
    @ResultMap(value = "giftMapping" )
    CampaignGift getByGiftValueAndPlatform(@Param("platform") String platform,@Param("giftValue") String giftValue);
}