package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2MemberCurrencyReduce;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyV2MemberCurrencyReduceMapper {

	@Insert("INSERT INTO loyalty_v2_member_currency_reduce(currency_id,cellphone,member_grade,currency_amount,event_id,history_table_name,changed_by_history_id,changed_by_cmp_id,loyalty_channel_id,reason_code,migrate_id,migrate_key,trigger_time,creator,updator,user_tag_id,reduce_type,user_ip,user_agent)" +
			" VALUES(#{currencyId},#{cellphone},#{memberGrade},#{currencyAmount},#{eventId},#{historyTableName},#{changedByHistoryId},#{changedByCmpId},#{loyaltyChannelId},#{reasonCode},#{migrateId},#{migrateKey},#{triggerTime},#{creator},#{updator},#{userTagId},#{reduceType},#{userIp},#{userAgent})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2MemberCurrencyReduce loyaltyV2MemberCurrencyReduce);

	@Insert("INSERT IGNORE INTO loyalty_v2_member_currency_reduce(currency_id,cellphone,member_grade,currency_amount,event_id,history_table_name,changed_by_history_id,changed_by_cmp_id,loyalty_channel_id,reason_code,migrate_id,migrate_key,trigger_time,creator,updator,user_tag_id,reduce_type,user_ip,user_agent)" +
			" VALUES(#{currencyId},#{cellphone},#{memberGrade},#{currencyAmount},#{eventId},#{historyTableName},#{changedByHistoryId},#{changedByCmpId},#{loyaltyChannelId},#{reasonCode},#{migrateId},#{migrateKey},#{triggerTime},#{creator},#{updator},#{userTagId}),#{reduceType},#{userIp},#{userAgent}")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyV2MemberCurrencyReduce loyaltyV2MemberCurrencyReduce);

	@Insert("INSERT INTO loyalty_v2_member_currency_reduce(currency_id,cellphone,member_grade,currency_amount,event_id,history_table_name,changed_by_history_id,changed_by_cmp_id,loyalty_channel_id,reason_code,migrate_id,migrate_key,trigger_time,creator,updator,user_tag_id,reduce_type)" +
			" VALUES(#{currencyId},#{cellphone},#{memberGrade},#{currencyAmount},#{eventId},#{historyTableName},#{changedByHistoryId},#{changedByCmpId},#{loyaltyChannelId},#{reasonCode},#{migrateId},#{migrateKey},#{triggerTime},#{creator},#{updator},#{userTagId},#{reduceType})" +
			" ON DUPLICATE KEY UPDATE currency_id=VALUES(currency_id),cellphone=VALUES(cellphone),member_grade=VALUES(member_grade),currency_amount=VALUES(currency_amount),event_id=VALUES(event_id),history_table_name=VALUES(history_table_name),changed_by_history_id=VALUES(changed_by_history_id),changed_by_cmp_id=VALUES(changed_by_cmp_id),loyalty_channel_id=VALUES(loyalty_channel_id),reason_code=VALUES(reason_code),migrate_id=VALUES(migrate_id),migrate_key=VALUES(migrate_key),trigger_time=VALUES(trigger_time),updator=VALUES(updator),user_tag_id=VALUES(user_tag_id),reduce_type=VALUES(reduce_type)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyV2MemberCurrencyReduce loyaltyV2MemberCurrencyReduce);

	@Update("UPDATE loyalty_v2_member_currency_reduce set currency_id=#{currencyId},cellphone=#{cellphone},member_grade=#{memberGrade},currency_amount=#{currencyAmount},event_id=#{eventId},history_table_name=#{historyTableName},changed_by_history_id=#{changedByHistoryId},changed_by_cmp_id=#{changedByCmpId},loyalty_channel_id=#{loyaltyChannelId},reason_code=#{reasonCode},migrate_id=#{migrateId},migrate_key=#{migrateKey},trigger_time=#{triggerTime},updator=#{updator} WHERE id=#{id}")
    int updateById(LoyaltyV2MemberCurrencyReduce loyaltyV2MemberCurrencyReduce);

    @Delete("DELETE FROM loyalty_v2_member_currency_reduce WHERE id=#{id}" )
    int deleteById(LoyaltyV2MemberCurrencyReduce loyaltyV2MemberCurrencyReduce);


    @Select("SELECT * FROM loyalty_v2_member_currency_reduce WHERE id=#{id} ")
    @Results(id = "loyaltyV2MemberCurrencyReduce-mapping", value = {
    @Result(property = "id", column = "id"),@Result(property = "currencyId", column = "currency_id"),@Result(property = "cellphone", column = "cellphone"),@Result(property = "memberGrade", column = "member_grade"),@Result(property = "currencyAmount", column = "currency_amount"),@Result(property = "eventId", column = "event_id"),@Result(property = "historyTableName", column = "history_table_name"),@Result(property = "changedByHistoryId", column = "changed_by_history_id"),@Result(property = "changedByCmpId", column = "changed_by_cmp_id"),@Result(property = "loyaltyChannelId", column = "loyalty_channel_id"),@Result(property = "reasonCode", column = "reason_code"),@Result(property = "migrateId", column = "migrate_id"),@Result(property = "migrateKey", column = "migrate_key"),@Result(property = "triggerTime", column = "trigger_time"),@Result(property = "creator", column = "creator"),@Result(property = "updator", column = "updator"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time")
    })
    LoyaltyV2MemberCurrencyReduce getById(@Param("id") Long id);


	@Select({"<script>" +
			" select a.cellphone,a.currency_amount,a.trigger_time,r.reason_remark as reasonCode from " +
			" loyalty_v2_member_currency_reduce a LEFT JOIN loyalty_v2_dict_currency_reason r " +
			" on a.reason_code=r.reason_code where  1=1 "+
			"<when test=\"cellphone!=null and cellphone !='' \">",
			"  and a.cellphone=#{cellphone} ",
			"</when>",
			"<when test=\"giftId!=null and giftId !='' \">",
			"  and a.currency_id =#{giftId}  ",
			"</when>",
			"<when test=\"startAt !=null and startAt !='' \">",
			"  and a.trigger_time &gt;=#{startAt} ",
			"</when>",
			"<when test=\"endAt!=null and endAt !='' \">",
			"  and a.trigger_time &lt;= #{endAt} ",
			"</when>",
			"</script>"})
	@ResultMap({"loyaltyV2MemberCurrencyReduce-mapping"})
	List<LoyaltyV2MemberCurrencyReduce> findMemberCurrencyReduceList(@Param("cellphone") String cellphone, @Param("giftId")Long giftId, @Param("startAt")String startAt, @Param("endAt")String endAt);
}
