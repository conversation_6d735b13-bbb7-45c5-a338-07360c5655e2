package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallWorkOrderAttachment;
import org.apache.ibatis.annotations.*;

import java.util.*;

@Mapper
public interface LoyaltyMallWorkOrderAttachmentMapper{

    @Insert("INSERT INTO `loyalty_mall_work_order_attachment` ( `wo_id`, `attachment_type`,file_name,`creator`, `updator`)" +
            " VALUES ( #{woId},#{attachmentType}, #{fileName}, #{creator}, #{creator})")
    void add(LoyaltyMallWorkOrderAttachment loyaltyMallWorkOrderAttachment);


    @Select("select *from loyalty_mall_work_order_attachment where wo_id=#{woId}")
    @Results(value = {
            @Result(property = "id",column = "id"),
            @Result(property = "woId",column = "wo_id"),
            @Result(property = "attachmentType",column = "attachment_type"),
            @Result(property = "fileName",column = "file_name"),
            @Result(property = "creator",column = "creator"),
            @Result(property = "updator",column = "updator"),
            @Result(property = "createTime",column = "create_time"),
            @Result(property = "updateTime",column = "update_time"),

    })
    List<LoyaltyMallWorkOrderAttachment> queryLoyaltyMallWorkOrderAttachmentByOrderId(@Param("woId")long woId);

}
