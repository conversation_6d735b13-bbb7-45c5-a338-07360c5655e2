package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyCampaignShareDetail;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/6 下午 03:15
 * @describe
 */
@Mapper
public interface LoyaltyCampaignShareDetailMapper {
    @Insert("INSERT INTO loyalty_campaign_share_detail(assistance_cellphone, share_id, bind_time, is_nua, creator, " +
            "updater,username, unionid, openid)\n" +
            "VALUES (#{assistanceCellphone},#{shareId},#{bindTime},#{isNua},#{creator},#{updater},#{username},#{unionid}, #{openid})")
    int insert(LoyaltyCampaignShareDetail loyaltyCampaignShareDetail);

    @Select("select * from loyalty_campaign_share_detail where assistance_cellphone=#{cellphone}")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "assistanceCellphone", column = "assistance_cellphone"),
            @Result(property = "shareId", column = "share_id"),
            @Result(property = "bindTime", column = "bind_time"),
            @Result(property = "isNua", column = "is_nua"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "creator", column = "creator")
    })
    LoyaltyCampaignShareDetail getByAssistanceCellphone(@Param("cellphone") String cellphone);
}
