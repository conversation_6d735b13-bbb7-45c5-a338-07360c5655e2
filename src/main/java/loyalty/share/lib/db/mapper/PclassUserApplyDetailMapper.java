package loyalty.share.lib.db.mapper;


import java.util.Date;

import loyalty.share.lib.db.model.PclassUserApplyDetail;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface PclassUserApplyDetailMapper  {

     @Insert("INSERT IGNORE INTO pclass_user_apply_detail(id,cellphone,pnec_id,pclass_code,user_name,baby_birthday,attendance,trigger_time,status,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{pnecId},#{pclassCode},#{userName},#{babyBirthday},#{attendance},#{triggerTime},#{status},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(PclassUserApplyDetail pclassUserApplyDetail);

    @Insert("INSERT INTO pclass_user_apply_detail(id,cellphone,pnec_id,pclass_code,user_name,baby_birthday,attendance,trigger_time,status,creator,updater)" +
                " VALUES(#{id},#{cellphone},#{pnecId},#{pclassCode},#{userName},#{babyBirthday},#{attendance},#{triggerTime},#{status},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE pnec_id=VALUES(pnec_id),user_name=VALUES(user_name),baby_birthday=VALUES(baby_birthday),attendance=VALUES(attendance),trigger_time=VALUES(trigger_time),status=VALUES(status),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(PclassUserApplyDetail pclassUserApplyDetail);

    @Update("UPDATE pclass_user_apply_detail set cellphone=#{cellphone},pnec_id=#{pnecId},pclass_code=#{pclassCode},user_name=#{userName},baby_birthday=#{babyBirthday},attendance=#{attendance},trigger_time=#{triggerTime},status=#{status},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(PclassUserApplyDetail pclassUserApplyDetail);

    @Delete("DELETE FROM pclass_user_apply_detail WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update pclass_user_apply_detail set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,cellphone,pnec_id,pclass_code,user_name,baby_birthday,attendance,trigger_time,status,creator,updater,create_time,update_time FROM pclass_user_apply_detail WHERE id=#{id} ")
    @Results(id = "pclassUserApplyDetail-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "pnecId", column = "pnec_id"),
      @Result(property = "pclassCode", column = "pclass_code"),
      @Result(property = "userName", column = "user_name"),
      @Result(property = "babyBirthday", column = "baby_birthday"),
      @Result(property = "attendance", column = "attendance"),
      @Result(property = "triggerTime", column = "trigger_time"),
      @Result(property = "status", column = "status"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    PclassUserApplyDetail getByIdEX(@Param("id") Long id);

    @Select("SELECT id,cellphone,pnec_id,pclass_code,user_name,baby_birthday,attendance,trigger_time,status,creator,updater,create_time,update_time FROM pclass_user_apply_detail WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "pclassUserApplyDetail-mapping")
    PclassUserApplyDetail getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys
    @Select("SELECT id,cellphone,pnec_id,pclass_code,user_name,baby_birthday,attendance,trigger_time,status,creator,updater,create_time,update_time FROM pclass_user_apply_detail WHERE cellphone=#{cellphone} and pclass_code=#{pclassCode} ")
    @ResultMap(value = "pclassUserApplyDetail-mapping")
    PclassUserApplyDetail getByCellphonePclassCode(@Param("cellphone") String cellphone,@Param("pclassCode") String pclassCode);



//update status sqls


//get data by foreign keys

}
