package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltySunLampApiRecord;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 太阳码-小程序码生成记录Mapper接口
 */
@Mapper
public interface LoyaltySunLampApiRecordMapper {

    /**
     * 插入记录
     *
     * @param record 记录信息
     * @return 影响行数
     */
    @Insert("INSERT INTO loyalty_sunLamp_api_record (" +
            "appid, req_platform, req_id, url, req_param, req_body, resp_body, resp_code, is_suc, " +
            "trigger_time, creator, updator) VALUES (" +
            "#{appid},#{reqPlatform}, #{reqId}, #{url}, #{reqParam}, #{reqBody}, #{respBody}, #{respCode}, #{isSuc}, " +
            "#{triggerTime}, #{creator}, #{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltySunLampApiRecord record);

    /**
     * 根据ID查询记录
     *
     * @param id 记录ID
     * @return 记录信息
     */
    @Select("SELECT * FROM loyalty_sunLamp_api_record WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "reqPlatform", column = "req_platform"),
            @Result(property = "reqId", column = "req_id"),
            @Result(property = "url", column = "url"),
            @Result(property = "reqParam", column = "req_param"),
            @Result(property = "reqBody", column = "req_body"),
            @Result(property = "respBody", column = "resp_body"),
            @Result(property = "respCode", column = "resp_code"),
            @Result(property = "isSuc", column = "is_suc"),
            @Result(property = "triggerTime", column = "trigger_time"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    LoyaltySunLampApiRecord selectById(@Param("id") Long id);

    /**
     * 根据请求平台和请求ID查询记录
     *
     * @param reqPlatform 请求平台
     * @param reqId 请求ID
     * @return 记录信息
     */
    @Select("SELECT * FROM loyalty_sunLamp_api_record WHERE req_platform = #{reqPlatform} AND req_id = #{reqId}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "reqPlatform", column = "req_platform"),
            @Result(property = "reqId", column = "req_id"),
            @Result(property = "url", column = "url"),
            @Result(property = "reqParam", column = "req_param"),
            @Result(property = "reqBody", column = "req_body"),
            @Result(property = "respBody", column = "resp_body"),
            @Result(property = "respCode", column = "resp_code"),
            @Result(property = "isSuc", column = "is_suc"),
            @Result(property = "triggerTime", column = "trigger_time"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    LoyaltySunLampApiRecord selectByPlatformAndReqId(@Param("reqPlatform") String reqPlatform, @Param("reqId") String reqId);

    /**
     * 更新记录
     *
     * @param record 记录信息
     * @return 影响行数
     */
    @Update("<script>" +
            "UPDATE loyalty_sunLamp_api_record " +
            "<set>" +
            "<if test='reqPlatform != null'>req_platform = #{reqPlatform},</if>" +
            "<if test='reqId != null'>req_id = #{reqId},</if>" +
            "<if test='url != null'>url = #{url},</if>" +
            "<if test='reqParam != null'>req_param = #{reqParam},</if>" +
            "<if test='reqBody != null'>req_body = #{reqBody},</if>" +
            "<if test='respBody != null'>resp_body = #{respBody},</if>" +
            "<if test='respCode != null'>resp_code = #{respCode},</if>" +
            "<if test='isSuc != null'>is_suc = #{isSuc},</if>" +
            "<if test='triggerTime != null'>trigger_time = #{triggerTime},</if>" +
            "<if test='creator != null'>creator = #{creator},</if>" +
            "<if test='updator != null'>updator = #{updator},</if>" +
            "</set>" +
            "WHERE id = #{id}" +
            "</script>")
    int updateById(LoyaltySunLampApiRecord record);

    /**
     * 根据条件查询记录列表
     *
     * @param record 查询条件
     * @return 记录列表
     */
    @Select("<script>" +
            "SELECT * FROM loyalty_sunLamp_api_record " +
            "<where>" +
            "<if test='reqPlatform != null'>AND req_platform = #{reqPlatform}</if>" +
            "<if test='reqId != null'>AND req_id = #{reqId}</if>" +
            "<if test='url != null'>AND url = #{url}</if>" +
            "<if test='respCode != null'>AND resp_code = #{respCode}</if>" +
            "<if test='isSuc != null'>AND is_suc = #{isSuc}</if>" +
            "<if test='triggerTime != null'>AND trigger_time = #{triggerTime}</if>" +
            "<if test='creator != null'>AND creator = #{creator}</if>" +
            "<if test='updator != null'>AND updator = #{updator}</if>" +
            "</where>" +
            "ORDER BY create_time DESC" +
            "</script>")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "reqPlatform", column = "req_platform"),
            @Result(property = "reqId", column = "req_id"),
            @Result(property = "url", column = "url"),
            @Result(property = "reqParam", column = "req_param"),
            @Result(property = "reqBody", column = "req_body"),
            @Result(property = "respBody", column = "resp_body"),
            @Result(property = "respCode", column = "resp_code"),
            @Result(property = "isSuc", column = "is_suc"),
            @Result(property = "triggerTime", column = "trigger_time"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<LoyaltySunLampApiRecord> selectByCondition(LoyaltySunLampApiRecord record);

    /**
     * 根据请求ID查询记录
     *
     * @param reqId 请求ID
     * @return 记录信息
     */
    @Select("SELECT * FROM loyalty_sunLamp_api_record WHERE req_id = #{reqId} limit 1")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "reqPlatform", column = "req_platform"),
            @Result(property = "reqId", column = "req_id"),
            @Result(property = "url", column = "url"),
            @Result(property = "reqParam", column = "req_param"),
            @Result(property = "reqBody", column = "req_body"),
            @Result(property = "respBody", column = "resp_body"),
            @Result(property = "respCode", column = "resp_code"),
            @Result(property = "triggerTime", column = "trigger_time"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    LoyaltySunLampApiRecord selectByReqId(@Param("reqId") String reqId);
}