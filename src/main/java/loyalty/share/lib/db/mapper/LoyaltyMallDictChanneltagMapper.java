package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyMallDictChanneltag;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyMallDictChanneltagMapper {

    @Insert("INSERT INTO loyalty_mall_dict_channeltag(id,tag_name,tag_code,created_by_user,updated_by_user)" +
            " VALUES(#{id},#{tagName},#{tagCode},#{createdByUser},#{updatedByUser})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyMallDictChanneltag loyaltyMallDictChanneltag);

    @Insert("INSERT IGNORE INTO loyalty_mall_dict_channeltag(id,tag_name,tag_code,created_by_user,updated_by_user)" +
            " VALUES(#{id},#{tagName},#{tagCode},#{createdByUser},#{updatedByUser})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyMallDictChanneltag loyaltyMallDictChanneltag);

    @Insert("INSERT INTO loyalty_mall_dict_channeltag(id,tag_name,tag_code,created_by_user,updated_by_user)" +
            " VALUES(#{id},#{tagName},#{tagCode},#{createdByUser},#{updatedByUser})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),tag_name=VALUES(tag_name),tag_code=VALUES(tag_code),updated_by_user=VALUES(updated_by_user)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyMallDictChanneltag loyaltyMallDictChanneltag);

    @Update("UPDATE loyalty_mall_dict_channeltag set id=#{id},tag_name=#{tagName},tag_code=#{tagCode},updated_by_user=#{updatedByUser} WHERE id=#{id}")
    int updateById(LoyaltyMallDictChanneltag loyaltyMallDictChanneltag);

    @Delete("DELETE FROM loyalty_mall_dict_channeltag WHERE id=#{id}")
    int deleteById(LoyaltyMallDictChanneltag loyaltyMallDictChanneltag);


    @Select("SELECT * FROM loyalty_mall_dict_channeltag WHERE id=#{id} ")
    @Results(id = "loyaltyMallDictChanneltag-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "tagName", column = "tag_name"), @Result(property = "tagCode", column = "tag_code"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyMallDictChanneltag getById(@Param("id") Long id);

	@Select("SELECT * FROM loyalty_mall_dict_channeltag where tag_type='admin' ")
	@ResultMap({"loyaltyMallDictChanneltag-mapping"})
    List<LoyaltyMallDictChanneltag> queryList();
}
