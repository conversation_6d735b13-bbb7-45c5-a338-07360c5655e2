package loyalty.share.lib.db.mapper;


import java.util.Date;

import loyalty.share.lib.db.model.PclassScheduleJobLog;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface PclassScheduleJobLogMapper {

     @Insert("INSERT IGNORE INTO pclass_schedule_job_log(id,task_code,job_starttime,job_endtime,result,is_success,success_count,creator,updater,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{taskCode},#{jobStarttime},#{jobEndtime},#{result},#{isSuccess},#{successCount},#{creator},#{updater},#{createdByUser},#{updatedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(PclassScheduleJobLog pclassScheduleJobLog);

    @Insert("INSERT INTO pclass_schedule_job_log(id,task_code,job_starttime,job_endtime,result,is_success,success_count,creator,updater,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{taskCode},#{jobStarttime},#{jobEndtime},#{result},#{isSuccess},#{successCount},#{creator},#{updater},#{createdByUser},#{updatedByUser})" +
                " ON DUPLICATE KEY UPDATE task_code=VALUES(task_code),job_starttime=VALUES(job_starttime),job_endtime=VALUES(job_endtime),result=VALUES(result),is_success=VALUES(is_success),success_count=VALUES(success_count),updater=VALUES(updater),updated_by_user=VALUES(updated_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(PclassScheduleJobLog pclassScheduleJobLog);

    @Update("UPDATE pclass_schedule_job_log set task_code=#{taskCode},job_starttime=#{jobStarttime},job_endtime=#{jobEndtime},result=#{result},is_success=#{isSuccess},success_count=#{successCount},updater=#{updater},updated_by_user=#{updatedByUser} WHERE id=#{id}" )
    int updateByEntity(PclassScheduleJobLog pclassScheduleJobLog);

    @Delete("DELETE FROM pclass_schedule_job_log WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update pclass_schedule_job_log set is_deleted=true, updater=#{userKey}, updated_by_user=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,task_code,job_starttime,job_endtime,result,is_success,success_count,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_schedule_job_log WHERE id=#{id} ")
    @Results(id = "pclassScheduleJobLog-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "taskCode", column = "task_code"),
      @Result(property = "jobStarttime", column = "job_starttime"),
      @Result(property = "jobEndtime", column = "job_endtime"),
      @Result(property = "result", column = "result"),
      @Result(property = "isSuccess", column = "is_success"),
      @Result(property = "successCount", column = "success_count"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    PclassScheduleJobLog getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,task_code,job_starttime,job_endtime,result,is_success,success_count,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_schedule_job_log WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "pclassScheduleJobLog-mapping")
    PclassScheduleJobLog getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


//update status sqls


//get data by foreign keys

}
