package loyalty.share.lib.db.mapper;


import java.util.Date;

import loyalty.share.lib.db.model.PclassCouponInfo;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface PclassCouponInfoMapper {

     @Insert("INSERT IGNORE INTO pclass_coupon_info(id,pclass_code,first_coupon_type,first_coupon_content,first_coupon_address,first_coupon_num,first_coupon_expiration_date,sec_coupon_type,sec_coupon_content,sec_coupon_address,sec_coupon_num,sec_coupon_expiration_date,creator,updater,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{pclassCode},#{firstCouponType},#{firstCouponContent},#{firstCouponAddress},#{firstCouponNum},#{firstCouponExpirationDate},#{secCouponType},#{secCouponContent},#{secCouponAddress},#{secCouponNum},#{secCouponExpirationDate},#{creator},#{updater},#{createdByUser},#{updatedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(PclassCouponInfo pclassCouponInfo);

    @Insert("INSERT INTO pclass_coupon_info(id,pclass_code,first_coupon_type,first_coupon_content,first_coupon_address,first_coupon_num,first_coupon_expiration_date,sec_coupon_type,sec_coupon_content,sec_coupon_address,sec_coupon_num,sec_coupon_expiration_date,creator,updater,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{pclassCode},#{firstCouponType},#{firstCouponContent},#{firstCouponAddress},#{firstCouponNum},#{firstCouponExpirationDate},#{secCouponType},#{secCouponContent},#{secCouponAddress},#{secCouponNum},#{secCouponExpirationDate},#{creator},#{updater},#{createdByUser},#{updatedByUser})" +
                " ON DUPLICATE KEY UPDATE first_coupon_type=VALUES(first_coupon_type),first_coupon_content=VALUES(first_coupon_content),first_coupon_address=VALUES(first_coupon_address),first_coupon_num=VALUES(first_coupon_num),first_coupon_expiration_date=VALUES(first_coupon_expiration_date),sec_coupon_type=VALUES(sec_coupon_type),sec_coupon_content=VALUES(sec_coupon_content),sec_coupon_address=VALUES(sec_coupon_address),sec_coupon_num=VALUES(sec_coupon_num),sec_coupon_expiration_date=VALUES(sec_coupon_expiration_date),updater=VALUES(updater),updated_by_user=VALUES(updated_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(PclassCouponInfo pclassCouponInfo);

    @Update("UPDATE pclass_coupon_info set pclass_code=#{pclassCode},first_coupon_type=#{firstCouponType},first_coupon_content=#{firstCouponContent},first_coupon_address=#{firstCouponAddress},first_coupon_num=#{firstCouponNum},first_coupon_expiration_date=#{firstCouponExpirationDate},sec_coupon_type=#{secCouponType},sec_coupon_content=#{secCouponContent},sec_coupon_address=#{secCouponAddress},sec_coupon_num=#{secCouponNum},sec_coupon_expiration_date=#{secCouponExpirationDate},updater=#{updater},updated_by_user=#{updatedByUser} WHERE id=#{id}" )
    int updateByEntity(PclassCouponInfo pclassCouponInfo);

    @Delete("DELETE FROM pclass_coupon_info WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update pclass_coupon_info set is_deleted=true, updater=#{userKey}, updated_by_user=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,pclass_code,first_coupon_type,first_coupon_content,first_coupon_address,first_coupon_num,first_coupon_expiration_date,sec_coupon_type,sec_coupon_content,sec_coupon_address,sec_coupon_num,sec_coupon_expiration_date,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_coupon_info WHERE id=#{id} ")
    @Results(id = "pclassCouponInfo-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "pclassCode", column = "pclass_code"),
      @Result(property = "firstCouponType", column = "first_coupon_type"),
      @Result(property = "firstCouponContent", column = "first_coupon_content"),
      @Result(property = "firstCouponAddress", column = "first_coupon_address"),
      @Result(property = "firstCouponNum", column = "first_coupon_num"),
      @Result(property = "firstCouponExpirationDate", column = "first_coupon_expiration_date"),
      @Result(property = "secCouponType", column = "sec_coupon_type"),
      @Result(property = "secCouponContent", column = "sec_coupon_content"),
      @Result(property = "secCouponAddress", column = "sec_coupon_address"),
      @Result(property = "secCouponNum", column = "sec_coupon_num"),
      @Result(property = "secCouponExpirationDate", column = "sec_coupon_expiration_date"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    PclassCouponInfo getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,pclass_code,first_coupon_type,first_coupon_content,first_coupon_address,first_coupon_num,first_coupon_expiration_date,sec_coupon_type,sec_coupon_content,sec_coupon_address,sec_coupon_num,sec_coupon_expiration_date,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_coupon_info WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "pclassCouponInfo-mapping")
    PclassCouponInfo getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys
    @Select("SELECT id,pclass_code,first_coupon_type,first_coupon_content,first_coupon_address,first_coupon_num,first_coupon_expiration_date,sec_coupon_type,sec_coupon_content,sec_coupon_address,sec_coupon_num,sec_coupon_expiration_date,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_coupon_info WHERE pclass_code=#{pclassCode} ")
    @ResultMap(value = "pclassCouponInfo-mapping")
    PclassCouponInfo getByPclassCode(@Param("pclassCode") String pclassCode);



//update status sqls



}
