package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyBogofCampaignSunLampDetail;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface LoyaltyBogofCampaignSunLampDetailMapper {

    @Insert("INSERT INTO loyalty_bogof_campaign_sunlamp_detail(id,campaign_id,pnec_id,plat_form,image_url,skip_path,creator,updater)" +
            " VALUES(#{id},#{campaignId},#{pnecId},#{platForm},#{imageUrl},#{skipPath},#{creator},#{updater})")
    int insertEntity(LoyaltyBogofCampaignSunLampDetail loyaltyBogofCampaignSunLampDetail);

    @Select("SELECT image_url FROM loyalty_bogof_campaign_sunlamp_detail WHERE campaign_id = #{campaignId} and pnec_id = #{pnecId}")
    @Results(value = {
            @Result(column = "image_url",property = "imageUrl")
    })
    LoyaltyBogofCampaignSunLampDetail getByCampaignIdAndImageUrl(@Param("campaignId") String campaignId,@Param("pnecId") String pnecId);
}
