package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2GiftStorage;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyV2GiftStorageMapper {

    @Insert("INSERT INTO loyalty_v2_gift_storage(id,gift_id,storage_amount,last_update_by_history_id,last_update_by_event,creator,updator)" +
            " VALUES(#{id},#{giftId},#{storageAmount},#{lastUpdateByHistoryId},#{lastUpdateByEvent},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2GiftStorage loyaltyV2GiftStorage);

    @Insert("INSERT IGNORE INTO loyalty_v2_gift_storage(id,gift_id,storage_amount,last_update_by_history_id,last_update_by_event,creator,updator)" +
            " VALUES(#{id},#{giftId},#{storageAmount},#{lastUpdateByHistoryId},#{lastUpdateByEvent},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyV2GiftStorage loyaltyV2GiftStorage);

    @Insert("INSERT INTO loyalty_v2_gift_storage(id,gift_id,storage_amount,last_update_by_history_id,last_update_by_event,creator,updator)" +
            " VALUES(#{id},#{giftId},#{storageAmount},#{lastUpdateByHistoryId},#{lastUpdateByEvent},#{creator},#{updator})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),gift_id=VALUES(gift_id),storage_amount=VALUES(storage_amount),last_update_by_history_id=VALUES(last_update_by_history_id),last_update_by_event=VALUES(last_update_by_event),updator=VALUES(updator)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyV2GiftStorage loyaltyV2GiftStorage);

    @Update("UPDATE loyalty_v2_gift_storage set id=#{id},gift_id=#{giftId},storage_amount=#{storageAmount},last_update_by_history_id=#{lastUpdateByHistoryId},last_update_by_event=#{lastUpdateByEvent},updator=#{updator} WHERE id=#{id}")
    int updateById(LoyaltyV2GiftStorage loyaltyV2GiftStorage);

    @Delete("DELETE FROM loyalty_v2_gift_storage WHERE id=#{id}")
    int deleteById(LoyaltyV2GiftStorage loyaltyV2GiftStorage);

    @Delete("DELETE FROM loyalty_v2_gift_storage WHERE gift_id=#{giftId}")
    int deleteByGiftId(@Param("giftId") Long giftId);

    @Select("SELECT * FROM loyalty_v2_gift_storage WHERE id=#{id} ")
    @Results(id = "loyaltyV2GiftStorage-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "giftId", column = "gift_id"), @Result(property = "storageAmount", column = "storage_amount"), @Result(property = "lastUpdateByHistoryId", column = "last_update_by_history_id"), @Result(property = "lastUpdateByEvent", column = "last_update_by_event"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time"), @Result(property = "creator", column = "creator"), @Result(property = "updator", column = "updator")
    })
    LoyaltyV2GiftStorage getById(@Param("id") Long id);

    @Update("UPDATE loyalty_v2_gift_storage set storage_amount=#{storageAmount},updator=#{updator} WHERE gift_id=#{giftId}")
    int updateByGiftId(LoyaltyV2GiftStorage loyaltyV2GiftStorage);
}
