package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyMallWorkOrder;
import org.apache.ibatis.annotations.*;

import java.util.*;


@Mapper
public interface LoyaltyMallWorkOrderMapper{

    @Insert("INSERT INTO `loyalty_mall_work_order` ( `work_order_type`,channel_id, `cellphone`,union_id,question_description,antifake_codes, `submit_time`, `creator`, `updator`,user_ip,user_agent)" +
            " VALUES ( #{workOrderType},#{channelId},#{cellphone},#{unionId}, #{questionDescription},#{antifakeCodes}, #{submitTime}, #{creator}, #{creator},#{userIp},#{userAgent})")
    @Options(
            useGeneratedKeys = true,
            keyProperty = "id"
    )
    void add(LoyaltyMallWorkOrder loyaltyMallWorkOrder);


    @Select({"<script>" +
            "select *from loyalty_mall_work_order where 1=1 " +
            "<if test='cellphone != null and cellphone !=\"\" '>",
            " and cellphone = {cellphone} ",
            "</if>",
            "<if test='unionId != null and unionId !=\"\" '>",
            " and union_id = #{unionId} ",
            "</if>",
            "<if test='antifakeCode != null and antifakeCode !=\"\" '>",
            " and antifake_codes like '%${antifakeCode}%' ",
            "</if>",
            "<if test='workOrderType != null and workOrderType !=\"\" '>",
            " and work_order_type =#{workOrderType} ",
            "</if>",
            "<if test='state != null and state !=\"\" '>",
            " and state=#{state} ",
            "</if>",
            "<if test='submitTimeStart != null and submitTimeStart !=\"\" '>",
            " and submit_time &gt; #{submit_time} ",
            "</if>",
            "<if test='submitTimeEnd != null and submitTimeEnd !=\"\" '>",
            " and submit_time &lt; #{submit_time} ",
            "</if>",
            " order by submit_time asc "+
            "</script>"})
    @Results(value = {
            @Result(property = "id",column = "id"),
            @Result(property = "workOrderType",column = "work_order_type"),
            @Result(property = "channelId",column = "channel_id"),
            @Result(property = "union_id",column = "unionId"),
            @Result(property = "cellphone",column = "cellphone"),
            @Result(property = "questionDescription",column = "question_description"),
            @Result(property = "antifakeCodes",column = "antifake_codes"),
            @Result(property = "creator",column = "creator"),
            @Result(property = "updator",column = "updator"),
            @Result(property = "createTime",column = "create_time"),
            @Result(property = "updateTime",column = "update_time"),

    })
    List<LoyaltyMallWorkOrder> queryLoyaltyMallWorkOrderList(@Param("cellphone")String cellphone,@Param("unionId")String unionId,@Param("antifakeCode")String antifakeCode,
                                                             @Param("workOrderType")String workOrderType,@Param("state")String state,
                                                             @Param("submitTimeStart")String submitTimeStart,@Param("submitTimeEnd")String submitTimeEnd);

    @Update("update loyalty_mall_work_order_attachment set state=#{state},remark=#{remark} where id=#{id}")
    void updateState(@Param("state") int state,@Param("remark") String remark,@Param("id")long id);

}
