package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.BlackListInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface BlacklistUserMapper {
    @Select("select * from loyalty_blacklist_user where id= #{id}")
    @Results(value = {
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "type", column = "type"),
            @Result(property = "listType", column = "list_type")
    }
    )
    BlackListInfo getBlacklistById(@Param("id") Long id);

    @Select({
            "<script>",
            "select * from loyalty_blacklist_user",
            "<where>",
            "<if test='cellphone != null and cellphone != \"\" '>",
            " and cellphone = #{cellphone} ",
            "</if>",
            "<if test='type != null and type != \"\" '>",
            " and type = #{type} ",
            "</if>",
            "<if test='address != null and address != \"\" '>",
            " and address = #{address} ",
            "</if>",
            "<if test='listType != null and listType != \"\" '>",
            " and list_type = #{listType} ",
            "</if>",
            "</where>",
            "order by update_time desc",
            "</script>"
    })
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "type", column = "type"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "address", column = "address"),
            @Result(property = "based", column = "based"),
            @Result(property = "listType", column = "list_type")
    })
    List<BlackListInfo> queryByVars(@Param("cellphone") String cellphone, @Param("type") String type,@Param("address") String address, @Param("listType") String listType);


    @Select("select * from loyalty_blacklist_user where cellphone= #{cellphone} and type=#{type} and list_type=#{listType}")
    @Results(value = {
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "type", column = "type"),
            @Result(property = "listType", column = "list_type")
    }
    )
    BlackListInfo getBlacklistByCellphone(@Param("cellphone") String cellphone, @Param("type") String type, @Param("listType") String listType);

    @Select({
            "<script>",
            "select * from loyalty_blacklist_user",
            "<where>",
            "<if test='cellphone != null and cellphone != \"\" '>",
            " and cellphone like '%${cellphone}%' ",
            "</if>",
            "<if test='reason != null and reason != \"\" '>",
            " and reason like '%${reason}%' ",
            "</if>",
            "<if test='type != null and type != \"\" '>",
            " and type like '%${type}%' ",
            "</if>",
            "<if test='creator != null and creator != \"\" '>",
            " and creator = #{creator} ",
            "</if>",
            "<if test='startAt != null and startAt != \"\"'>",
            " and date_format(create_time,'%Y-%m-%d') &gt;= #{startAt} ",
            "</if>",
            "<if test='endAt != null and endAt != \"\"'>",
            " and date_format(create_time,'%Y-%m-%d') &lt;= #{endAt} ",
            "</if>",
            "<if test='address != null and address != \"\" '>",
            " and address like '%${address}%' ",
            "</if>",
            "<if test='based != null and based != \"\" '>",
            " and based = #{based} ",
            "</if>",
            "<if test='listType != null and listType != \"\" '>",
            " and list_type = #{listType} ",
            "</if>",
            "</where>",
            "order by update_time desc",
            "</script>"
    })
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "type", column = "type"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "address", column = "address"),
            @Result(property = "based", column = "based"),
            @Result(property = "listType", column = "list_type")
    })
    List<BlackListInfo> queryList(@Param("cellphone") String cellphone, @Param("reason") String reason, @Param("type") String type, @Param("creator") String creator, @Param("startAt") String startAt, @Param("endAt") String endAt,@Param("address") String address,@Param("based") String based, @Param("listType") String listType);

    @Delete("delete from loyalty_blacklist_user where id=#{id}")
    void deleteBlacklist(@Param("id") Long id);

    @Insert("INSERT INTO loyalty_blacklist_user(`cellphone`, `reason`, `type`,`creator`,`address`,`based`,`editor`,`list_type`) VALUES (#{cellphone}, #{reason}, #{type},#{userId},#{address},#{based},#{editor},#{listType})")
    void saveBlacklist(@Param("cellphone") String cellphone, @Param("reason") String reason, @Param("type") String type, @Param("userId") Long userId,@Param("address") String address,@Param("based") String based,@Param("editor") Long editor,@Param("listType") String listType);

    @Select({"<script>" +
            "select * from loyalty_blacklist_user where id in " +
            "<foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>" +
            "#{id} " +
            "</foreach>" +
            "</script>"})
    List<BlackListInfo> selectMoreBlacklists(@Param("ids") List<Long> ids);

    @Delete({"<script>" +
            "delete from loyalty_blacklist_user where id in " +
            "<foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>" +
            "#{id} " +
            "</foreach>" +
            "</script>"})
    void deleteMoreBlacklists(@Param("ids") List<Long> ids);

    @Select({"<script>",
            "select black.id,black.cellphone,black.reason,black.type,admin.user_name,black.create_time,black.update_time,black.address,black.based,black.list_type,editor_admin.user_name as editor from (loyalty_blacklist_user as black INNER JOIN loyalty_admin_user as admin on black.creator=admin.id) left JOIN loyalty_admin_user editor_admin on black.editor = editor_admin.id ",
            "<where>",
            "<if test='listType != null and listType != \"\" '>",
            " and list_type = #{listType} ",
            "</if>",
            "<if test='ids != null and ids.size>0'>",
            " and black.id in  ",
            "<foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>",
            "#{id} ",
            "</foreach>",
            "</if>",
            "</where>",
            "</script>"
    })
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "type", column = "type"),
            @Result(property = "creator", column = "user_name"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "address", column = "address"),
            @Result(property = "based", column = "based"),
            @Result(property = "listType", column = "list_type"),
            @Result(property = "editor", column = "editor")
    })
    List<BlackListInfo> exportBlacklist(@Param("ids") List<Long> ids, @Param("listType") String listType);



    @Select("<script>" +
            " select count(1) from loyalty_blacklist_user where type in  " +
            "<foreach item='type' collection='types' open='(' separator=',' close=')' >" +
            " #{type} "  +
            "</foreach>" +
            " and " +
            " ( " +
            "    cellphone= #{cellphone} " +
            "   <choose> " +
            "       <when test=\"address != null \"> " +
            "          or address = #{address}   " +
            "       </when> " +
            "   </choose> " +
            " ) " +
            " and list_type=#{listType} " +
            "</script>"
    )
    Integer getBlacklistByCellphoneOrAddress(@Param("cellphone") String cellphone,@Param("address") String address,
                                             @Param("types") List<String> types, @Param("listType") String listType);
}
