package loyalty.share.lib.db.mapper;


import java.util.Date;

import loyalty.share.lib.db.model.PclassPnecRel;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface PclassPnecRelMapper {

     @Insert("INSERT IGNORE INTO pclass_pnec_rel(id,pclass_code,pnec_id,creator,updater,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{pclassCode},#{pnecId},#{creator},#{updater},#{createdByUser},#{updatedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(PclassPnecRel pclassPnecRel);

    @Insert("INSERT INTO pclass_pnec_rel(id,pclass_code,pnec_id,creator,updater,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{pclassCode},#{pnecId},#{creator},#{updater},#{createdByUser},#{updatedByUser})" +
                " ON DUPLICATE KEY UPDATE updater=VALUES(updater),updated_by_user=VALUES(updated_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(PclassPnecRel pclassPnecRel);

    @Update("UPDATE pclass_pnec_rel set pclass_code=#{pclassCode},pnec_id=#{pnecId},updater=#{updater},updated_by_user=#{updatedByUser} WHERE id=#{id}" )
    int updateByEntity(PclassPnecRel pclassPnecRel);

    @Delete("DELETE FROM pclass_pnec_rel WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update pclass_pnec_rel set is_deleted=true, updater=#{userKey}, updated_by_user=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,pclass_code,pnec_id,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_pnec_rel WHERE id=#{id} ")
    @Results(id = "pclassPnecRel-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "pclassCode", column = "pclass_code"),
      @Result(property = "pnecId", column = "pnec_id"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    PclassPnecRel getByIdEX(@Param("id") Long id);

    @Select("SELECT id,pclass_code,pnec_id,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_pnec_rel WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "pclassPnecRel-mapping")
    PclassPnecRel getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys
    @Select("SELECT id,pclass_code,pnec_id,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_pnec_rel WHERE pclass_code=#{pclassCode} and pnec_id=#{pnecId} ")
    @ResultMap(value = "pclassPnecRel-mapping")
    PclassPnecRel getByPclassCodePnecId(@Param("pclassCode") String pclassCode,@Param("pnecId") String pnecId);



//update status sqls


//get data by foreign keys
    @Select("SELECT id,pclass_code,pnec_id,creator,updater,created_by_user,updated_by_user,create_time,update_time FROM pclass_pnec_rel WHERE pclass_code=#{pclassCode} ")
    @ResultMap(value = "pclassPnecRel-mapping")
    List<PclassPnecRel> getByPclassCode(@Param("pclassCode") String pclassCode);


}
