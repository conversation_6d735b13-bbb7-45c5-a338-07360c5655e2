package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallPageConfigs;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 */
@Mapper
public interface LoyaltyMallPageConfigsMapper {
    /**
     * 添加数据
     *
     * @param loyaltyMallPageConfigs loyaltyMallPageConfigs
     */
    @Options(useGeneratedKeys = true)
    @Insert("INSERT INTO loyalty_mall_page_configs(page_code,page_name,page_path,page_title,layout_type,backgroud_image,background_color,css_style,content,source_code,sort,extra,theme_id,remark,status,publish_time,is_enabled,is_editable,creator,updator) " +
            "VALUE (#{pageCode},#{pageName},#{pagePath},#{pageTitle},#{layoutType},#{backgroudImage},#{backgroundColor},#{cssStyle},#{content},#{sourceCode},#{sort},#{extra},#{themeId},#{remark},#{status},#{publishTime},#{isEnabled},#{isEditable},#{creator},#{updator})")
    void addLoyaltyMallPageConfigs(LoyaltyMallPageConfigs loyaltyMallPageConfigs);

    /**
     * 修改数据
     *
     * @param loyaltyMallPageConfigs LoyaltyMallPageConfigs
     * @return 修改的条数
     */
    @Update("UPDATE loyalty_mall_page_configs set page_code=#{pageCode},page_name=#{pageName},page_path=#{pagePath},page_title=#{pageTitle},layout_type=#{layoutType},backgroud_image=#{backgroudImage},background_color=#{backgroundColor},css_style=#{cssStyle},content=#{content},source_code=#{sourceCode},sort=#{sort},extra=#{extra},theme_id=#{themeId},remark=#{remark},status=#{status},publish_time=#{publishTime},is_enabled=#{isEnabled},is_editable=#{isEditable},updator=#{updator} WHERE id=#{id}")
    int updateByEntity(LoyaltyMallPageConfigs loyaltyMallPageConfigs);

    /**
     * 通过id 获取数据
     *
     * @param id 主键
     * @return LoyaltyMallPageConfigs
     */
    @Select("SELECT id,page_code,page_name,page_path,page_title,layout_type,backgroud_image,background_color,css_style,content,source_code,sort,extra,theme_id,remark,status,publish_time,is_enabled,is_editable,creator,updator,create_time,update_time FROM loyalty_mall_page_configs WHERE id =#{id} AND is_enabled = true")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "pageCode", column = "page_code"),
            @Result(property = "pageName", column = "page_name"),
            @Result(property = "pagePath", column = "page_path"),
            @Result(property = "pageTitle", column = "page_title"),
            @Result(property = "layoutType", column = "layout_type"),
            @Result(property = "backgroudImage", column = "backgroud_image"),
            @Result(property = "backgroundColor", column = "background_color"),
            @Result(property = "cssStyle", column = "css_style"),
            @Result(property = "content", column = "content"),
            @Result(property = "sourceCode", column = "source_code"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "extra", column = "extra"),
            @Result(property = "themeId", column = "theme_id"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "status", column = "status"),
            @Result(property = "publishTime", column = "publish_time"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "isEditable", column = "is_editable"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),

    })
    LoyaltyMallPageConfigs getById(@Param("id") Long id);

    /**
     * 通过pageCode 获取到数据
     *
     * @param pageCode pageCode
     * @return LoyaltyMallPageConfigs
     */
    @Select("SELECT id,page_code,page_name,page_path,page_title,layout_type,backgroud_image,background_color,css_style,content,source_code,sort,extra,theme_id,remark,status,publish_time,is_enabled,is_editable,creator,updator,create_time,update_time FROM loyalty_mall_page_configs WHERE page_code =#{pageCode} AND is_enabled = true")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "pageCode", column = "page_code"),
            @Result(property = "pageName", column = "page_name"),
            @Result(property = "pagePath", column = "page_path"),
            @Result(property = "pageTitle", column = "page_title"),
            @Result(property = "layoutType", column = "layout_type"),
            @Result(property = "backgroudImage", column = "backgroud_image"),
            @Result(property = "backgroundColor", column = "background_color"),
            @Result(property = "cssStyle", column = "css_style"),
            @Result(property = "content", column = "content"),
            @Result(property = "sourceCode", column = "source_code"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "extra", column = "extra"),
            @Result(property = "themeId", column = "theme_id"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "status", column = "status"),
            @Result(property = "publishTime", column = "publish_time"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "isEditable", column = "is_editable"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),

    })
    LoyaltyMallPageConfigs getByPageCode(@Param("pageCode") String pageCode);

    /**
     * 删除数据
     *
     * @param id 主键
     */
    @Delete("DELETE FROM loyalty_mall_page_configs WHERE id = #{id}")
    void deleteById(Long id);


}
