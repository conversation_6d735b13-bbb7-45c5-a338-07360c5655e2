package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyV2DictCurrencyReason;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyV2DictCurrencyReasonMapper {

    @Insert("INSERT INTO loyalty_v2_dict_currency_reason(id,reason_code,reason_remark,senario,created_by_user,updated_by_user)" +
            " VALUES(#{id},#{reasonCode},#{reasonRemark},#{senario},#{createdByUser},#{updatedByUser})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyV2DictCurrencyReason loyaltyV2DictCurrencyReason);

    @Insert("INSERT IGNORE INTO loyalty_v2_dict_currency_reason(id,reason_code,reason_remark,senario,created_by_user,updated_by_user)" +
            " VALUES(#{id},#{reasonCode},#{reasonRemark},#{senario},#{createdByUser},#{updatedByUser})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyV2DictCurrencyReason loyaltyV2DictCurrencyReason);

    @Insert("INSERT INTO loyalty_v2_dict_currency_reason(id,reason_code,reason_remark,senario,created_by_user,updated_by_user)" +
            " VALUES(#{id},#{reasonCode},#{reasonRemark},#{senario},#{createdByUser},#{updatedByUser})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),reason_code=VALUES(reason_code),reason_remark=VALUES(reason_remark),senario=VALUES(senario),updated_by_user=VALUES(updated_by_user)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyV2DictCurrencyReason loyaltyV2DictCurrencyReason);

    @Update("UPDATE loyalty_v2_dict_currency_reason set id=#{id},reason_code=#{reasonCode},reason_remark=#{reasonRemark},senario=#{senario},updated_by_user=#{updatedByUser} WHERE id=#{id}")
    int updateById(LoyaltyV2DictCurrencyReason loyaltyV2DictCurrencyReason);

    @Delete("DELETE FROM loyalty_v2_dict_currency_reason WHERE id=#{id}")
    int deleteById(LoyaltyV2DictCurrencyReason loyaltyV2DictCurrencyReason);


    @Select("SELECT * FROM loyalty_v2_dict_currency_reason WHERE id=#{id} ")
    @Results(id = "loyaltyV2DictCurrencyReason-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "reasonCode", column = "reason_code"), @Result(property = "reasonRemark", column = "reason_remark"), @Result(property = "senario", column = "senario"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyV2DictCurrencyReason getById(@Param("id") Long id);


    @Select({"SELECT * FROM loyalty_v2_dict_currency_reason"})
    @ResultMap(value = "loyaltyV2DictCurrencyReason-mapping")
    List<LoyaltyV2DictCurrencyReason> getAllCurrencyReasons();
}
