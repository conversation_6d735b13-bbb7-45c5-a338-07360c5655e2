package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyMallPageModule;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface LoyaltyMallPageModuleMapper {

     @Insert("INSERT IGNORE INTO loyalty_mall_page_module(id,module_name,module_group_id,module_type,module_title,module_desc,module_icon,module_selected_icon,main_content,background_color,background_image,font_color,css_style,link,sort,extra,remark,is_enabled,creator,updator)" +
                " VALUES(#{id},#{moduleName},#{moduleGroupId},#{moduleType},#{moduleTitle},#{moduleDesc},#{moduleIcon},#{moduleSelectedIcon},#{mainContent},#{backgroundColor},#{backgroundImage},#{fontColor},#{cssStyle},#{link},#{sort},#{extra},#{remark},#{isEnabled},#{creator},#{updator})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyMallPageModule loyaltyMallPageModule);

    @Insert("INSERT INTO loyalty_mall_page_module(id,module_name,module_group_id,module_type,module_title,module_desc,module_icon,module_selected_icon,main_content,background_color,background_image,font_color,css_style,link,sort,extra,remark,is_enabled,creator,updator)" +
                " VALUES(#{id},#{moduleName},#{moduleGroupId},#{moduleType},#{moduleTitle},#{moduleDesc},#{moduleIcon},#{moduleSelectedIcon},#{mainContent},#{backgroundColor},#{backgroundImage},#{fontColor},#{cssStyle},#{link},#{sort},#{extra},#{remark},#{isEnabled},#{creator},#{updator})" +
                " ON DUPLICATE KEY UPDATE module_name=VALUES(module_name),module_group_id=VALUES(module_group_id),module_type=VALUES(module_type),module_title=VALUES(module_title),module_desc=VALUES(module_desc),module_icon=VALUES(module_icon),module_selected_icon=VALUES(module_selected_icon),main_content=VALUES(main_content),background_color=VALUES(background_color),background_image=VALUES(background_image),font_color=VALUES(font_color),css_style=VALUES(css_style),link=VALUES(link),sort=VALUES(sort),extra=VALUES(extra),remark=VALUES(remark),is_enabled=VALUES(is_enabled),updator=VALUES(updator)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyMallPageModule loyaltyMallPageModule);

    @Update("UPDATE loyalty_mall_page_module set module_name=#{moduleName},module_group_id=#{moduleGroupId},module_type=#{moduleType},module_title=#{moduleTitle},module_desc=#{moduleDesc},module_icon=#{moduleIcon},module_selected_icon=#{moduleSelectedIcon},main_content=#{mainContent},background_color=#{backgroundColor},background_image=#{backgroundImage},font_color=#{fontColor},css_style=#{cssStyle},link=#{link},sort=#{sort},extra=#{extra},remark=#{remark},is_enabled=#{isEnabled},updator=#{updator} WHERE id=#{id}" )
    int updateByEntity(LoyaltyMallPageModule loyaltyMallPageModule);

    @Delete("DELETE FROM loyalty_mall_page_module WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_mall_page_module set is_deleted=true WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,module_name,module_group_id,module_type,module_title,module_desc,module_icon,module_selected_icon,main_content,background_color,background_image,font_color,css_style,link,sort,extra,remark,is_enabled,creator,updator,create_time,update_time FROM loyalty_mall_page_module WHERE id=#{id} ")
    @Results(id = "loyaltyMallPageModule-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "moduleName", column = "module_name"),
      @Result(property = "moduleGroupId", column = "module_group_id"),
      @Result(property = "moduleType", column = "module_type"),
      @Result(property = "moduleTitle", column = "module_title"),
      @Result(property = "moduleDesc", column = "module_desc"),
      @Result(property = "moduleIcon", column = "module_icon"),
      @Result(property = "moduleSelectedIcon", column = "module_selected_icon"),
      @Result(property = "mainContent", column = "main_content"),
      @Result(property = "backgroundColor", column = "background_color"),
      @Result(property = "backgroundImage", column = "background_image"),
      @Result(property = "fontColor", column = "font_color"),
      @Result(property = "cssStyle", column = "css_style"),
      @Result(property = "link", column = "link"),
      @Result(property = "sort", column = "sort"),
      @Result(property = "extra", column = "extra"),
      @Result(property = "remark", column = "remark"),
      @Result(property = "isEnabled", column = "is_enabled"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updator", column = "updator"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyMallPageModule getByIdEX(@Param("id") Long id);

    @Select("SELECT id,module_name,module_group_id,module_type,module_title,module_desc,module_icon,module_selected_icon,main_content,background_color,background_image,font_color,css_style,link,sort,extra,remark,is_enabled,creator,updator,create_time,update_time FROM loyalty_mall_page_module WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyMallPageModule-mapping")
    LoyaltyMallPageModule getByIdFilterIsDeleted(@Param("id") Long id);

    @Delete({
            "<script>",
            "DELETE FROM loyalty_mall_page_module WHERE id IN",
            "<foreach item='id' collection='ids' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    int deleteByIdsEX(@Param("ids") List<Long> ids);


    @Insert({
            "<script>",
            "INSERT INTO loyalty_mall_page_module (id, module_name, module_group_id, module_type, module_title, module_desc, module_icon, module_selected_icon, main_content, background_color, background_image, font_color, css_style, link, sort, extra, remark, is_enabled, creator, updator) ",
            "VALUES ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.id}, #{item.moduleName}, #{item.moduleGroupId}, #{item.moduleType}, #{item.moduleTitle}, #{item.moduleDesc}, #{item.moduleIcon}, #{item.moduleSelectedIcon}, #{item.mainContent}, #{item.backgroundColor}, #{item.backgroundImage}, #{item.fontColor}, #{item.cssStyle}, #{item.link}, #{item.sort}, #{item.extra}, #{item.remark}, #{item.isEnabled}, #{item.creator}, #{item.updator})",
            "</foreach>",
            "ON DUPLICATE KEY UPDATE ",
            "module_name=VALUES(module_name), ",
            "module_group_id=VALUES(module_group_id), ",
            "module_type=VALUES(module_type), ",
            "module_title=VALUES(module_title), ",
            "module_desc=VALUES(module_desc), ",
            "module_icon=VALUES(module_icon), ",
            "module_selected_icon=VALUES(module_selected_icon), ",
            "main_content=VALUES(main_content), ",
            "background_color=VALUES(background_color), ",
            "background_image=VALUES(background_image), ",
            "font_color=VALUES(font_color), ",
            "css_style=VALUES(css_style), ",
            "link=VALUES(link), ",
            "sort=VALUES(sort), ",
            "extra=VALUES(extra), ",
            "remark=VALUES(remark), ",
            "is_enabled=VALUES(is_enabled), ",
            "updator=VALUES(updator)",
            "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntities(@Param("list") List<LoyaltyMallPageModule> loyaltyMallPageModules);

    @Select("SELECT id,module_name,module_group_id,module_type,module_title,module_desc,module_icon,module_selected_icon,main_content,background_color,background_image,font_color,css_style,link,sort,extra,remark,is_enabled,creator,updator,create_time,update_time FROM loyalty_mall_page_module WHERE module_group_id=#{groupId} ")
    @ResultMap(value = "loyaltyMallPageModule-mapping")
    List<LoyaltyMallPageModule> getByGroupId(@Param("groupId") Integer groupId);

    @Delete("DELETE FROM loyalty_mall_page_module WHERE module_group_id=#{groupId}" )
    int deleteByGroupId(@Param("groupId") Integer groupId);
}
