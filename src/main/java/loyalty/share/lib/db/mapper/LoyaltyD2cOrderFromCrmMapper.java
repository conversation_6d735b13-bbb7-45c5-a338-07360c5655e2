package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.DBLoyaltyD2cOrderFromCrm;
import loyalty.share.lib.db.model.MemberAddBookModel;
import loyalty.share.lib.db.model.MemberReduceBookModel;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyD2cOrderFromCrmMapper {

    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert(" INSERT INTO `loyalty_d2c_order_from_crm`(`cellphone`, `order_no`, `is_enabled`, `is_add_pot`, `currency_add`) VALUES " +
            "(#{cellphone}, #{orderNo}, #{isEnabled}, #{isAddPot}, #{currencyAdd});")
    int addLoyaltyD2cOrderFromCrm(DBLoyaltyD2cOrderFromCrm model);

    @Update("UPDATE `loyalty_d2c_order_from_crm` SET   `is_enabled` = #{isEnabled}, `is_add_pot` = #{isAddPot},`currency_add` = #{currencyAdd} WHERE `order_no` = #{orderNo};")
    int updateLoyaltyD2cOrderFromCrm(DBLoyaltyD2cOrderFromCrm model);

    @Select(" select * from  `loyalty_d2c_order_from_crm` where is_enabled = 1 and  is_add_pot = 0 and create_time <=#{time}")
    @Results(id = "loyaltyD2cOrderFromCrm-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "orderNo", column = "order_no"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "isAddPot", column = "is_add_pot"),
            @Result(property = "currencyAdd", column = "currency_add")
    })
    List<DBLoyaltyD2cOrderFromCrm> getNeedAddPotList(@Param("time")String time);
}
