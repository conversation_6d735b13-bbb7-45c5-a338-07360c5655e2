package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyBogofCampaignApiRecord;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltyBogofCampaignApiRecordMapper  {

     @Insert("INSERT IGNORE INTO loyalty_bogof_campaign_api_record(id,api_platform,cellphone,loyalty_bogof_campaign_record_id,url,req_param,req_body,resp_body,resp_code,is_suc,trigger_time,creator,updater)" +
                " VALUES(#{id},#{apiPlatform},#{cellphone},#{loyaltyBogofCampaignRecordId},#{url},#{reqParam},#{reqBody},#{respBody},#{respCode},#{isSuc},#{triggerTime},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyBogofCampaignApiRecord loyaltyBogofCampaignApiRecord);

    @Insert("INSERT INTO loyalty_bogof_campaign_api_record(id,api_platform,cellphone,loyalty_bogof_campaign_record_id,url,req_param,req_body,resp_body,resp_code,is_suc,trigger_time,creator,updater)" +
            " VALUES(#{id},#{apiPlatform},#{cellphone},#{loyaltyBogofCampaignRecordId},#{url},#{reqParam},#{reqBody},#{respBody},#{respCode},#{isSuc},#{triggerTime},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertEntity(LoyaltyBogofCampaignApiRecord loyaltyBogofCampaignApiRecord);

    @Insert("INSERT INTO loyalty_bogof_campaign_api_record(id,api_platform,cellphone,loyalty_bogof_campaign_record_id,url,req_param,req_body,resp_body,resp_code,is_suc,trigger_time,creator,updater)" +
                " VALUES(#{id},#{apiPlatform},#{cellphone},#{loyaltyBogofCampaignRecordId},#{url},#{reqParam},#{reqBody},#{respBody},#{respCode},#{isSuc},#{triggerTime},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE api_platform=VALUES(api_platform),cellphone=VALUES(cellphone),loyalty_bogof_campaign_record_id=VALUES(loyalty_bogof_campaign_record_id),url=VALUES(url),req_param=VALUES(req_param),req_body=VALUES(req_body),resp_body=VALUES(resp_body),resp_code=VALUES(resp_code),is_suc=VALUES(is_suc),trigger_time=VALUES(trigger_time),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyBogofCampaignApiRecord loyaltyBogofCampaignApiRecord);

    @Update("UPDATE loyalty_bogof_campaign_api_record set api_platform=#{apiPlatform},cellphone=#{cellphone},loyalty_bogof_campaign_record_id=#{loyaltyBogofCampaignRecordId},url=#{url},req_param=#{reqParam},req_body=#{reqBody},resp_body=#{respBody},resp_code=#{respCode},is_suc=#{isSuc},trigger_time=#{triggerTime},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LoyaltyBogofCampaignApiRecord loyaltyBogofCampaignApiRecord);

    @Delete("DELETE FROM loyalty_bogof_campaign_api_record WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_bogof_campaign_api_record set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,api_platform,cellphone,loyalty_bogof_campaign_record_id,url,req_param,req_body,resp_body,resp_code,is_suc,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_api_record WHERE id=#{id} ")
    @Results(id = "loyaltyBogofCampaignApiRecord-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "apiPlatform", column = "api_platform"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "loyaltyBogofCampaignRecordId", column = "loyalty_bogof_campaign_record_id"),
      @Result(property = "url", column = "url"),
      @Result(property = "reqParam", column = "req_param"),
      @Result(property = "reqBody", column = "req_body"),
      @Result(property = "respBody", column = "resp_body"),
      @Result(property = "respCode", column = "resp_code"),
      @Result(property = "isSuc", column = "is_suc"),
      @Result(property = "triggerTime", column = "trigger_time"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyBogofCampaignApiRecord getByIdEX(@Param("id") Long id);

    @Select("SELECT id,api_platform,cellphone,loyalty_bogof_campaign_record_id,url,req_param,req_body,resp_body,resp_code,is_suc,trigger_time,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_api_record WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyBogofCampaignApiRecord-mapping")
    LoyaltyBogofCampaignApiRecord getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys


//update status sqls
    @Update("update loyalty_bogof_campaign_api_record set is_suc=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsSuc( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater );




//get data by foreign keys

}
