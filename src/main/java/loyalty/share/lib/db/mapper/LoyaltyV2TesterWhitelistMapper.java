package loyalty.share.lib.db.mapper;

import lombok.Data;
import lombok.ToString;
import loyalty.share.lib.db.model.LoyaltyV2TesterWhitelist;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

@Mapper
public interface LoyaltyV2TesterWhitelistMapper {

    @Select({
            "<script>",
            "select * from loyalty_v2_tester_whitelist where cellphone=#{cellphone} and enabled=1 " ,
            " and (channel = #{channel} or channel is null) ",
            "</script>"
    })
    public LoyaltyV2TesterWhitelist getByCellphoneAndChannel(@Param("cellphone") String cellphone,@Param("channel") String channel);
}
