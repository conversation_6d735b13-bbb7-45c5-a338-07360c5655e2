package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallChangeCellphoneLog;
import loyalty.share.lib.db.model.LoyaltyMallFQA;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyMallChangeCellphoneLogMapper {

    @Insert("INSERT INTO `loyalty_mall_change_cellphone_log` (`id`, `change_from_cellphone`, `change_to_cellphone`, `create_time`, `update_time`) " +
            "VALUES (#{id}, #{changeFromCellphone}, #{changeToCellphone},now(), now())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyMallChangeCellphoneLog changeCellphoneLog);


}
