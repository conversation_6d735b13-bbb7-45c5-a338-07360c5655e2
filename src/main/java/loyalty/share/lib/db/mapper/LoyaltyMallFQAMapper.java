package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallFQA;
import org.apache.ibatis.annotations.*;

import java.util.*;

@Mapper
public interface LoyaltyMallFQAMapper {

    @Select(" select loyalty_mall_fqa.*,loyalty_mall_channeltag_resource.channel_id as channel_id from loyalty_mall_fqa INNER JOIN loyalty_mall_channeltag_resource ON " +
		    " loyalty_mall_fqa.id = loyalty_mall_channeltag_resource.resource_id and loyalty_mall_channeltag_resource.resource_type='FQA'" +
		    " where loyalty_mall_channeltag_resource.tag_id =#{tagId} and  " +
		    " loyalty_mall_channeltag_resource.channel_id =#{channelId} order by loyalty_mall_fqa.create_time desc")
    @Results(value = {
            @Result(property = "id",column = "id"),
            @Result(property = "title",column = "title"),
            @Result(property = "channelId",column = "channel_id"),
            @Result(property = "type",column = "type"),
            @Result(property = "content",column = "content"),
            @Result(property = "createTime",column = "create_time"),
            @Result(property = "updateTime",column = "update_time"),
    })
    List<LoyaltyMallFQA> queryLoyaltyMallFQAList(@Param("tagId")Long tagId,@Param("channelId") String channelId);

    @Select(" select id,`type`,title,content,is_enabled,create_time,update_time from loyalty_mall_fqa WHERE is_enabled = 1 order by create_time desc")
    @Results(value = {
            @Result(property = "id",column = "id"),
            @Result(property = "title",column = "title"),
            @Result(property = "type",column = "type"),
            @Result(property = "content",column = "content"),
            @Result(property = "createTime",column = "create_time"),
            @Result(property = "updateTime",column = "update_time")
    })
    List<LoyaltyMallFQA> queryLoyaltyMallFQAListNew();
}
