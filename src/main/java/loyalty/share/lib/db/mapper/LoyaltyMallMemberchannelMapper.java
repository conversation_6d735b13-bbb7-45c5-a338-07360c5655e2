package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyMallMemberchannel;
import loyalty.share.lib.db.model.LoyaltyV2MemberCurrency;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyMallMemberchannelMapper {

    @Insert("INSERT INTO loyalty_mall_memberchannel(id,cellphone,sync_tag_id,manual_tag_id,current_tag_id,current_tag_type,sync_time,can_sync,last_modified_by,last_modifer_id,last_modify_remark,last_modify_time,creator,updator)" +
            " VALUES(#{id},#{cellphone},#{syncTagId},#{manualTagId},#{currentTagId},#{currentTagType},#{syncTime},#{canSync},#{lastModifiedBy},#{lastModiferId},#{lastModifyRemark},#{lastModifyTime},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyMallMemberchannel loyaltyMallMemberchannel);

    @Insert("INSERT IGNORE INTO loyalty_mall_memberchannel(id,cellphone,sync_tag_id,manual_tag_id,current_tag_id,current_tag_type,sync_time,can_sync,last_modified_by,last_modifer_id,last_modify_remark,last_modify_time,creator,updator)" +
            " VALUES(#{id},#{cellphone},#{syncTagId},#{manualTagId},#{currentTagId},#{currentTagType},#{syncTime},#{canSync},#{lastModifiedBy},#{lastModiferId},#{lastModifyRemark},#{lastModifyTime},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnore(LoyaltyMallMemberchannel loyaltyMallMemberchannel);

    @Insert("INSERT INTO loyalty_mall_memberchannel(id,cellphone,sync_tag_id,manual_tag_id,current_tag_id,current_tag_type,sync_time,can_sync,last_modified_by,last_modifer_id,last_modify_remark,last_modify_time,creator,updator)" +
            " VALUES(#{id},#{cellphone},#{syncTagId},#{manualTagId},#{currentTagId},#{currentTagType},#{syncTime},#{canSync},#{lastModifiedBy},#{lastModiferId},#{lastModifyRemark},#{lastModifyTime},#{creator},#{updator})" +
            " ON DUPLICATE KEY UPDATE id=VALUES(id),cellphone=VALUES(cellphone),sync_tag_id=VALUES(sync_tag_id),manual_tag_id=VALUES(manual_tag_id),current_tag_id=VALUES(current_tag_id),current_tag_type=VALUES(current_tag_type),sync_time=VALUES(sync_time),can_sync=VALUES(can_sync),last_modified_by=VALUES(last_modified_by),last_modifer_id=VALUES(last_modifer_id),last_modify_remark=VALUES(last_modify_remark),last_modify_time=VALUES(last_modify_time),updator=VALUES(updator)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdate(LoyaltyMallMemberchannel loyaltyMallMemberchannel);

    @Insert("INSERT INTO loyalty_mall_memberchannel(cellphone,sync_tag_id,manual_tag_id,current_tag_id,current_tag_type,sync_time,can_sync,last_modified_by,last_modifer_id,last_modify_remark,last_modify_time,creator,updator) " +
            "VALUES(#{cellphone},#{syncTagId},#{manualTagId},#{currentTagId},#{currentTagType},#{syncTime},#{canSync},#{lastModifiedBy},#{lastModiferId},#{lastModifyRemark},now(),#{updator},#{updator}) " +
            "ON DUPLICATE KEY UPDATE manual_tag_id=VALUES(manual_tag_id),current_tag_id=VALUES(current_tag_id),current_tag_type=VALUES(current_tag_type),can_sync=VALUES(can_sync),last_modified_by=VALUES(last_modified_by),last_modifer_id=VALUES(last_modifer_id),last_modify_remark=VALUES(last_modify_remark),last_modify_time=VALUES(last_modify_time),updator=VALUES(updator) ")
    void insertUpdateLoyaltyMallMemberchannel(LoyaltyMallMemberchannel loyaltyMallMemberchannel);

    @Update("UPDATE loyalty_mall_memberchannel set id=#{id},cellphone=#{cellphone},sync_tag_id=#{syncTagId},manual_tag_id=#{manualTagId},current_tag_id=#{currentTagId},current_tag_type=#{currentTagType},sync_time=#{syncTime},can_sync=#{canSync},last_modified_by=#{lastModifiedBy},last_modifer_id=#{lastModiferId},last_modify_remark=#{lastModifyRemark},last_modify_time=#{lastModifyTime},updator=#{updator} WHERE id=#{id}")
    int updateById(LoyaltyMallMemberchannel loyaltyMallMemberchannel);

    @Delete("DELETE FROM loyalty_mall_memberchannel WHERE id=#{id}")
    int deleteById(LoyaltyMallMemberchannel loyaltyMallMemberchannel);


    @Select("SELECT * FROM loyalty_mall_memberchannel WHERE id=#{id} ")
    @Results(id = "loyaltyMallMemberchannel-mapping", value = {
            @Result(property = "id", column = "id"), @Result(property = "cellphone", column = "cellphone"), @Result(property = "syncTagId", column = "sync_tag_id"), @Result(property = "manualTagId", column = "manual_tag_id"), @Result(property = "currentTagId", column = "current_tag_id"), @Result(property = "currentTagType", column = "current_tag_type"), @Result(property = "syncTime", column = "sync_time"), @Result(property = "canSync", column = "can_sync"), @Result(property = "lastModifiedBy", column = "last_modified_by"), @Result(property = "lastModiferId", column = "last_modifer_id"), @Result(property = "lastModifyRemark", column = "last_modify_remark"), @Result(property = "lastModifyTime", column = "last_modify_time"), @Result(property = "creator", column = "creator"), @Result(property = "updator", column = "updator"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyMallMemberchannel getById(@Param("id") Long id);

	@Select("SELECT\n" +
			"\tloyalty_mall_dict_channeltag.tag_code\n" +
			"FROM\n" +
			"\tloyalty_mall_memberchannel\n" +
			"LEFT JOIN loyalty_mall_dict_channeltag on loyalty_mall_memberchannel.current_tag_id = loyalty_mall_dict_channeltag.id \n" +
			"where loyalty_mall_memberchannel.cellphone=#{cellphone} ")
	String getByCellphone(@Param("cellphone") String cellphone);

	@Select("SELECT * FROM loyalty_mall_memberchannel WHERE cellphone=#{cellphone} ")
	@ResultMap({"loyaltyMallMemberchannel-mapping"})
	LoyaltyMallMemberchannel getMemberChannelByCellphone(@Param("cellphone") String cellphone);

    @Select("SELECT count(1) FROM loyalty_v2_campaign_scan_result WHERE cellphone=#{cellphone} ")
    @ResultType(Long.class)
    Long getMemberScanRecordByCellphone(@Param("cellphone") String cellphone);

    @Select(" select count(1) from loyalty_mall_register_map where channel =#{channel}")
    @ResultType(Long.class)
    Long getRegisterType(@Param("channel") String channel);
}
