package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.DBAnswer;
import loyalty.share.lib.db.model.DBQuestionInfo;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyQuestionMapper {

    @Insert("insert into loyalty_questionnaire_question (campaign_id,question,type,is_enabled,series,question_analysis) values (#{campaignId},#{question},#{type},#{IsEnabled},#{series},#{questionAnalysis})")
    @SelectKey(statement = "select LAST_INSERT_ID()", keyProperty = "id", before = false, resultType = Integer.class)
    Integer saveQuestion(DBQuestionInfo dbQuestionInfo);


    @Insert("insert into loyalty_questionnaire_answer (type,answer,is_true,question_id) values (#{dbAnswer.type},#{dbAnswer.answer},#{dbAnswer.isTrue},#{dbAnswer.questionId}) ")
    void saveAnswer(@Param("dbAnswer") DBAnswer dbAnswer);


    @Delete("delete from loyalty_questionnaire_question where id=#{id}")
    void deleteQuestion(Integer id);

    @Delete("delete from loyalty_questionnaire_answer where question_id=#{id}")
    void deleteAnswer(Integer id);

    @Select("select is_Enabled from loyalty_questionnaire_question where id=#{id}")
    Integer isEnabled(Integer id);

    @Update("update loyalty_questionnaire_question set campaign_id=#{campaignId},question=#{question},type=#{type},is_enabled=#{IsEnabled},series=#{series},question_analysis=#{questionAnalysis} where id=#{id}")
    void updateQuestion(DBQuestionInfo dbQuestionInfo);

    @Update("update loyalty_questionnaire_answer set type=#{type},answer=#{answer},is_true=#{isTrue} where id=#{id} ")
    void updateAnswer( DBAnswer dbAnswer);

    @Select("select id from loyalty_questionnaire_question where id=#{questionId}")
    Boolean questionIdIsExist(Integer questionId);


}
