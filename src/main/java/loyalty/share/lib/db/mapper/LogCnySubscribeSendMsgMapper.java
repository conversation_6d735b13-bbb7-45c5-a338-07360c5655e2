package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LogCnySubscribeSendMsg;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LogCnySubscribeSendMsgMapper {

    @Insert("INSERT IGNORE INTO log_cny_subscribe_send_msg(id,cny_subscribe_msg_info_id,send_data,is_suc,response_msg,creator,updater)" +
            " VALUES(#{id},#{pclassSubscribeMsgInfoId},#{sendData},#{isSuc},#{responseMsg},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(LogCnySubscribeSendMsg logCnySubscribeSendMsg);

    @Insert("INSERT INTO log_cny_subscribe_send_msg(id,cny_subscribe_msg_info_id,send_data,is_suc,response_msg,creator,updater)" +
            " VALUES(#{id},#{pclassSubscribeMsgInfoId},#{sendData},#{isSuc},#{responseMsg},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE cny_subscribe_msg_info_id=VALUES(cny_subscribe_msg_info_id),send_data=VALUES(send_data),is_suc=VALUES(is_suc),response_msg=VALUES(response_msg),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(LogCnySubscribeSendMsg logCnySubscribeSendMsg);

    @Update("UPDATE log_cny_subscribe_send_msg set cny_subscribe_msg_info_id=#{pclassSubscribeMsgInfoId},send_data=#{sendData},is_suc=#{isSuc},response_msg=#{responseMsg},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LogCnySubscribeSendMsg logCnySubscribeSendMsg);

    @Delete("DELETE FROM log_cny_subscribe_send_msg WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update log_cny_subscribe_send_msg set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,cny_subscribe_msg_info_id,send_data,is_suc,response_msg,creator,updater,create_time,update_time FROM log_cny_subscribe_send_msg WHERE id=#{id} ")
    @Results(id = "logCnySubscribeSendMsg-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "pclassSubscribeMsgInfoId", column = "cny_subscribe_msg_info_id"),
            @Result(property = "sendData", column = "send_data"),
            @Result(property = "isSuc", column = "is_suc"),
            @Result(property = "responseMsg", column = "response_msg"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    LogCnySubscribeSendMsg getByIdEX(@Param("id") Long id);

    @Select("SELECT id,cny_subscribe_msg_info_id,send_data,is_suc,response_msg,creator,updater,create_time,update_time FROM log_cny_subscribe_send_msg WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "logCnySubscribeSendMsg-mapping")
    LogCnySubscribeSendMsg getByIdFilterIsDeleted(@Param("id") Long id);




//get data by foreign keys

}
