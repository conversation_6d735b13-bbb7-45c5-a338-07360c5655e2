package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyAdminUser;
import loyalty.share.lib.db.model.LoyaltyAdminUserRole;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyAdminUserMapper {

    @Insert("INSERT INTO loyalty_admin_user(id,user_name,cellphone,mail_box,remark,password,is_enabled,creator,updator)" +
            " VALUES(#{id},#{userName},#{cellphone},#{mailBox},#{remark},#{password},#{isEnabled},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyAdminUser loyaltyAdminUser);

     @Insert("INSERT IGNORE INTO loyalty_admin_user(id,user_name,cellphone,mail_box,remark,password,is_enabled,creator,updator)" +
                " VALUES(#{id},#{userName},#{cellphone},#{mailBox},#{remark},#{password},#{isEnabled},#{creator},#{updator})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyAdminUser loyaltyAdminUser);

    @Insert("INSERT INTO loyalty_admin_user(id,user_name,cellphone,mail_box,remark,password,is_enabled,creator,updator)" +
                " VALUES(#{id},#{userName},#{cellphone},#{mailBox},#{remark},#{password},#{isEnabled},#{creator},#{updator})" +
                " ON DUPLICATE KEY UPDATE id=VALUES(id),user_name=VALUES(user_name),cellphone=VALUES(cellphone),mail_box=VALUES(mail_box),remark=VALUES(remark),password=VALUES(password),is_enabled=VALUES(is_enabled),updator=VALUES(updator)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyAdminUser loyaltyAdminUser);

    @Update("UPDATE loyalty_admin_user set id=#{id},user_name=#{userName},cellphone=#{cellphone},mail_box=#{mailBox},remark=#{remark},password=#{password},is_enabled=#{isEnabled},updator=#{updator} WHERE id=#{id}" )
    int updateById(LoyaltyAdminUser loyaltyAdminUser);

    @Delete("DELETE FROM loyalty_admin_user WHERE id=#{id}" )
    int deleteById(LoyaltyAdminUser loyaltyAdminUser);


    @Select("SELECT * FROM loyalty_admin_user WHERE id=#{id} ")
    @Results(id = "loyaltyAdminUser-mapping", value = {
    @Result(property = "id", column = "id"),@Result(property = "userName", column = "user_name"),@Result(property = "cellphone", column = "cellphone"),@Result(property = "mailBox", column = "mail_box"),@Result(property = "remark", column = "remark"),@Result(property = "password", column = "password"),@Result(property = "isEnabled", column = "is_enabled"),@Result(property = "creator", column = "creator"),@Result(property = "updator", column = "updator"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time")
    })
    LoyaltyAdminUser getById(@Param("id") Long id);

	@Select("select * from loyalty_admin_user where user_name = #{userName} ")
	@ResultMap({"loyaltyAdminUser-mapping"})
	LoyaltyAdminUser getAdminUserByUserName(@Param("userName") String userName);

	@Delete("delete from loyalty_admin_user_auth where user_id = #{userId}")
	void deleteAuthInfosByUser(@Param("userId") Long userId);

	@Update(" update loyalty_admin_user set is_enabled = #{isEnabled}, updator=#{optUserId} where id=#{userId}")
	void updateUserStatus(@Param("userId") Long userId, @Param("optUserId") Long optUserId, @Param("isEnabled") Boolean isEnabled);


    /*@Insert("insert into loyalty_admin_user_yang(user_name,password,mobile,remake,nick_name,email,creator,updator) values(#{userName},#{password},#{mobile},#{remake},#{nickName},#{email},#{creator},#{updator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void createUser(LoyaltyAdminUser user);

    @Update("update loyalty_admin_user_yang set user_name = #{userName},mobile = #{mobile},status = #{status},remake = #{remake},nick_name=#{nickName},email=#{email},updator=#{updator} where id = #{id}")
    void updateUser(LoyaltyAdminUser user);

    @Update("update loyalty_admin_user_yang set status = #{status} ,updator=#{updator} where id = #{userId}")
    void changeStatus(@Param("userId") Long userId, @Param("status") int status,@Param("updator") String updator);

    @Select("select loyalty_admin_user_role.id,loyalty_admin_user_role.name from loyalty_admin_user_auth ru join loyalty_admin_user_role on ru.role_id = loyalty_admin_user_role.id where loyalty_admin_user_role.id = 1 and user_id = #{userId}")
    LoyaltyAdminUserRole selectAdministrator(@Param("userId") Long userId);

    @Delete("delete from loyalty_admin_user_auth_yang where user_id = #{userId}")
    void deleteRealease(@Param("userId") Long userId);

    @Select("select `loyalty_admin_user_yang`.*,IF(loyalty_admin_user_auth_yang.role_id=1,true,false) AS isAdmin from `loyalty_admin_user_yang` left join loyalty_admin_user_auth_yang on `loyalty_admin_user_yang`.id=loyalty_admin_user_auth_yang.user_id where `loyalty_admin_user_yang`.id=#{id} ORDER BY isAdmin DESC LIMIT 1 ")
    @ResultType(LoyaltyAdminUser.class)
    LoyaltyAdminUser getById(@Param("id") Long id);

    @Select("select * from loyalty_admin_user_yang where user_name=#{account} and status=1 ")
    @ResultType(LoyaltyAdminUser.class)
    LoyaltyAdminUser getByAccount(@Param("account") String account);

    @Update("update loyalty_admin_user_yang set password = #{newPassword} where id = #{userId}")
    void changePassword(@Param("userId") long userId, @Param("newPassword") String newPassword);

    @Select("select user_id from loyalty_admin_user_auth_yang where role_id=#{roleId}")
    @ResultType(Long.class)
    List<Long> getUserListByRole(@Param("roleId") Long roleId);*/

    @Select("select user_id from loyalty_admin_user_auth where role_id=#{roleId}")
    @ResultType(Long.class)
    List<Long> getUserListByRole(@Param("roleId") Long roleId);
}
