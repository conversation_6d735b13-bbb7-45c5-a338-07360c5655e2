package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyCampaignShare;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/6 下午 03:15
 * @describe
 */
@Mapper
public interface LoyaltyCampaignShareMapper {
    @Insert("INSERT INTO loyalty_campaign_share(campaign_id, owner_cellphone, is_full, trigger_time, updater, " +
            "creator, unionid, openid)\n" +
            "VALUES (#{campaignId},#{ownerCellphone},#{isFull},#{triggerTime},#{updater},#{creator},#{unionid}, #{openid})")
    @Options(useGeneratedKeys = true)
    int insert(LoyaltyCampaignShare loyaltyCampaignShare);

    @Select("SELECT id,\n" +
            "       campaign_id,\n" +
            "       owner_cellphone,\n" +
            "       is_full,\n" +
            "       wxa_code,\n" +
            "       trigger_time,\n" +
            "       create_time,\n" +
            "       update_time,\n" +
            "       updater,\n" +
            "       creator, unionid, openid " +
            "FROM loyalty_campaign_share\n" +
            "WHERE owner_cellphone = #{cellphone}")
    @Results(id = "LoyaltyCampaignShare-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "campaignId", column = "campaign_id"),
            @Result(property = "ownerCellphone", column = "owner_cellphone"),
            @Result(property = "isFull", column = "is_full"),
            @Result(property = "triggerTime", column = "trigger_time"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "wxaCode", column = "wxa_code"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "unionid", column = "unionid"),
            @Result(property = "openid", column = "openid")
    })
    LoyaltyCampaignShare selectByCellphone(String cellphone);

    @Update("UPDATE loyalty_campaign_share\n" +
            "SET campaign_id=#{campaignId},owner_cellphone=#{ownerCellphone},is_full=#{isFull}," +
            "trigger_time=#{triggerTime},wxa_code=#{wxaCode}\n" +
            "WHERE id = #{id}")
    int update(LoyaltyCampaignShare loyaltyCampaignShare);


    @Select("SELECT id,\n" +
            "       campaign_id,\n" +
            "       owner_cellphone,\n" +
            "       is_full,\n" +
            "       wxa_code,\n" +
            "       trigger_time,\n" +
            "       create_time,\n" +
            "       update_time,\n" +
            "       updater,\n" +
            "       creator, unionid, openid " +
            "FROM loyalty_campaign_share\n" +
            "WHERE id = #{id}")
    @ResultMap("LoyaltyCampaignShare-mapping")
    LoyaltyCampaignShare selectById(@Param("id") Long shareId);
}
