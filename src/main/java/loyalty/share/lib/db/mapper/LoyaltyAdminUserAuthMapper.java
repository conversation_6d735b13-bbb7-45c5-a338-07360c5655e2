package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyAdminUserAuth;

import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface LoyaltyAdminUserAuthMapper {

    @Insert("INSERT INTO loyalty_admin_user_auth(user_id,role_id,authorized_by_user)" +
            " VALUES(#{userId},#{roleId},#{authorizedByUser})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyAdminUserAuth loyaltyAdminUserAuth);

     @Insert("INSERT IGNORE INTO loyalty_admin_user_auth(user_id,role_id,authorized_by_user)" +
                " VALUES(,#{userId},#{roleId},#{authorizedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnore(LoyaltyAdminUserAuth loyaltyAdminUserAuth);

    @Insert("INSERT INTO loyalty_admin_user_auth(id,user_id,role_id,authorized_by_user)" +
                " VALUES(#{id},#{userId},#{roleId},#{authorizedByUser})" +
                " ON DUPLICATE KEY UPDATE id=VALUES(id),user_id=VALUES(user_id),role_id=VALUES(role_id),authorized_by_user=VALUES(authorized_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdate(LoyaltyAdminUserAuth loyaltyAdminUserAuth);

    @Update("UPDATE loyalty_admin_user_auth set id=#{id},user_id=#{userId},role_id=#{roleId},authorized_by_user=#{authorizedByUser} WHERE id=#{id}" )
    int updateById(LoyaltyAdminUserAuth loyaltyAdminUserAuth);

    @Delete("DELETE FROM loyalty_admin_user_auth WHERE id=#{id}" )
    int deleteById(LoyaltyAdminUserAuth loyaltyAdminUserAuth);


    @Select("SELECT * FROM loyalty_admin_user_auth WHERE id=#{id} ")
    @Results(id = "loyaltyAdminUserAuth-mapping", value = {
    @Result(property = "id", column = "id"),@Result(property = "userId", column = "user_id"),@Result(property = "roleId", column = "role_id"),@Result(property = "authorizedByUser", column = "authorized_by_user"),@Result(property = "createTime", column = "create_time"),@Result(property = "updateTime", column = "update_time"),@Result(property = "key", column = "key"),@Result(property = "key", column = "key"),@Result(property = "key", column = "key"),@Result(property = "constraint", column = "constraint"),@Result(property = "constraint", column = "constraint"),@Result(property = "constraint", column = "constraint")
    })
    LoyaltyAdminUserAuth getById(@Param("id") Long id);
}
