package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallPageModuleGroup;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 */
@Mapper
public interface LoyaltyMallPageModuleGroupMapper {

    /**
     * 添加数据
     *
     * @param loyaltyMallPageModuleGroup loyaltyMallPageModuleGroup
     */
    @Options(useGeneratedKeys = true)
    @Insert("INSERT INTO loyalty_mall_page_module_group(group_name,backgroud_image,background_color,css_style,sort,extra,remark,is_enabled,creator,updator) " +
            "VALUE (#{groupName},#{backgroudImage},#{backgroundColor},#{cssStyle},#{sort},#{extra},#{remark},#{isEnabled},#{creator},#{updator})")
    void addLoyaltyMallPageModuleGroup(LoyaltyMallPageModuleGroup loyaltyMallPageModuleGroup);

    /**
     * 修改数据
     *
     * @param loyaltyMallPageModuleGroup loyaltyMallPageModuleGroup
     * @return 修改的条数
     */
    @Update("UPDATE loyalty_mall_page_module_group set group_name=#{groupName},backgroud_image=#{backgroudImage},background_color=#{backgroundColor},css_style=#{cssStyle},sort=#{sort},extra=#{extra},remark=#{remark},is_enabled=#{isEnabled},updator=#{updator} WHERE id=#{id}")
    int updateByEntity(LoyaltyMallPageModuleGroup loyaltyMallPageModuleGroup);

    /**
     * 通过id 查询数据
     *
     * @param id 主键id
     * @return LoyaltyMallPageModuleGroup
     */
    @Results(id = "LoyaltyMallPageGroupRel-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "groupName", column = "group_name"),
            @Result(property = "backgroudImage", column = "backgroud_image"),
            @Result(property = "backgroundColor", column = "background_color"),
            @Result(property = "cssStyle", column = "css_style"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "extra", column = "extra"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    @Select("SELECT id,group_name,backgroud_image,background_color,css_style,sort,extra,remark,is_enabled,creator,updator,create_time,update_time FROM loyalty_mall_page_module_group WHERE id = #{id} AND is_enabled = true")
    LoyaltyMallPageModuleGroup getById(@Param("id") Long id);

    /**
     * 删除数据
     *
     * @param id 主键id
     */
    @Delete("DELETE FROM loyalty_mall_page_module_group WHERE id = #{id}")
    void deleteById(Long id);

}
