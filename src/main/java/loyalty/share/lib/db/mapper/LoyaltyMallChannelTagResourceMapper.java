package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyMallChannelTagResource;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyMallChannelTagResourceMapper {

    @Insert("INSERT INTO `loyalty_mall_channeltag_resource` (`resource_id`, `resource_type`, `tag_id`, `channel_id`, `created_by_user`, `updated_by_user`, `create_time`, `update_time`) " +
		    "VALUES (#{resourceId}, #{resourceType}, #{tagId}, #{channelId}, #{createdByUser}, #{updatedByUser}, #{createTime}, #{updateTime});")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyaltyMallChannelTagResource loyaltyMallChannelTagResource);

	@Insert("INSERT INTO loyalty_mall_channeltag_resource(resource_id,resource_type,tag_id,channel_id,created_by_user,updated_by_user)" +
			" VALUES(#{resourceId},#{resourceType},#{tagId},#{channelId},#{createdByUser},#{updatedByUser})" +
			" ON DUPLICATE KEY UPDATE resource_id=VALUES(resource_id),resource_type=VALUES(resource_type)," +
			" tag_id=VALUES(tag_id),channel_id=VALUES(channel_id),created_by_user=VALUES(created_by_user),updated_by_user=VALUES(updated_by_user)  ")
	@Options(useGeneratedKeys = true, keyProperty = "id")
	int insertUpdate(LoyaltyMallChannelTagResource loyaltyMallChannelTagResource);

	@Delete("DELETE FROM loyalty_mall_channeltag_resource WHERE resource_id=#{resourceId} and resource_type=#{type}" )
	int deleteByIdAndType(@Param("resourceId")Long resourceId,@Param("type")String type);
}
