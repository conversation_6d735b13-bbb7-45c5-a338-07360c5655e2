package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallMemberCartModel;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyMallMemberCartMapper {

    @Options(useGeneratedKeys = true)
    @Insert("INSERT INTO loyalty_mall_member_cart (channel_id,cellphone,commodity_id,size_comb_id,commodity_amount,add_time,creator,updator,create_time,update_time) " +
            "VALUE (#{channelId},#{cellphone},#{commodityId},#{sizeCombId},#{commodityAmount},#{addTime},#{creator},#{updator},now(),now())")
    int addMemberCart(LoyaltyMallMemberCartModel model);

    @Select("SELECT * FROM loyalty_mall_member_cart WHERE cellphone=#{cellphone} AND channel_id=#{channelId} ")
    @Results(id = "memberCart-mapping", value = {
            @Result(column = "commodity_id", property = "commodityId"),
            @Result(column = "size_comb_id", property = "sizeCombId"),
            @Result(column = "commodity_amount", property = "commodityAmount"),
            @Result(column = "add_time", property = "addTime"),
            @Result(column = "channel_id", property = "channelId")
    })
    List<LoyaltyMallMemberCartModel> queryMemberCartList(@Param("cellphone") String cellphone, @Param("channelId") Long channelId);
}
