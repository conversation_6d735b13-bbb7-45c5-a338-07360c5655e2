package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyMallLink;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltyMallLinkMapper {

     @Insert("INSERT IGNORE INTO loyalty_mall_link(id,type,title,link,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{type},#{title},#{link},#{createdByUser},#{updatedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyMallLink loyaltyMallLink);

    @Insert("INSERT INTO loyalty_mall_link(id,type,title,link,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{type},#{title},#{link},#{createdByUser},#{updatedByUser})" +
                " ON DUPLICATE KEY UPDATE type=VALUES(type),title=VALUES(title),link=VALUES(link),updated_by_user=VALUES(updated_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyMallLink loyaltyMallLink);

    @Update("UPDATE loyalty_mall_link set type=#{type},title=#{title},link=#{link},updated_by_user=#{updatedByUser} WHERE id=#{id}" )
    int updateByEntity(LoyaltyMallLink loyaltyMallLink);

    @Delete("DELETE FROM loyalty_mall_link WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update loyalty_mall_link set is_deleted=true, updated_by_user=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,type,title,link,created_by_user,updated_by_user,create_time,update_time FROM loyalty_mall_link WHERE id=#{id} ")
    @Results(id = "loyaltyMallLink-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "type", column = "type"),
      @Result(property = "title", column = "title"),
      @Result(property = "link", column = "link"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyMallLink getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,type,title,link,created_by_user,updated_by_user,create_time,update_time FROM loyalty_mall_link WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyMallLink-mapping")
    LoyaltyMallLink getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


//update status sqls


//get data by foreign keys

}
