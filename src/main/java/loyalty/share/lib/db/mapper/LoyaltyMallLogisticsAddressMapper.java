package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyMallLogisticsAddress;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface LoyaltyMallLogisticsAddressMapper {

    @Options(useGeneratedKeys = true)
    @Insert("INSERT INTO loyalty_mall_logistics_address(channel_id,user_cellphone,country,province,city,area,road,add_detail,room_number,recv_cellphone,recv_name,is_default,creator,updator) " +
            "VALUE  (#{channelId},#{userCellphone},#{country},#{province},#{city},#{area},#{road},#{detail},#{roomNumber},#{recvCellphone},#{recvName},#{isDefault},#{creator},#{creator})")
    void addMemberAddress(LoyaltyMallLogisticsAddress model);

    @Update("UPDATE loyalty_mall_logistics_address " +
            "SET country=#{country},province=#{province},city=#{city},area=#{area},road=#{road},add_detail=#{detail},room_number=#{roomNumber},recv_cellphone=#{recvCellphone},recv_name=#{recvName},is_default=#{isDefault},updator=#{updator} " +
            "WHERE id=#{id} ")
    void updateMemberAddress(LoyaltyMallLogisticsAddress model);

    @Delete("DELETE FROM loyalty_mall_logistics_address WHERE ID=#{addressId} ")
    void deleteMemberAddress(Long addressId);

    @Update("UPDATE loyalty_mall_logistics_address SET is_default=#{status} WHERE user_cellphone=#{cellphone} AND channel_id=#{channelId} ")
    void updateAllAddressDefault(@Param("cellphone") String cellphone, @Param("status") Boolean status,@Param("channelId")Long channelId);

    @Select("SELECT * FROM loyalty_mall_logistics_address WHERE ID=#{id} ")
    @Results(id = "address-mapping", value = {
            @Result(property = "userCellphone", column = "user_cellphone"),
            @Result(property = "detail", column = "add_detail"),
            @Result(property = "roomNumber", column = "room_number"),
            @Result(property = "recvCellphone", column = "recv_cellphone"),
            @Result(property = "recvName", column = "recv_name"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "channelId", column = "channel_id"),
            @Result(property = "isDefault", column = "is_default")
    })
    LoyaltyMallLogisticsAddress queryUserAddressInfo(Long addressId);

    @Select("SELECT * FROM loyalty_mall_logistics_address WHERE user_cellphone=#{cellphone} AND channel_id in (#{channelId}) ")
    @ResultMap(value = "address-mapping")
    List<LoyaltyMallLogisticsAddress> queryUserAddressList(@Param("cellphone") String cellphone, @Param("channelId") String channelId);
}
