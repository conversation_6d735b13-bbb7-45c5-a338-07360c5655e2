package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyBogofCampaignProductDetail;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface LoyaltyBogofCampaignProductDetailMapper {

     @Insert("INSERT IGNORE INTO loyalty_bogof_campaign_product_detail(id,campaign_record_id,campaign_product_scan_id,traceability_code,digitcode,product_sapid,creator,updater)" +
                " VALUES(#{id},#{campaignRecordId},#{campaignProductScanId},#{traceabilityCode},#{digitcode},#{productSapid},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyBogofCampaignProductDetail loyaltyBogofCampaignProductDetail);

    @Insert("INSERT INTO loyalty_bogof_campaign_product_detail(id,campaign_record_id,campaign_product_scan_id,traceability_code,digitcode,product_sapid,creator,updater)" +
            " VALUES(#{id},#{campaignRecordId},#{campaignProductScanId},#{traceabilityCode},#{digitcode},#{productSapid},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertEntity(LoyaltyBogofCampaignProductDetail loyaltyBogofCampaignProductDetail);

    @Insert("INSERT INTO loyalty_bogof_campaign_product_detail_black(id,campaign_record_id,campaign_product_scan_id,traceability_code,digitcode,product_sapid,creator,updater)" +
            " VALUES(#{id},#{campaignRecordId},#{campaignProductScanId},#{traceabilityCode},#{digitcode},#{productSapid},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertBlackEntity(LoyaltyBogofCampaignProductDetail loyaltyBogofCampaignProductDetail);

    @Insert("INSERT INTO loyalty_bogof_campaign_product_detail(id,campaign_record_id,campaign_product_scan_id,traceability_code,digitcode,product_sapid,creator,updater)" +
                " VALUES(#{id},#{campaignRecordId},#{campaignProductScanId},#{traceabilityCode},#{digitcode},#{productSapid},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE campaign_record_id=VALUES(campaign_record_id),campaign_product_scan_id=VALUES(campaign_product_scan_id),traceability_code=VALUES(traceability_code),digitcode=VALUES(digitcode),product_sapid=VALUES(product_sapid),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyBogofCampaignProductDetail loyaltyBogofCampaignProductDetail);

    @Update("UPDATE loyalty_bogof_campaign_product_detail set campaign_record_id=#{campaignRecordId},campaign_product_scan_id=#{campaignProductScanId},traceability_code=#{traceabilityCode},digitcode=#{digitcode},product_sapid=#{productSapid},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(LoyaltyBogofCampaignProductDetail loyaltyBogofCampaignProductDetail);

    @Delete("DELETE FROM loyalty_bogof_campaign_product_detail WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_bogof_campaign_product_detail set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,campaign_record_id,campaign_product_scan_id,traceability_code,digitcode,product_sapid,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_product_detail WHERE id=#{id} ")
    @Results(id = "loyaltyBogofCampaignProductDetail-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "campaignRecordId", column = "campaign_record_id"),
      @Result(property = "campaignProductScanId", column = "campaign_product_scan_id"),
      @Result(property = "traceabilityCode", column = "traceability_code"),
      @Result(property = "digitcode", column = "digitcode"),
      @Result(property = "productSapid", column = "product_sapid"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyBogofCampaignProductDetail getByIdEX(@Param("id") Long id);

    @Select("SELECT id,campaign_record_id,campaign_product_scan_id,traceability_code,digitcode,product_sapid,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_product_detail WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyBogofCampaignProductDetail-mapping")
    LoyaltyBogofCampaignProductDetail getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys


//update status sqls


//get data by foreign keys
    @Select("SELECT id,campaign_record_id,campaign_product_scan_id,traceability_code,digitcode,product_sapid,creator,updater,create_time,update_time FROM loyalty_bogof_campaign_product_detail WHERE campaign_record_id=#{campaignRecordId} ")
    @ResultMap(value = "loyaltyBogofCampaignProductDetail-mapping")
    List<LoyaltyBogofCampaignProductDetail> getByCampaignRecordId(@Param("campaignRecordId") Long campaignRecordId);


}
