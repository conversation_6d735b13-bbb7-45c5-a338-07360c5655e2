package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyatlyCnySubscribeMsgInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/5 下午 02:33
 * @describe
 */
@Mapper
public interface LoyatlyCnySubscribeMsgInfoMapper {
    @Insert("INSERT INTO loyatly_cny_subscribe_msg_info(id, cellphone, unionid, openid, share_id, campaign_id,\n" +
        "                                           wxmpp_subscribe_msg_info_id, is_send, creator, updater)\n" +
        "VALUES (#{id},#{cellphone},#{unionid},#{openid},#{shareId},#{campaignId},#{wxmppSubscribeMsgInfoId}," +
        "#{isSend},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoyatlyCnySubscribeMsgInfo loyatlyCnySubscribeMsgInfo);
}
