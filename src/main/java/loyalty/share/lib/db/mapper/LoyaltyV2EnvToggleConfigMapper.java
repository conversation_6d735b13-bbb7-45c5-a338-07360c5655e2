package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.LoyaltyV2EnvToggleConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface LoyaltyV2EnvToggleConfigMapper {

    @Select("select id,channel,msg,is_enabled as isEnabled,create_time as createTime,update_time as updateTime from loyalty_v2_env_toggle_config where channel=#{channel}")
    @ResultType(LoyaltyV2EnvToggleConfig.class)
    public LoyaltyV2EnvToggleConfig getByChannel(@Param("channel") String channel);

}
