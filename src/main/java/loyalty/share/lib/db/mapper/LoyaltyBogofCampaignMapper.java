package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyBogofCampaign;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltyBogofCampaignMapper {

     @Insert("INSERT IGNORE INTO loyalty_bogof_campaign(id,cc_campaign_type,template_id,config_properties,remark,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{ccCampaignType},#{templateId},#{configProperties},#{remark},#{createdByUser},#{updatedByUser})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyBogofCampaign loyaltyBogofCampaign);

    @Insert("INSERT INTO loyalty_bogof_campaign(id,cc_campaign_type,template_id,config_properties,remark,created_by_user,updated_by_user)" +
                " VALUES(#{id},#{ccCampaignType},#{templateId},#{configProperties},#{remark},#{createdByUser},#{updatedByUser})" +
                " ON DUPLICATE KEY UPDATE template_id=VALUES(template_id),config_properties=VALUES(config_properties),remark=VALUES(remark),updated_by_user=VALUES(updated_by_user)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyBogofCampaign loyaltyBogofCampaign);

    @Update("UPDATE loyalty_bogof_campaign set cc_campaign_type=#{ccCampaignType},template_id=#{templateId},config_properties=#{configProperties},remark=#{remark},updated_by_user=#{updatedByUser} WHERE id=#{id}" )
    int updateByEntity(LoyaltyBogofCampaign loyaltyBogofCampaign);

    @Delete("DELETE FROM loyalty_bogof_campaign WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update loyalty_bogof_campaign set is_deleted=true, updated_by_user=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,cc_campaign_type,template_id,config_properties,remark,created_by_user,updated_by_user,create_time,update_time FROM loyalty_bogof_campaign WHERE id=#{id} ")
    @Results(id = "loyaltyBogofCampaign-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "ccCampaignType", column = "cc_campaign_type"),
      @Result(property = "templateId", column = "template_id"),
      @Result(property = "configProperties", column = "config_properties"),
      @Result(property = "remark", column = "remark"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyBogofCampaign getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,cc_campaign_type,template_id,config_properties,remark,created_by_user,updated_by_user,create_time,update_time FROM loyalty_bogof_campaign WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyBogofCampaign-mapping")
    LoyaltyBogofCampaign getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys
    @Select("SELECT id,cc_campaign_type,template_id,config_properties,remark,created_by_user,updated_by_user,create_time,update_time FROM loyalty_bogof_campaign WHERE cc_campaign_type=#{ccCampaignType} ")
    @ResultMap(value = "loyaltyBogofCampaign-mapping")
    LoyaltyBogofCampaign getByCcCampaignType(@Param("ccCampaignType") String ccCampaignType);

    @Select("SELECT id,cc_campaign_type,template_id,config_properties,remark,created_by_user,updated_by_user,create_time,update_time FROM loyalty_bogof_campaign WHERE cc_campaign_id=#{ccCampaignId} ")
    @ResultMap(value = "loyaltyBogofCampaign-mapping")
    LoyaltyBogofCampaign getByCcCampaignId(@Param("ccCampaignId") String ccCampaignId);



//update status sqls


//get data by foreign keys

}
