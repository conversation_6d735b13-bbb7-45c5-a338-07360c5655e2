package loyalty.share.lib.db.mapper;

import loyalty.share.lib.db.model.AiYingDaoStoreInfo;
import loyalty.share.lib.db.model.Log3rdPartyAYDCallModel;
import org.apache.ibatis.annotations.*;

@Mapper
public interface LoyaltyV2Log3rdpartaydcallMapper {
    @Select("SELECT * FROM loyalty_access_aydstore WHERE store_id=#{storeId}")
    @Results(value = {
            @Result(column = "store_id",property = "storeId"),
            @Result(column = "nc_store_name",property = "ncStoreName"),
            @Result(column = "nc_store_address",property = "ncStoreAddress"),
            @Result(column = "ax_code",property = "axCode"),
            @Result(column = "ayd_store_name",property = "aydStoreName"),
            @Result(column = "ayd_store_address",property = "aydStoreAddress"),
    })
    AiYingDaoStoreInfo findStoreInfoByStoreId(String storeId);

    @Insert("INSERT INTO loyalty_v2_log_3rdpartaydcall(id,req_desc,cellphone,request_senario,request_body,request_url,response_body,creator,updator,send_successed, nc_code,store_id,store_name,client_name)" +
            " VALUES(#{id},#{reqDesc},#{cellphone},#{requestSenario},#{requestBody},#{requestUrl},#{responseBody},#{creator},#{updator},#{sendSuccessed},#{ncCode},#{storeId},#{storeName},#{clientName})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertLog3rdpartaydcall(Log3rdPartyAYDCallModel model);
}
