package loyalty.share.lib.db.mapper;


import loyalty.share.lib.db.model.LoyaltyMallPageThemes;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface LoyaltyMallPageThemesMapper {

     @Insert("INSERT IGNORE INTO loyalty_mall_page_themes(id,theme_name,primary_color,secondary_color,backgroud_image,background_color,font_color,extra,remark,creator,updator)" +
                " VALUES(#{id},#{themeName},#{primaryColor},#{secondaryColor},#{backgroudImage},#{backgroundColor},#{fontColor},#{extra},#{remark},#{creator},#{updator})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(LoyaltyMallPageThemes loyaltyMallPageThemes);

    @Insert("INSERT INTO loyalty_mall_page_themes(id,theme_name,primary_color,secondary_color,backgroud_image,background_color,font_color,extra,remark,creator,updator)" +
                " VALUES(#{id},#{themeName},#{primaryColor},#{secondaryColor},#{backgroudImage},#{backgroundColor},#{fontColor},#{extra},#{remark},#{creator},#{updator})" +
                " ON DUPLICATE KEY UPDATE theme_name=VALUES(theme_name),primary_color=VALUES(primary_color),secondary_color=VALUES(secondary_color),backgroud_image=VALUES(backgroud_image),background_color=VALUES(background_color),font_color=VALUES(font_color),extra=VALUES(extra),remark=VALUES(remark),updator=VALUES(updator)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(LoyaltyMallPageThemes loyaltyMallPageThemes);

    @Update("UPDATE loyalty_mall_page_themes set theme_name=#{themeName},primary_color=#{primaryColor},secondary_color=#{secondaryColor},backgroud_image=#{backgroudImage},background_color=#{backgroundColor},font_color=#{fontColor},extra=#{extra},remark=#{remark},updator=#{updator} WHERE id=#{id}" )
    int updateByEntity(LoyaltyMallPageThemes loyaltyMallPageThemes);

    @Delete("DELETE FROM loyalty_mall_page_themes WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update loyalty_mall_page_themes set is_deleted=true WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,theme_name,primary_color,secondary_color,backgroud_image,background_color,font_color,extra,remark,creator,updator,create_time,update_time FROM loyalty_mall_page_themes WHERE id=#{id} ")
    @Results(id = "loyaltyMallPageThemes-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "themeName", column = "theme_name"),
      @Result(property = "primaryColor", column = "primary_color"),
      @Result(property = "secondaryColor", column = "secondary_color"),
      @Result(property = "backgroudImage", column = "backgroud_image"),
      @Result(property = "backgroundColor", column = "background_color"),
      @Result(property = "fontColor", column = "font_color"),
      @Result(property = "extra", column = "extra"),
      @Result(property = "remark", column = "remark"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updator", column = "updator"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    LoyaltyMallPageThemes getByIdEX(@Param("id") Long id);

    @Select("SELECT id,theme_name,primary_color,secondary_color,backgroud_image,background_color,font_color,extra,remark,creator,updator,create_time,update_time FROM loyalty_mall_page_themes WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "loyaltyMallPageThemes-mapping")
    LoyaltyMallPageThemes getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys


//update status sqls


//get data by foreign keys

}
