package loyalty.share.lib.common.model;

public enum LoyaltyReason {
    CAMPAIGN("C_"),
    MALL_EXCHANGE("E_0001"),
    WORK_ORDER("E_0002"),
    NEW_MON_MANUAL("E_0006"),
    RETURN_GOODS("E_0008"),
    EXPIRED("E_0005"),
    CANCEL_ORDER_DETAIL("E_0010");

    private String eventCode;

    LoyaltyReason(String eventCode) {
        this.eventCode = eventCode;
    }


    public String getEventCode() {
        if (!this.equals(CAMPAIGN))
            return eventCode;
        throw new RuntimeException("Not supported enum value");
    }

    public String getCampaignEventCode(Long campaignId) {
        if (this.equals(CAMPAIGN))
            return eventCode + campaignId.toString();
        throw new RuntimeException("Not supported enum value");
    }
}
