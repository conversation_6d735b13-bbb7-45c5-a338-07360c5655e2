package loyalty.share.lib.common.model;

/**
 * <AUTHOR>
 */
public enum UploadTargetEnum {
    /**
     * 上传到oss
     */
    OSS_UPLOAD("上传到oss", "1"),
    /**
     * 上传到服务器
     */
    SERVER_UPLOAD("上传到服务器", "2");

    private String name;
    private String code;

    private UploadTargetEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    private UploadTargetEnum() {
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }
}