package loyalty.share.lib.dictcache;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

import java.util.LinkedList;
import java.util.List;

@Configuration
public class DictCacheRedisConfig {
    @Value("${redis.dict.cache.topic}")
    private String redisListenerTopic;

    @Bean
    public RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory,
                                                   MessageListenerAdapter listenerAdapter) {

        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        List<PatternTopic> patternTopics = new LinkedList<PatternTopic>();
        patternTopics.add(new PatternTopic(redisListenerTopic));
        container.addMessageListener(listenerAdapter, patternTopics);
        return container;
    }


    @Bean
    MessageListenerAdapter listenerAdapter(DictDataReloadManager receiver) {
        return new MessageListenerAdapter(receiver, "onReload");
    }

    @Bean
    public RedisTemplate redisTemplate(RedisConnectionFactory connectionFactory) {
        return new StringRedisTemplate(connectionFactory);
    }

}
