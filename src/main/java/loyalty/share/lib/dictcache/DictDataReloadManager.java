package loyalty.share.lib.dictcache;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import loyalty.share.lib.dictcache.cache.DictCache;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class DictDataReloadManager {
    private final Map<String, DictCache> cacheMap;

    public DictDataReloadManager(DictCache... caches) {
        cacheMap = new HashMap<String, DictCache>();
        for (DictCache cache : caches) {
            cacheMap.put(cache.cacheName(), cache);
        }
    }

    public void onReload(Serializable message) {
        ReloadMsg reloadMsg = JSON.parseObject((String) message, ReloadMsg.class);
        log.info("Received reload message from user = {}, cache = {}", reloadMsg.getFromUser(), reloadMsg.getCacheName());
        if (StringUtils.isNotBlank(reloadMsg.getCacheName())) {
            DictCache dictCache = cacheMap.get(reloadMsg.getCacheName());
            if (dictCache == null) {
                log.error("No such cache");
                return;
            }
            dictCache.loadData();
        } else {
            for (DictCache dictCache : cacheMap.values()) {
                dictCache.loadData();
            }
        }
        log.info("Reload data completed.");

    }
}
