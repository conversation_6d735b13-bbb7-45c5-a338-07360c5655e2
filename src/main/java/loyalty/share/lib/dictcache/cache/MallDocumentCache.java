package loyalty.share.lib.dictcache.cache;

import lombok.extern.slf4j.Slf4j;
import loyalty.share.lib.db.mapper.LoyaltyMallDictTextMapper;
import loyalty.share.lib.db.model.LoyaltyMallDictText;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

@Slf4j
@Component
public class MallDocumentCache implements DictCache {
    public final static String MALL_DOCUMENT_CACHE = "MALL_DOCUMENT";
    private static Map<String, Map<String, LoyaltyMallDictText>> dictionary = new HashMap<String, Map<String, LoyaltyMallDictText>>();
    private final LoyaltyMallDictTextMapper dictTextMapper;
    @Value("${redis.dict.cache.categories}")
    private String categores;

    public MallDocumentCache(LoyaltyMallDictTextMapper dictTextMapper) {
        this.dictTextMapper = dictTextMapper;
    }

    @PostConstruct
    public void loadData() {
        List<LoyaltyMallDictText> dictTexts = dictTextMapper.getAllEnabledText();
        for (LoyaltyMallDictText dictText : dictTexts) {
            String key = getDictKey(dictText.getChannelId(), dictText.getTextArea());
            if (!dictionary.containsKey(key)) {
                dictionary.put(key, new HashMap<String, LoyaltyMallDictText>());
            }
            Map<String, LoyaltyMallDictText> codeMap = dictionary.get(key);
            codeMap.put(dictText.getIntCode() == null ? "" : dictText.getIntCode(), dictText);
        }
    }

    private String getDictKey(Long channelId, String textType) {
        String key = "";
        if (channelId != null) {
            key += "C_" + String.valueOf(channelId);
        }
        key += "||";
        if (textType != null) {
            key += "T_" + textType;
        }
        return key;
    }

    public LoyaltyMallDictText getDocument(Long channelId, TextType textType, Set<String> inputCodes) {
        String key = getDictKey(channelId, textType.name());
        if (dictionary.containsKey(key)) {
            LoyaltyMallDictText loyaltyMallDictText = null;
            Map<String, LoyaltyMallDictText> codeMap = dictionary.get(key);
            if (inputCodes == null || inputCodes.size() == 0) {
                return codeMap.get("");
            }

            for (String intCodeCombined : codeMap.keySet()) {
                String[] splited = intCodeCombined.split(",");
                Set<String> filteredSet = new HashSet<String>();
                for (String intCode : splited) {
                    for (String code : inputCodes) {
                        if (intCode.equals(code)) {
                            filteredSet.add(code);
                        }
                    }

                }
                if (filteredSet.size() == splited.length) {
                    if (loyaltyMallDictText == null)
                        loyaltyMallDictText = codeMap.get(intCodeCombined);
                    else
                        loyaltyMallDictText = loyaltyMallDictText.getPriority() < codeMap.get(intCodeCombined).getPriority() ? loyaltyMallDictText : codeMap.get(intCodeCombined);
                }
            }
            return loyaltyMallDictText;
        }
        return null;
    }

    public String cacheName() {
        return MALL_DOCUMENT_CACHE;
    }


    public enum TextType {
        SCAN_FAIL_REASON,
        QUERY_QUALIFICATION
    }
}
