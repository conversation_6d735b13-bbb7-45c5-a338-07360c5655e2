package loyalty.share.lib.dictcache.cache;


import lombok.extern.slf4j.Slf4j;
import loyalty.share.lib.db.mapper.LoyaltyV2DictCurrencyReasonMapper;
import loyalty.share.lib.db.model.LoyaltyV2DictCurrencyReason;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CurrencyRemarkCache implements DictCache {
    public final static String CURRENCY_REMAR_CACHE = "CURRENCY_REMARK";
    private static Map<String, String> reasonMap = new HashMap<String, String>();
    private final LoyaltyV2DictCurrencyReasonMapper loyaltyV2DictCurrencyReasonMapper;
    @Value("${redis.dict.cache.categories}")
    private String categores;

    public CurrencyRemarkCache(LoyaltyV2DictCurrencyReasonMapper loyaltyV2DictCurrencyReasonMapper) {
        this.loyaltyV2DictCurrencyReasonMapper = loyaltyV2DictCurrencyReasonMapper;
    }

    public static String getValue(String code) {
        return reasonMap.get(code);
    }

    @PostConstruct
    public void loadData() {
        if (!categores.contains(CURRENCY_REMAR_CACHE))
            return;
        List<LoyaltyV2DictCurrencyReason> reasons = loyaltyV2DictCurrencyReasonMapper.getAllCurrencyReasons();
        reasonMap.clear();
        for (LoyaltyV2DictCurrencyReason reason : reasons) {
            reasonMap.put(reason.getReasonCode(), reason.getReasonRemark());
        }

    }

    public String cacheName() {
        return CURRENCY_REMAR_CACHE;
    }
}
