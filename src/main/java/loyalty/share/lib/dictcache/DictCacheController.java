package loyalty.share.lib.dictcache;

import com.cstools.data.model.restful.response.InternalResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/dictcache")
public class DictCacheController {
    private final DictCacheReloadMsgPublisher dictCacheReloadMsgPublisher;

    public DictCacheController(DictCacheReloadMsgPublisher dictCacheReloadMsgPublisher) {
        this.dictCacheReloadMsgPublisher = dictCacheReloadMsgPublisher;
    }

    @PostMapping(value = "reload")
    public InternalResponse reloadCache(HttpServletRequest request, @RequestParam("categoryName") String categoryName) {
        log.info("Start to execute reloadCache");

        try {
            String remoteAddr = request.getRemoteAddr();
            if (categoryName.equals("ALL"))
                categoryName = null;
            ReloadMsg reloadMsg = new ReloadMsg(remoteAddr, categoryName);
            dictCacheReloadMsgPublisher.publishMessage(reloadMsg);

        } catch (Exception e) {
            log.error("Error while execute reloadCache");
        }

        log.info("End to execute reloadCache");
        return InternalResponse.success();
    }
}
