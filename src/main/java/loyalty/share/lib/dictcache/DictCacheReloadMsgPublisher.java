package loyalty.share.lib.dictcache;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DictCacheReloadMsgPublisher {

    private final StringRedisTemplate redisTemplate;

    @Value("${redis.dict.cache.topic}")
    private String topic;

    public DictCacheReloadMsgPublisher(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void publishMessage(ReloadMsg reloadMsg) {
        log.info("Start to publish reload message from user = {}, topic = {}", reloadMsg.toString(), topic);
        redisTemplate.convertAndSend(topic, JSON.toJSONString(reloadMsg));
    }

}
