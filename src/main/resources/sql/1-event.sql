
-- 新增不同渠道信息，用来对应不同的账本
DROP TABLE IF EXISTS loyalty_v2_book_owner;
CREATE TABLE loyalty_v2_book_owner(
	id INT PRIMARY KEY auto_increment,
	owner_code VARCHAR(128) NOT NULL COMMENT'账本持有人编码',
	owner_name VARCHAR(128) NOT NULL COMMENT'账本持有人名称',
	remark VARCHAR(640) COMMENT'备注详细',
	created_by_user INT NOT NULL COMMENT '创建人',
	updated_by_user INT NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP
);
-- 新增渠道
INSERT INTO loyalty_v2_book_owner (id,owner_code,owner_name,remark,created_by_user,updated_by_user) VALUES (1,'b5483071c87411e9a29f0235d2b38928','TradeMark','TradeMark',1,1);
INSERT INTO loyalty_v2_book_owner (id,owner_code,owner_name,remark,created_by_user,updated_by_user) VALUES (2,'b5483070c87411e9a29f0235d2b38928','D2C','D2C',1,1);
INSERT INTO loyalty_v2_book_owner (id,owner_code,owner_name,remark,created_by_user,updated_by_user) VALUES (3,'b548306fc87411e9a29f0235d2b38928','eCom','eCom',1,1);


--todo:evan 这个赠品不带id么？不行吧 done
-- 新增赠品
INSERT INTO campaign_gift(ID, GIFT_NAME, TYPE, GIFT_VALUE, CREATOR, UPDATOR, CREATE_TIME, UPDATE_TIME, GIFT_SERIES,platform)
VALUES (10919,'loyalty积分', 'POINT_EXTERNAL', 'LOYALTY_2019', '1', '1', now(),now(), 'LOYALTY', 'LOYALTY');

-- 新增不同渠道对应的账本关系表
DROP TABLE IF EXISTS loyalty_v2_currency_book;
CREATE TABLE loyalty_v2_currency_book(
	id INT PRIMARY KEY auto_increment,
	gift_id INT NOT NULL COMMENT'货币的赠品id',
	book_owner_id INT NOT NULL COMMENT'账本持有人id',
	created_by_user INT NOT NULL COMMENT '创建人',
	updated_by_user INT NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP
);
-- 新增账本
INSERT INTO loyalty_v2_currency_book(id,gift_id,book_owner_id,created_by_user,updated_by_user) VALUES(1,10919,1,1,1);

-- todo：evan, primary key(id)不要了，就普通key， 然后把cellphone+book_id作为分区KEY
-- 新增用户对应的货币对应的总账本信息表
DROP TABLE IF EXISTS loyalty_v2_member_currency;
CREATE TABLE loyalty_v2_member_currency(
	id INT auto_increment,
	gift_id INT NOT NULL COMMENT'货币的赠品id',
	cellphone VARCHAR(64) NOT NULL COMMENT'消费者手机号',
	currency_amount BIGINT NOT NULL COMMENT'当前货币数量',
	last_changed_by_eventid INT COMMENT'最后一次改变积分的事件Id',
	last_changed_by_historyid INT COMMENT'最后一次改变积分的相关操作历史记录id',
	last_changed_by_cmpid INT COMMENT'最后一次改变积分的活动id',
	member_grade INT COMMENT'会员等级',
	creator VARCHAR(64) NOT NULL COMMENT '创建人',
	updator VARCHAR(64) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	INDEX cellphone_gift_id(cellphone,gift_id) USING BTREE,
	UNIQUE KEY cellphone_gift_id(cellphone,gift_id),
	KEY(id),KEY(gift_id),KEY(cellphone)
	);
-- 增加10个分区
ALTER TABLE loyalty_v2_member_currency PARTITION BY KEY(cellphone,gift_id) PARTITIONS 10;


-- 如果减分处理不了，先存起来，然后定时再处理，失败则邮件报警。mq单线
DROP TABLE IF EXISTS loyalty_v2_member_add_book;
CREATE TABLE loyalty_v2_member_add_book(
	id INT auto_increment,
	book_id INT NOT NULL COMMENT'账本id',
	cellphone VARCHAR(64) NOT NULL COMMENT'消费者手机号',
	currency_amount BIGINT NOT NULL COMMENT'收入货币数量',
	event_id INT NOT NULL DEFAULT 0 COMMENT'事件id',
	changed_by_add_id INT COMMENT'改变积分的历史记录id【不同事件对应的历史记录表不一样】',
	
	trigger_time DATETIME NOT NULL COMMENT'积分变更的触发时间【程序生成】',
	expire_time DATETIME COMMENT'积分过期时间',
	close_expend_id INT COMMENT'平盘记录id',
	inherit_parent_id INT COMMENT'拆分自哪条加分记录的id',
	create_operation_id VARCHAR(120) COMMENT'创建操作的标识',
	close_operation_id VARCHAR(120) COMMENT'平盘使用操作的标识',

	creator VARCHAR(64) NOT NULL COMMENT '创建人',
	updator VARCHAR(64) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	INDEX book_id_cellphone(book_id,cellphone) USING BTREE,
	INDEX trigger_time(trigger_time) USING BTREE,
	INDEX expire_time(expire_time) USING BTREE,
	INDEX close_expend_id(close_expend_id) USING BTREE,
	INDEX close_operation_id(close_operation_id) USING BTREE,
	KEY(id),KEY(cellphone),KEY(book_id)
);
	
-- 增加100个分区
ALTER TABLE loyalty_v2_member_add_book PARTITION BY KEY(book_id,cellphone) PARTITIONS 100;

-- 用户积分减少记录表
DROP TABLE IF EXISTS loyalty_v2_member_reduce_book;
CREATE TABLE loyalty_v2_member_reduce_book(
	id INT auto_increment,
	book_id INT NOT NULL COMMENT'账本id',
	cellphone VARCHAR(64) NOT NULL COMMENT'消费者手机号',
	currency_amount BIGINT NOT NULL COMMENT'收入货币数量',
	event_id INT NOT NULL DEFAULT 0 COMMENT'事件id',
	changed_by_reduce_id INT COMMENT'改变积分的历史记录id【不同事件对应的历史记录表不一样】',
	trigger_time DATETIME NOT NULL COMMENT'积分变更的触发时间【程序生成】',
	close_operation_id VARCHAR(120) COMMENT'平盘使用操作的标识',

	creator VARCHAR(64) NOT NULL COMMENT '创建人',
	updator VARCHAR(64) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	INDEX book_id_cellphone(book_id,cellphone) USING BTREE,
	INDEX trigger_time(trigger_time) USING BTREE,
	INDEX close_operation_id(close_operation_id) USING BTREE,
	KEY(id),KEY(cellphone),KEY(book_id)
);
-- 增加100个分区
ALTER TABLE loyalty_v2_member_reduce_book PARTITION BY KEY(book_id,cellphone) PARTITIONS 100;


--todo: evan, event_code not null
-- 新增积分事件表
DROP TABLE IF EXISTS loyalty_v2_event;
CREATE TABLE loyalty_v2_event(
	id INT PRIMARY KEY auto_increment,
	event_code VARCHAR(64) NOT NULL COMMENT'事件代码,如SIGN, CAMPAIGN, EXCHANGE, DEPOSITE, PURCHASE, SYSTEM_ROLLING, EXTERNAL等',
	description VARCHAR(128),
	direction VARCHAR(64) NOT NULL COMMENT'标记是加分事件还是减分事件,OUT 减分， IN:加分, ',
	creator VARCHAR(64) NOT NULL COMMENT '创建人',
	updator VARCHAR(64) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	UNIQUE KEY event_code(event_code,direction)
);
-- 增加数据迁移事件
INSERT INTO loyalty_v2_event(id,event_code,description,direction,creator,updator) VALUES(1,'DATA_PATCH','数据迁移','IN','evan','evan');
INSERT INTO loyalty_v2_event(id,event_code,description,direction,creator,updator) VALUES(2,'DATA_PATCH','数据迁移','OUT','evan','evan');
INSERT INTO loyalty_v2_event(id,event_code,description,direction,creator,updator) VALUES(3,'EXPIRE','积分过期','OUT','evan','evan');
INSERT INTO loyalty_v2_event(id,event_code,description,direction,creator,updator) VALUES(4,'SCAN','扫码','IN','xuetianhua','xuetianhua');
INSERT INTO loyalty_v2_event(id,event_code,description,direction,creator,updator) VALUES(5,'DEPOSITE','积分变更','IN','xuetianhua','xuetianhua');
INSERT INTO loyalty_v2_event(id,event_code,description,direction,creator,updator) VALUES(6,'EXCHANGE','兑换事件','IN','xuetianhua','xuetianhua');
INSERT INTO loyalty_v2_event(id,event_code,description,direction,creator,updator) VALUES(7,'CAMPAIGN_ADD','参与活动','IN','evan','evan');

-- 新增平盘异常表
DROP TABLE IF EXISTS loyalty_v2_book_event_exception;
CREATE TABLE loyalty_v2_book_event_exception(
	id INT PRIMARY KEY auto_increment,
	event_id INT,
	event_params text COMMENT'事件参数',
	cellphone VARCHAR(64),
	related_book_ids VARCHAR(64) COMMENT'账本id列表',
	err_msg VARCHAR(640),
	err_detail text,
	create_time DATETIME DEFAULT NOW()
);

-- 新增异常记录表
DROP TABLE IF EXISTS loyalty_v2_book_event_exception;
CREATE TABLE loyalty_v2_book_event_exception(
	id INT PRIMARY key auto_increment,
	event_id INT,
	event_type VARCHAR(64) COMMENT'add|reduce',
	event_params text NOT NULL,
	cellphone VARCHAR(64),
	related_book_ids VARCHAR(320),
	related_currency_ids VARCHAR(320),
	err_msg text,
	err_detail text,
	ready_to_reprocess bool NOT NULL,
	retry_count INT NOT NULL DEFAULT 0,
	retry_times datetime ,
	has_processed bool NOT NULL DEFAULT FALSE,
	creator VARCHAR(64) NOT NULL COMMENT '创建人',
	updator VARCHAR(64) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	INDEX cellphone(cellphone) USING BTREE,
	INDEX create_time(create_time) USING BTREE,
	INDEX retry_count(retry_count) USING BTREE,
	INDEX ready_to_reprocess(ready_to_reprocess) USING BTREE,
	INDEX has_processed(has_processed) USING BTREE
);