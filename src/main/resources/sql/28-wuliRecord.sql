DROP TABLE IF EXISTS loyalty_v2_wuli_record;
CREATE TABLE loyalty_v2_wuli_record	(
	id INT PRIMARY KEY auto_increment,
	union_id VARCHAR(64) ,
	channel_id int(11),
	cellphone VARCHAR(64) NOT NULL,
	remark VARCHAR(128),
	file_name <PERSON><PERSON><PERSON><PERSON>(100),
	status VARCHAR(16),
	creator varchar(32) NOT NULL COMMENT '创建人',
	updator varchar(32) NOT NULL COMMENT '修改人',
	auditing_time DATETIME COMMENT'审核时间',
    create_time DATETIME DEFAULT NOW(),
    update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	INDEX cellphone(cellphone) USING BTREE
);


