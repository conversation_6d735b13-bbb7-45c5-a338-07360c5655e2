<!--last id 32590192-->

DELIMITER $$

DROP TRIGGER `log_loyalty_v2_currency_add_record_write_record_update`$$

CREATE
    TRIGGER `log_loyalty_v2_currency_add_record_write_record_update` AFTER UPDATE ON `loyalty_v2_member_currency_add` 
    FOR EACH ROW INSERT INTO log_loyalty_v2_currency_amt_add (
	member_currency_id,
	org_used_amt,
	new_used_amt,
	last_changed_by_add_id,
	last_changed_by_reduce_id,
	member_grade,
	creator
)
VALUES
	(
		NEW.id,
		OLD.used_amount,
		NEW.used_amount,
		NEW.id,
		NEW.changed_by_reduce_id,
		NEW.member_grade,
		NEW.updator
	);
$$

DELIMITER ;