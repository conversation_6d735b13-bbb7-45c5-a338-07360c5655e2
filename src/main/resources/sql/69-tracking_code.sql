CREATE TABLE `loyalty_tracking_code_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cuanhuo_code` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '罐码',
  `digit_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '防伪码',
  `net_weight` float DEFAULT NULL COMMENT '净重',
  `product_date` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产日期',
  `product_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品sapid',
  `product_series` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品系列',
  `product_stage` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品段数',
  `source_place` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品来源',
  `source_dealer` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品经销商',
  `code_region` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '罐码大区',
  `code_area` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '罐码片区',
  `ship_to_code` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '经销商shipto编码',
  `is_tmall` tinyint(1) DEFAULT NULL COMMENT '是否天猫罐码',
  `is_gift` tinyint(1) DEFAULT NULL COMMENT '是否赠品',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录创建时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_cuanhuo_code`(`cuanhuo_code`) USING BTREE,
    UNIQUE INDEX `uk_digit_code`(`digit_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 中商返回的经销商字段
ALTER TABLE `campaign_history_product` MODIFY COLUMN `agency` varchar(256) , algorithm=INPLACE ;
ALTER TABLE loyalty_tracking_code_info ADD COLUMN pack_code VARCHAR(128) DEFAULT NULL COMMENT '箱码' AFTER digit_code;

ALTER TABLE loyalty_tracking_code_info
  add column has_vb_code varchar(4) DEFAULT '否'  COMMENT '是否有膜码',
  ADD COLUMN `aluminium_top_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '膜上码',
  ADD COLUMN `aluminium_bottom_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '膜下码';


ALTER TABLE loyalty_tracking_code_info
	add UNIQUE index aluminium_top_code_uk(aluminium_top_code),
	add UNIQUE index aluminium_bottom_code_uk(aluminium_bottom_code);


ALTER TABLE loyalty_tracking_code_info
	add index aluminium_top_code_ik(aluminium_top_code),
	add index aluminium_bottom_code_ik(aluminium_bottom_code);


