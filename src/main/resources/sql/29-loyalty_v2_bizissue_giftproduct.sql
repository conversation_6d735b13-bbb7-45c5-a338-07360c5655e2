DROP TABLE IF EXISTS loyalty_v2_bizissue_giftproduct;
CREATE TABLE `loyalty_v2_bizissue_giftproduct` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `traceability_code` varchar(64) NOT NULL COMMENT '罐码',
  `biz_senario` varchar(64) NOT NULL COMMENT 'gift_to_product | product_to_gift',
  `platform` varchar(64) DEFAULT NULL,
  `creator` varchar(32) NOT NULL COMMENT '创建人',
  `updator` varchar(32) NOT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_traceability_code_senario` (`traceability_code`, `biz_senario`) USING BTREE,
  INDEX `ik_traceability_code`(traceability_code) USING BTREE
) DEFAULT CHARSET=utf8;
