CREATE TABLE `log_youan_lottery_call` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                          `cellphone` varchar(32) DEFAULT NULL,
                                          `req_successed` TINYINT DEFAULT 0 COMMENT '请求业务成功标志 0--成功 1--失败',
                                          `req_path` varchar(512) DEFAULT NULL COMMENT '请求接口路径',
                                          `req_body` longtext COMMENT '请求体',
                                          `result_body` longtext COMMENT '请求响应体',
                                          `req_id` VARCHAR(64) DEFAULT NULL COMMENT '请求标识id',
                                          `caller` varchar(32) DEFAULT NULL COMMENT '调用者',
                                          `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                          PRIMARY KEY (`id`) USING BTREE,
                                          KEY `cellphone` (`cellphone`),
                                          KEY `req_id` (`req_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='佑安增加抽奖次数记录表';