alter table campaign_history_bxgx add column channel_id bigint(20);

alter table campaign_history_bxgx drop column gift_id_bk,  drop column exchange_id;


alter table campaign_history_product add
(ship_to_code varchar(20), agency varchar(20), custom_region varchar(20), tracking_region varchar(20), is_gift int(2), is_tmall_order int(2));


alter table campaign add column `campaign_senario` varchar(50) DEFAULT NULL;


alter table campaign_gift_relationship add expire_type varchar(64);
alter table campaign_gift_relationship add datetime;
alter table campaign_gift_relationship add expire_in_hour int(20);
ALTER TABLE campaign_gift_relationship ADD currency_book_id BIGINT(20);
ALTER TABLE campaign_gift ADD COLUMN rmb_rating DOUBLE;



alter table campaign add column loyalty_event_id	bigint(20) DEFAULT null;
ALTER TABLE `campaign_gift`
ADD COLUMN `logistics_supllier_id` bigint(20) COMMENT '供应商id' AFTER `remark`;

ALTER TABLE `campaign`
ADD COLUMN `is_editable` tinyint(1) DEFAULT 1 COMMENT '是否在页面显示' AFTER `loyalty_event_id`;





alter table campaign_rule_info
	add column active_start_time datetime DEFAULT NULL comment '规则启用时间',
	add column active_end_time datetime DEFAULT NULL comment '规则停用时间',
	add column is_enabled  tinyint(1)  DEFAULT TRUE,
	add column output_type varchar(256) DEFAULT NULL,
	add column output_result varchar(128) DEFAULT NULL;

alter table campaign_rule_info add column rule_group_id	bigint(20) DEFAULT null;


create table rule_group (
  id bigint(20) not null auto_increment,
	group_name varchar(512) default null comment '分组名字',
	group_code varchar(128) not null comment '分组码',
	remark varchar(500) default null comment '备注',
	created_by_user varchar(32) not null,
  updated_by_user varchar(32) not null,
  create_time datetime default current_timestamp,
  update_time datetime default current_timestamp,
	primary key (id),
	unique key uk_group_code (group_code) using btree,
	key group_code (group_code) using btree,
);

DROP TABLE IF EXISTS rule_group_detail;
create table rule_group_detail (
  id bigint(20) not null auto_increment,
	rule_group_id bigint(20) not null,
	rule_id bigint(20) not null,
	created_by_user varchar(32) not null,
  updated_by_user varchar(32) not null,
  create_time datetime default current_timestamp,
  update_time datetime default current_timestamp,
	primary key (id),
	CONSTRAINT `fk_rule_group_id` FOREIGN KEY (`rule_group_id`) REFERENCES `rule_group` (`id`),
	unique key uk_group_code_rule (rule_group_id,rule_id) using btree,
	key rule_group_rule_id (rule_id) using btree,
	key group_code (rule_group_id) using btree,
	key group_code_rule (rule_group_id, rule_id) using btree
);



-- DATA PATCH
--SELECT EXTERNAL_PROPERTIES->>"$.enabled" AS enabled, EXTERNAL_PROPERTIES->>"$.startTime" AS startTime FROM campaign_rule_info WHERE id=9157;

update campaign_rule_info set active_start_time= EXTERNAL_PROPERTIES->>"$.startTime" , 	active_end_time= EXTERNAL_PROPERTIES->>"$.endTime" ,
  is_enabled= (case when EXTERNAL_PROPERTIES->>"$.enabled" = 'true' then 1 when  EXTERNAL_PROPERTIES->>"$.enabled" = 'false' then 0 else NULL END)
WHERE EXTERNAL_PROPERTIES IS NOT NULL	;


update 	campaign_rule_info set
		output_type=
			  if(
						LOCATE('=', RULE_EXECUTED_VALUE)>0,
						substring(
								RULE_EXECUTED_VALUE, 1, LOCATE('=', RULE_EXECUTED_VALUE)-1
						) ,
						NULL
				),
		output_result =
				if(
						LOCATE('=', RULE_EXECUTED_VALUE)>0,
						substring(
								RULE_EXECUTED_VALUE,  LOCATE('=', RULE_EXECUTED_VALUE)+1
						) ,
						NULL
				)
  where RULE_EXECUTED_VALUE is not null and RULE_EXECUTED_VALUE!='loyalty_common_rule';

--



-- 删除唯一索引
alter table campaign_rule_info drop index CAMPAIGN_ID;

alter table campaign_rule_info add index index_rule_group_id(rule_group_id);
alter table campaign_rule_info add foreign key rule_group_id_ibfk(rule_group_id) references rule_group(id);
-- 添加唯一索引
alter table campaign_rule_info add CONSTRAINT  uk_group_output unique (campaign_id, rule_group_id, dimension_id, priority, output_type, output_result) ;

update campaign_rule_info set output_type='loyalty_common_rule' where RULE_EXECUTED_VALUE='loyalty_common_rule'	;

	alter table rule add column platform	VARCHAR(32) DEFAULT null comment '规则刷新平台,cmp,loyalty平台,null表示全平台可用';
	alter table rule_element add column platform	VARCHAR(32) DEFAULT null  comment '规则刷新平台,cmp,loyalty平台,null表示全平台可用';




-- rule_element 索引问题
ALTER TABLE `rule_element` DROP INDEX `ELEMENT_NAME`,ADD INDEX `uk_name_platform`(`ELEMENT_NAME`, `platform`);

