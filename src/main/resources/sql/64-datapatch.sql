UPDATE loyalty_v2_member_currency_add
SET trigger_time = '2021-12-31 23:59:59'
WHERE reason_code = 'E_EXP_ADD_POINT';

ALTER TABLE experience_amount_copy ADD COLUMN packs FLOAT(11,1);
UPDATE experience_amount_copy SET
packs = IF((ROUND(current_amt/200) > FLOOR(current_amt/200)) AND (current_amt/200 != FLOOR(current_amt/200)+0.5)
, CEIL(current_amt/200),
IF( current_amt/200 != FLOOR(current_amt/200),  FLOOR(current_amt/200)+0.5, current_amt/200)) ;




UPDATE loyalty_v2_member_currency_add
INNER JOIN `experience_amount_copy` ON loyalty_v2_member_currency_add.`cellphone` = experience_amount_copy.`cellphone`
SET loyalty_v2_member_currency_add.`currency_amount` = loyalty_v2_member_currency_add.`currency_amount`- experience_amount_copy.`packs`,
loyalty_v2_member_currency_add.updator='20220111patchminus'
WHERE loyalty_v2_member_currency_add.`reason_code` = 'E_LOYALTY_CHANGE_POINT_2022' AND experience_amount_copy.`current_amt`>0;


UPDATE loyalty_v2_member_currency_add
INNER JOIN `experience_amount_copy` ON loyalty_v2_member_currency_add.`cellphone` = experience_amount_copy.`cellphone`
SET loyalty_v2_member_currency_add.`currency_amount` = loyalty_v2_member_currency_add.`currency_amount`+ experience_amount_copy.`packs`,
loyalty_v2_member_currency_add.updator='20220111patchplus'
WHERE loyalty_v2_member_currency_add.`reason_code` = 'E_LOYALTY_CHANGE_POINT_2021' AND experience_amount_copy.`current_amt`>0;

UPDATE loyalty_v2_member_add_book
INNER JOIN loyalty_v2_member_currency_add ON loyalty_v2_member_add_book.changed_by_add_id = loyalty_v2_member_currency_add.id
SET loyalty_v2_member_add_book.currency_amount = loyalty_v2_member_currency_add.`currency_amount`
WHERE (loyalty_v2_member_currency_add.updator='20220111patchplus' OR  loyalty_v2_member_currency_add.updator='20220111patchminus');


SELECT * FROM experience_amount_copy WHERE cellphone = '18615311132';
SELECT * FROM loyalty_v2_member_currency_add  WHERE cellphone = '18615311132';
SELECT * FROM loyalty_v2_member_add_book  WHERE cellphone = '18615311132';