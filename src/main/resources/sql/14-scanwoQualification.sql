DROP TABLE IF EXISTS `loyalty_v2_scanwo_qualification`;
CREATE TABLE `loyalty_v2_scanwo_qualification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `work_order_id` bigint(20) NOT NULL,
  `antifake_code` varchar(32) NOT NULL,
  `user_cellphone` varchar(32) NOT NULL,
  `remark` varchar(500) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by_user` bigint(16) NOT NULL,
  `updated_by_user` bigint(16) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cellphone_antifake_code` (`antifake_code`,`user_cellphone`) USING BTREE,
  KEY `user_cellphone` (`user_cellphone`) USING BTREE,
  KEY `work_order_id` (`work_order_id`) USING BTREE,
  KEY `antifake_code` (`antifake_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;