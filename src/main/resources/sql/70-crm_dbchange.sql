-- loyalty 改动脚本
-- 测试环境
INSERT INTO `rule_selector_db_config`(`ID`, `host`, `driver`, `port`, `user`, `password`, `jdbc_url`, `schema_name`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`) VALUES (13, 'crm_pg_for_loyalty', 'org.postgresql.Driver', '5432', 'jojo_chen', '9kji*XL\\', '******************************************************************************************************', 'cep', 'howard', 'howard', now(), now());
INSERT INTO `rule_selector_db`(`ID`, `DESCRIPTION`, `TEMPLATE`, `DB_CONFIG_ID`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`) VALUES (7, 'crm_pg_for_loyalty', 'select', 13, 'howard', 'howard', now(), now());

-- 正式环境
INSERT INTO `rule_selector_db_config`(`ID`, `host`, `driver`, `port`, `user`, `password`, `jdbc_url`, `schema_name`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`) VALUES (13, 'crm_pg_for_loyalty', 'org.postgresql.Driver', '5432', 'loyalty', 'Q<G:h6{MggJw[RK]', '******************************************************************************************************', 'cep', 'howard', 'howard', now(), now());
INSERT INTO `rule_selector_db`(`ID`, `DESCRIPTION`, `TEMPLATE`, `DB_CONFIG_ID`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`) VALUES (7, 'crm_pg_for_loyalty', 'select', 13, 'howard', 'howard', now(), now());


-- 切换脚本
UPDATE `rule_element` SET  `SELECTOR_ID` = 7, `SEL_STATEMENT` = 'select count(1) from crm_standard_member m where m.mobile=${cellphone}  and m.status=\'NORMAL\' and m.deleted=0', `UPDATOR` = 'howard',  `UPDATE_TIME` = now()  WHERE `ID` = 20153;
UPDATE `rule_element` SET  `SELECTOR_ID` = 7, `SEL_STATEMENT` = 'select  date_part(\'day\',now()-f.birthday) from crm_standard_member_family f left join crm_standard_member m on f.member_id=m.member_id where m.mobile=${cellphone} and f.family_ties=\'10\' and m.status=\'NORMAL\' and m.deleted=0 limit 1',  `UPDATOR` = 'howard',`UPDATE_TIME` = now()  WHERE `ID` = 20165;
UPDATE `rule_element` SET  `SELECTOR_ID` = 7, `SEL_STATEMENT` = 'SELECT COALESCE((select date_part(\'day\',now()-f.birthday) from crm_standard_member_family f left join crm_standard_member m on f.member_id=m.member_id where m.mobile=${cellphone} and  m.status=\'NORMAL\' and m.deleted=0 and\r\nf.family_ties=\'11\' limit 1),-999) as sec_state', `UPDATOR` = 'howard',  `UPDATE_TIME` = now()  WHERE `ID` = 20166;
UPDATE `rule_element` SET  `SELECTOR_ID` = 7, `SEL_STATEMENT` = 'select count(1) from crm_standard_member m inner join crm_standard_member_family f on m.member_id=f.member_id where m.mobile=${cellphone} and f.family_ties=\'10\' and m.status=\'NORMAL\' and m.deleted=0', `SEL_RESULT_VALUE_PATH` = '$.is_first_baby',  `UPDATOR` = 'howard', `UPDATE_TIME` = now() WHERE `ID` = 20173;
UPDATE `rule_element` SET  `SELECTOR_ID` = 7, `SEL_STATEMENT` = 'select count(1) from crm_standard_member m inner join crm_standard_member_family f on m.member_id=f.member_id where m.mobile=${cellphone} and f.family_ties=\'11\' and m.status=\'NORMAL\' and m.deleted=0', `SEL_RESULT_VALUE_PATH` = '$.is_sec_baby',  `UPDATOR` = 'howard',  `UPDATE_TIME` = now() WHERE `ID` = 20174;
UPDATE `rule_element` SET  `SELECTOR_ID` = 7, `SEL_STATEMENT` = 'select * from crm_standard_member where mobile=${cellphone} and status=\'NORMAL\' and deleted=0 limit 1',  `UPDATOR` = 'howard',  `UPDATE_TIME` = now() WHERE `ID` = 20315;
UPDATE `rule_element` SET  `SELECTOR_ID` = 7, `SEL_STATEMENT` = 'select count(1) from crm_standard_member m inner join crm_standard_member_family f on m.member_id=f.member_id where m.mobile=${cellphone} and f.family_ties in (\'10\', \'11\',\'12\') and m.status=\'NORMAL\' and m.deleted=0', `UPDATOR` = 'howard',  `UPDATE_TIME` = now() WHERE `ID` = 20358;
UPDATE `rule_element` SET  `SELECTOR_ID` = 7, `SEL_STATEMENT` = 'SELECT count(1) as count FROM crm_standard_member m	INNER JOIN crm_standard_member_order o ON o.member_id = m.member_id  INNER JOIN crm_standard_member_order_products op ON o.order_no = op.order_no\r\n	 INNER JOIN crm_standard_product_prototype product ON op.product_id = product.product_id  where m.mobile=${cellphone} and m.status=\'NORMAL\' and m.deleted=0  and (op.attr5>=\'750\' or product.attr1>=\'750G\')',  `UPDATOR` = 'howard',  `UPDATE_TIME` = now() WHERE `ID` = 20429;



-- 回滚脚本
UPDATE `rule_element` SET `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'SELECT count(1) from PROFILE_MEMBER where CELLPHONE=${cellphone}', `UPDATOR` = 'admin', `UPDATE_TIME` = '2018-07-01 00:02:53' WHERE `id` = 20153;
UPDATE `rule_element` SET `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select top 1 DATEDIFF(DD,b.BABYBIRTHDAY,GETUTCDATE()) from PROFILE_BABY b left JOIN PROFILE_MEMBER m on m.Id=b.MEMBERID where m.CELLPHONE=${cellphone} AND b.ISSECBIRTH=0 ', `UPDATOR` = 'admin', `UPDATE_TIME` = '2017-12-15 15:19:13' WHERE `id` = 20165;
UPDATE `rule_element` SET `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'SELECT COALESCE((select top 1 DATEDIFF(DD,b.BABYBIRTHDAY,GETUTCDATE()) from PROFILE_BABY b left JOIN PROFILE_MEMBER m on m.Id=b.MEMBERID where m.CELLPHONE=${cellphone} AND b.ISSECBIRTH=1),-999) as sec_state', `UPDATOR` = 'admin', `UPDATE_TIME` = '2017-12-22 13:30:38' WHERE `id` = 20166;
UPDATE `rule_element` SET `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select count(1) from PROFILE_BABY b inner JOIN PROFILE_MEMBER m on m.Id=b.MEMBERID where m.CELLPHONE=${cellphone} AND b.ISSECBIRTH=0', `UPDATOR` = 'admin', `UPDATE_TIME` = '2018-07-01 00:02:47' WHERE `id` = 20173;
UPDATE `rule_element` SET `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select count(1) from PROFILE_BABY b left join PROFILE_MEMBER m on b.MemberId=m.Id where b.IsSecBirth=1 and m.CELLPHONE=${cellphone}', `UPDATOR` = 'admin', `UPDATE_TIME` = '2018-07-01 00:02:50' WHERE `id` = 20174;
UPDATE `rule_element` SET `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select top 1 SUBCHANNEL from PROFILE_MEMBER where CELLPHONE=${cellphone}', `UPDATOR` = 'admin', `UPDATE_TIME` = '2017-12-15 15:17:58' WHERE `id` = 20315;
UPDATE `rule_element` SET `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select count(1) from PROFILE_BABY b inner JOIN PROFILE_MEMBER m on m.Id=b.MEMBERID where m.CELLPHONE=${cellphone}', `UPDATOR` = 'starry', `UPDATE_TIME` = '2021-09-02 09:43:11' WHERE `id` = 20358;
UPDATE `rule_element` SET `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'SELECT COUNT(1) FROM ORDERINFO o LEFT JOIN ORDERDETAIL d ON o.Id=d.OrderId left join DICT_PRODUCT on d.SAPID=DICT_PRODUCT.SAP_ID LEFT JOIN PROFILE_MEMBER m ON o.MemberId=m.Id WHERE o.CELLPHONE=${cellphone} AND (  d.size>=\'750\'  or (  DICT_PRODUCT.pack_size>=\'750G\') )', `UPDATOR` = 'xuetianhua', `UPDATE_TIME` = '2022-11-14 12:37:33' WHERE `id` = 20429;




-- backup
UPDATE `rule_element` SET `ELEMENT_NAME` = 'is_member', `sel_level` = 0, `DESCRIPTION` = '是否会员', `DATA_TYPE` = 'bool', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'SELECT count(1) from PROFILE_MEMBER where CELLPHONE=${cellphone}', `SEL_RESULT_VALUE_PATH` = '$.is_member', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2018-07-01 00:02:53', `UPDATE_TIME` = '2018-07-01 00:02:53', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20153;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'first_baby_month', `sel_level` = 0, `DESCRIPTION` = '一胎月龄', `DATA_TYPE` = 'int', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select top 1 DATEDIFF(DD,b.BABYBIRTHDAY,GETUTCDATE()) from PROFILE_BABY b left JOIN PROFILE_MEMBER m on m.Id=b.MEMBERID where m.CELLPHONE=${cellphone} AND b.ISSECBIRTH=0 ', `SEL_RESULT_VALUE_PATH` = '$.first_baby_month', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2017-12-15 15:19:13', `UPDATE_TIME` = '2017-12-15 15:19:13', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20165;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'sec_baby_month', `sel_level` = 0, `DESCRIPTION` = '二胎月龄', `DATA_TYPE` = 'int', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'SELECT COALESCE((select top 1 DATEDIFF(DD,b.BABYBIRTHDAY,GETUTCDATE()) from PROFILE_BABY b left JOIN PROFILE_MEMBER m on m.Id=b.MEMBERID where m.CELLPHONE=${cellphone} AND b.ISSECBIRTH=1),-999) as sec_state', `SEL_RESULT_VALUE_PATH` = '$.sec_baby_month', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2017-12-22 13:30:38', `UPDATE_TIME` = '2017-12-22 13:30:38', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20166;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'has_uploaded', `sel_level` = 0, `DESCRIPTION` = '是否上传准生证', `DATA_TYPE` = 'int', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = ' SELECT case when count(*) >0 then 1 else 0 end as flag  FROM BIRTHCERTIFICATE bc  INNER JOIN PROFILE_MEMBER pm on pm.CELLPHONE=bc.CELLPHONE  left join PROFILE_WECHATINFO pw on pw.MemberId=pm.Id where bc.id not in(  SELECT top 0 bc.id  FROM BIRTHCERTIFICATE bc  INNER JOIN PROFILE_MEMBER pm on pm.CELLPHONE=bc.CELLPHONE  left join PROFILE_WECHATINFO pw on pw.MemberId=pm.Id   and bc.CELLPHONE=${cellphone}  and bc.state=1  and  certtype=2 ) \r\n and bc.CELLPHONE=${cellphone}', `SEL_RESULT_VALUE_PATH` = '$.has_uploaded', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2018-07-26 10:53:20', `UPDATE_TIME` = '2018-07-26 10:53:20', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20167;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'sec_baby_audit_status', `sel_level` = 0, `DESCRIPTION` = '二胎准生证状态', `DATA_TYPE` = 'int', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'SELECT COALESCE((SELECT top 1 BIRTHCERTIFICATE.STATE as sec_state FROM [dbo].[BIRTHCERTIFICATE], profile_baby WHERE profile_baby.id = BIRTHCERTIFICATE.babyid AND cellphone = ${cellphone} AND isSecBirth=1 ORDER BY BIRTHCERTIFICATE.CREATEDATE DESC),999) as sec_state', `SEL_RESULT_VALUE_PATH` = '$.sec_baby_audit_status', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2018-10-25 11:29:39', `UPDATE_TIME` = '2018-10-25 11:29:39', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20170;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'first_baby_audit_status', `sel_level` = 0, `DESCRIPTION` = '一胎准生证状态', `DATA_TYPE` = 'int', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'SELECT COALESCE((SELECT top 1 BIRTHCERTIFICATE.STATE as sec_state FROM [dbo].[BIRTHCERTIFICATE], profile_baby WHERE profile_baby.id = BIRTHCERTIFICATE.babyid AND cellphone = ${cellphone} AND isSecBirth=0 ORDER BY BIRTHCERTIFICATE.CREATEDATE DESC),999) as sec_state', `SEL_RESULT_VALUE_PATH` = '$.first_baby_audit_status', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2018-10-25 11:29:39', `UPDATE_TIME` = '2018-10-25 11:29:39', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20171;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'is_first_baby', `sel_level` = 0, `DESCRIPTION` = '是否一胎', `DATA_TYPE` = 'bool', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select count(1) from PROFILE_BABY b inner JOIN PROFILE_MEMBER m on m.Id=b.MEMBERID where m.CELLPHONE=${cellphone} AND b.ISSECBIRTH=0', `SEL_RESULT_VALUE_PATH` = '$.is_first_baby', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2018-07-01 00:02:47', `UPDATE_TIME` = '2018-07-01 00:02:47', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20173;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'is_sec_baby', `sel_level` = 0, `DESCRIPTION` = '是否二胎', `DATA_TYPE` = 'bool', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select count(1) from PROFILE_BABY b left join PROFILE_MEMBER m on b.MemberId=m.Id where b.IsSecBirth=1 and m.CELLPHONE=${cellphone}', `SEL_RESULT_VALUE_PATH` = '$.is_sec_baby', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2018-07-01 00:02:50', `UPDATE_TIME` = '2018-07-01 00:02:50', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20174;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'is_after_uploadLoyaltyFirCert_1010', `sel_level` = 0, `DESCRIPTION` = '会员一胎是否在2020年10月10日前上传Loyalty证件', `DATA_TYPE` = 'bool', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = '	SELECT count(1) FROM BIRTHCERTIFICATE ,WLTSWLSH,PROFILE_BABY WHERE WLTSWLSH.id=BIRTHCERTIFICATE.LOGISTICSID and PROFILE_BABY.id=BIRTHCERTIFICATE.BABYID and BIRTHCERTIFICATE.SOURCETYPE = \'LOYALTY\'  AND BIRTHCERTIFICATE.cellphone=${cellphone} and datediff(day,WLTSWLSH.CREATEDATE,\'2020-10-10\')>=0 and PROFILE_BABY.IsSecBirth=0', `SEL_RESULT_VALUE_PATH` = '$.is_after_uploadLoyaltyFirCert_1010', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2018-07-01 00:02:47', `UPDATE_TIME` = '2018-07-01 00:02:47', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20284;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'is_after_uploadLoyaltySecCert_1010', `sel_level` = 0, `DESCRIPTION` = '会员二胎是否在2020年10月10日前上传Loyalty证件', `DATA_TYPE` = 'bool', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = '	SELECT count(1) FROM BIRTHCERTIFICATE ,WLTSWLSH,PROFILE_BABY WHERE WLTSWLSH.id=BIRTHCERTIFICATE.LOGISTICSID and PROFILE_BABY.id=BIRTHCERTIFICATE.BABYID and BIRTHCERTIFICATE.SOURCETYPE = \'LOYALTY\'  AND BIRTHCERTIFICATE.cellphone=${cellphone} and datediff(day,WLTSWLSH.CREATEDATE,\'2020-10-10\')>=0 and PROFILE_BABY.IsSecBirth=1', `SEL_RESULT_VALUE_PATH` = '$.is_after_uploadLoyaltySecCert_1010', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2018-07-01 00:02:47', `UPDATE_TIME` = '2018-07-01 00:02:47', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20285;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'subchannel', `sel_level` = 0, `DESCRIPTION` = '子渠道', `DATA_TYPE` = 'int', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select top 1 SUBCHANNEL from PROFILE_MEMBER where CELLPHONE=${cellphone}', `SEL_RESULT_VALUE_PATH` = '$.subchannel', `CREATOR` = 'admin', `UPDATOR` = 'admin', `CREATE_TIME` = '2017-12-15 15:17:58', `UPDATE_TIME` = '2017-12-15 15:17:58', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20315;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'is_baby', `sel_level` = 0, `DESCRIPTION` = '是否胎次', `DATA_TYPE` = 'int', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'select count(1) from PROFILE_BABY b inner JOIN PROFILE_MEMBER m on m.Id=b.MEMBERID where m.CELLPHONE=${cellphone}', `SEL_RESULT_VALUE_PATH` = '$.is_baby', `CREATOR` = 'starry', `UPDATOR` = 'starry', `CREATE_TIME` = '2021-09-02 09:43:11', `UPDATE_TIME` = '2021-09-02 09:43:11', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20358;
UPDATE `rule_element` SET `ELEMENT_NAME` = 'ORDER_COUNT_750', `sel_level` = 0, `DESCRIPTION` = '大于等于750订单的数量', `DATA_TYPE` = 'int', `SELECTOR_TYPE` = 'DB', `SELECTOR_ID` = 2, `SEL_STATEMENT` = 'SELECT COUNT(1) FROM ORDERINFO o LEFT JOIN ORDERDETAIL d ON o.Id=d.OrderId left join DICT_PRODUCT on d.SAPID=DICT_PRODUCT.SAP_ID LEFT JOIN PROFILE_MEMBER m ON o.MemberId=m.Id WHERE o.CELLPHONE=${cellphone} AND (  d.size>=\'750\'  or (  DICT_PRODUCT.pack_size>=\'750G\') )', `SEL_RESULT_VALUE_PATH` = '$.ORDER_COUNT_750', `CREATOR` = 'xuetianhua', `UPDATOR` = 'xuetianhua', `CREATE_TIME` = '2022-11-14 12:37:33', `UPDATE_TIME` = '2022-11-14 12:37:33', `categories` = 'member', `is_visible` = 1, `display_constraints` = NULL, `display_group` = NULL, `query_flag` = 0, `platform` = 'loyalty' WHERE `ID` = 20429;
