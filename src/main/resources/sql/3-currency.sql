-- 新增用户加分信息表
DROP TABLE IF EXISTS loyalty_v2_member_currency_add;
CREATE TABLE loyalty_v2_member_currency_add(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
  `currency_id` bigint(20) NOT NULL COMMENT'货币id(campaign_gift_relationship 表的currency_book_id)',
	cellphone VARCHAR(64) NOT NULL COMMENT'消费者手机号',
	member_grade INT(11) COMMENT'会员等级',
	current_member_grade INT(11) COMMENT'当前会员等级',
	currency_amount bigint(20) NOT NULL COMMENT'收入货币数量',
	event_id bigint(20) COMMENT'事件id',
	history_table_name VARCHAR(64) COMMENT'历史表名称(如果扫码:loyalty_v2_campaign_history_scan)',
	changed_by_history_id bigint(20) COMMENT'改变积分的历史记录id【不同事件对应的历史记录表不一样】',
	changed_by_cmp_id bigint(20) COMMENT'改变积分的活动id',
	trigger_time DATETIME COMMENT'积分变更的触发时间【程序生成】',
	expire_time DATETIME COMMENT'积分过期时间',
	creator VARCHAR(64) NOT NULL COMMENT '创建人',
	updator VARCHAR(64) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
  reason_code varchar(128),
  migrate_id bigint(20) COMMENT'对应的迁移记录Id',
  migrate_key VARCHAR(128) COMMENT '对应的uniqueKey（对于日增数据这是antifake_code)',

	INDEX cellphone_currency_id(cellphone,currency_id) USING BTREE,
	INDEX cellphone(cellphone) USING BTREE,
	INDEX changed_by_cmp_id(changed_by_cmp_id) USING BTREE,
	INDEX currency_id(currency_id) USING BTREE,
	KEY(id),KEY(currency_id),KEY(cellphone),KEY(changed_by_cmp_id)
	);
-- 增加100个分区
ALTER TABLE loyalty_v2_member_currency_add PARTITION BY KEY(cellphone,currency_id) PARTITIONS 100;


-- 新增用户减分信息表
DROP TABLE IF EXISTS loyalty_v2_member_currency_reduce;
CREATE TABLE loyalty_v2_member_currency_reduce(
	id bigint(20) NOT NULL AUTO_INCREMENT,
  currency_id bigint(20) NOT NULL COMMENT'货币id(campaign_gift_relationship 表的currency_book_id)',
	cellphone VARCHAR(64) NOT NULL COMMENT'消费者手机号',
	member_grade INT(11) COMMENT'会员等级',
	currency_amount bigint(20) NOT NULL COMMENT'收入货币数量',
	event_id bigint(20) COMMENT'事件id',
	history_table_name VARCHAR(64) COMMENT'历史表名称(如果扫码:loyalty_v2_campaign_history_scan)',
	changed_by_history_id bigint(20) COMMENT'改变积分的历史记录id【不同事件对应的历史记录表不一样】',
	changed_by_cmp_id bigint(20) COMMENT'改变积分的活动id',
	reason_code varchar(128),
  migrate_id bigint(20) COMMENT'对应的迁移记录Id',
  migrate_key VARCHAR(128) COMMENT '对应的uniqueKey（对于日增数据这是antifake_code)',
	trigger_time DATETIME COMMENT'积分变更的触发时间【程序生成】',
	creator VARCHAR(64) NOT NULL COMMENT '创建人',
	updator VARCHAR(64) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	INDEX cellphone_currency_id(cellphone,currency_id) USING BTREE,
	INDEX cellphone(cellphone) USING BTREE,
	INDEX changed_by_cmp_id(changed_by_cmp_id) USING BTREE,
	INDEX currency_id(currency_id) USING BTREE,
	KEY(id),KEY(currency_id),KEY(cellphone),KEY(changed_by_cmp_id)
	);
-- 增加100个分区
ALTER TABLE loyalty_v2_member_currency_reduce PARTITION BY KEY(cellphone,currency_id) PARTITIONS 100;




--ALTER TABLE campaign_history_bxgx DROP COLUMN gift_id;
ALTER TABLE loyalty_v2_member_currency DROP COLUMN member_grade;
ALTER TABLE loyalty_v2_member_currency DROP COLUMN last_changed_by_cmpid;
ALTER TABLE loyalty_v2_member_currency DROP COLUMN last_changed_by_historyid;
ALTER TABLE loyalty_v2_member_currency DROP COLUMN last_changed_by_eventid;

alter table loyalty_v2_member_currency add last_changed_by_add_id bigint(20);
alter table loyalty_v2_member_currency add last_changed_by_reduce_id bigint(20);

alter table loyalty_v2_member_currency_add add loyalty_channel_id bigint(20);
alter table loyalty_v2_member_currency_reduce add loyalty_channel_id bigint(20);
alter table loyalty_v2_member_currency_add add used_amount bigint(20) DEFAULT 0;
alter table loyalty_v2_member_currency_add add changed_by_reduce_id bigint(20);
alter table loyalty_v2_member_currency_add add used_amount bigint(20);

DROP TABLE IF EXISTS `log_loyalty_v2_currency_amt`;
CREATE TABLE `log_loyalty_v2_currency_amt` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `member_currency_id` bigint(20)  COMMENT '货币id',
  `org_currency_amt` bigint(20)  COMMENT '减分前分数',
  `new_currency_amt` bigint(20)  COMMENT '减分后分数',
  `last_changed_by_add_id` bigint(20) DEFAULT NULL COMMENT '最后变更加分id',
  `last_changed_by_reduce_id` bigint(20) DEFAULT NULL COMMENT '最后变更减分id',
  `creator` varchar(64) NOT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  KEY `id` (`id`),
  KEY `member_currency_id` (`member_currency_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8

DROP TABLE IF EXISTS `log_loyalty_v2_currency_amt_add`;
CREATE TABLE `log_loyalty_v2_currency_amt_add` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `member_currency_id` bigint(20)  COMMENT '主键id',
  `org_used_amt` bigint(20)  COMMENT '减分前分数',
  `new_used_amt` bigint(20)  COMMENT '减分后分数',
  `last_changed_by_add_id` bigint(20) DEFAULT NULL COMMENT '最后变更加分id',
  `last_changed_by_reduce_id` bigint(20) DEFAULT NULL COMMENT '最后变更减分id',
  `member_grade` int(20)  COMMENT '会员等级',
  `creator` varchar(64) NOT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  KEY `id` (`id`),
  KEY `member_currency_id` (`member_currency_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8


DROP TRIGGER
IF EXISTS `log_loyalty_v2_currency_record_write_record_update`;
DELIMITER ;;
CREATE TRIGGER `log_loyalty_v2_currency_record_write_record_update` AFTER UPDATE ON `loyalty_v2_member_currency` FOR EACH ROW INSERT INTO log_loyalty_v2_currency_amt (
	member_currency_id,
	org_currency_amt,
	new_currency_amt,
	last_changed_by_add_id,
	last_changed_by_reduce_id,
	creator
)
VALUES
	(
		OLD.id,
		OLD.currency_amount,
		NEW.currency_amount,
		OLD.last_changed_by_add_id,
		NEW.last_changed_by_reduce_id,
		NEW.updator
	);;
DELIMITER ;

DROP TRIGGER
IF EXISTS `log_loyalty_v2_currency_add_record_write_record_update`;
DELIMITER ;;
CREATE TRIGGER `log_loyalty_v2_currency_add_record_write_record_update` AFTER UPDATE ON `loyalty_v2_member_currency_add` FOR EACH ROW INSERT INTO log_loyalty_v2_currency_amt_add (
	member_currency_id,
	org_used_amt,
	new_used_amt,
	last_changed_by_add_id,
	last_changed_by_reduce_id,
	member_grade,
	creator
)
VALUES
	(
		OLD.id,
		OLD.currency_amount,
		NEW.used_amount,
		OLD.id,
		NEW.changed_by_reduce_id,
		NEW.member_grade,
		NEW.updator
	);;
DELIMITER ;


ALTER TABLE loyalty_v2_member_currency_reduce ADD reduce_type VARCHAR(32) DEFAULT 'expireTime' COMMENT '扣分类型'