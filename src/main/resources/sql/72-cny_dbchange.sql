CREATE TABLE `campaign_history_detail_cnyshare` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `share_id` bigint(20) NOT NULL COMMENT '分享id',
  `share_detail_id` bigint(20) DEFAULT NULL COMMENT '分享明细id',
  `campaign_history_id` bigint(20) NOT NULL COMMENT 'bxgx_history_id',
  `recommended_cellphone` varchar(255) DEFAULT NULL COMMENT '推荐人手机号码',
  `redeem_qualification_id` bigint(20) DEFAULT NULL COMMENT '核销资格',
  `creator` varchar(255) NOT NULL,
  `updator` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `campaign_history_id` (`campaign_history_id`),
  KEY `share_id` (`share_id`),
  KEY `recommended_cellphone` (`recommended_cellphone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `campaign_qualification_loyalty_cny` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `qualification_id` bigint(20) NOT NULL,
  `share_id` bigint(20) NOT NULL,
  `share_detail_id` bigint(20) DEFAULT NULL,
  `gift_id` bigint(20) NOT NULL,
  `creator` varchar(64) NOT NULL COMMENT '创建人',
  `updator` varchar(64) NOT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `qualification_id_gift_uk` (`qualification_id`,`gift_id`) USING BTREE,
  KEY `qualification_id` (`qualification_id`),
  KEY `share_id` (`share_id`),
  KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `log_cny_subscribe_send_msg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `cny_subscribe_msg_info_id` bigint(20) NOT NULL COMMENT '订阅消息记录id',
  `send_data` varchar(512) NOT NULL COMMENT '发送的数据',
  `is_suc` tinyint(1) NOT NULL COMMENT '是否成功',
  `response_msg` varchar(128) DEFAULT NULL COMMENT '接口返回',
  `creator` varchar(64) NOT NULL COMMENT '行记录创建者',
  `updater` varchar(64) NOT NULL COMMENT '行记录更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '行记录创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
  PRIMARY KEY (`id`),
  KEY `ix_pclass_subscribe_msg_info_id` (`cny_subscribe_msg_info_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='订阅发送日志';


CREATE TABLE `loyalty_cny_sendoutrepocket_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `campaign_id` bigint(20) NOT NULL,
  `qualification_id` bigint(20) NOT NULL,
  `openid` varchar(64) NOT NULL,
  `history_bxgx_id` bigint(20) NOT NULL,
  `send_out_status` varchar(10) NOT NULL,
  `return_result` longtext,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `creator` varchar(32) DEFAULT NULL,
  `updator` varchar(32) DEFAULT NULL,
  `return_msg` varchar(1000) DEFAULT NULL,
  `amount` varchar(20) DEFAULT NULL COMMENT '红包发送金额',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `campaign_qualifcation_uk` (`campaign_id`,`qualification_id`),
  KEY `return_msg` (`return_msg`) USING BTREE,
  KEY `qualification_id` (`qualification_id`) USING BTREE,
  KEY `openid` (`openid`) USING BTREE,
  KEY `send_out_status` (`send_out_status`) USING BTREE,
  KEY `create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `loyalty_cny_subscribe_msg_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `cellphone` varchar(24) NOT NULL COMMENT '用户手机号码',
  `unionid` varchar(64) DEFAULT NULL COMMENT 'unionid',
  `openid` varchar(64) NOT NULL COMMENT 'openid',
  `share_id` bigint(20) NOT NULL COMMENT '分享ID',
  `campaign_id` bigint(20) NOT NULL COMMENT '活动ID',
  `wxmpp_subscribe_msg_info_id` bigint(20) NOT NULL COMMENT '订阅消息记录id',
  `is_send` tinyint(1) NOT NULL COMMENT '是否已发送',
  `creator` varchar(64) NOT NULL COMMENT '行记录创建者',
  `updater` varchar(64) NOT NULL COMMENT '行记录更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '行记录创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_ukey` (`cellphone`,`share_id`,`wxmpp_subscribe_msg_info_id`) USING BTREE,
  KEY `ix_cellphone` (`cellphone`) USING BTREE,
  KEY `ix_share_id` (`share_id`) USING BTREE,
  KEY `ix_sub_msg_id` (`wxmpp_subscribe_msg_info_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='CNY裂变消息订阅信息';

CREATE TABLE `loyalty_campaign_share` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `campaign_id` bigint(20) NOT NULL COMMENT '活动iD',
  `owner_cellphone` varchar(24) NOT NULL COMMENT '邀请人手机号码',
  `is_full` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否满团',
  `trigger_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发起时间',
  `wxa_code` longtext COMMENT '小程序分享太阳码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` varchar(255) NOT NULL COMMENT '创建人',
  `creator` varchar(255) NOT NULL COMMENT '修改人',
  `unionid` varchar(64) DEFAULT NULL COMMENT 'unionid',
  `openid` varchar(64) DEFAULT NULL COMMENT 'openid',
  PRIMARY KEY (`id`),
  UNIQUE KEY `campaign_id` (`campaign_id`,`owner_cellphone`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `loyalty_campaign_share_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `assistance_cellphone` varchar(24) NOT NULL COMMENT '助力号码',
  `share_id` bigint(20) NOT NULL COMMENT '邀请id，loyalty_campaign_share(id)',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定时间',
  `is_nua` tinyint(4) NOT NULL DEFAULT '0' COMMENT '活动期间是否购买美赞臣产品成为nua',
  `is_got` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已领取',
  `username` varchar(24) DEFAULT NULL COMMENT '用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(255) NOT NULL COMMENT '创建人',
  `updater` varchar(255) NOT NULL COMMENT '更新人',
  `nua_time` datetime DEFAULT NULL COMMENT 'nua时间',
  `has_qualification` tinyint(4) NOT NULL DEFAULT '0' COMMENT '助力成功有奖品（nua前五的才有标记送礼品）',
  `order_no` varchar(128) DEFAULT NULL,
  `gravida` int(11) DEFAULT NULL,
  `unionid` varchar(64) DEFAULT NULL COMMENT 'unionid',
  `openid` varchar(64) DEFAULT NULL COMMENT 'openid',
  PRIMARY KEY (`id`),
  UNIQUE KEY `assistance_cellphone_share` (`assistance_cellphone`,`share_id`)
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `log_loyalty_mall_register` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `register_cellphone` varchar(24) NOT NULL COMMENT '注册号码',
  `register_unionid` varchar(255) DEFAULT NULL COMMENT '注册unionid',
  `register_openid` varchar(255) DEFAULT NULL COMMENT '注册openid',
  `recruit_cellphone` varchar(12) DEFAULT NULL COMMENT '邀请人手机号',
  `sub_channel` varchar(255) DEFAULT NULL COMMENT '子渠道',
  `source` varchar(255) DEFAULT NULL COMMENT '渠道来源',
  `register_info` text COMMENT '注册信息',
  `trigger_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '触发时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(255) NOT NULL COMMENT '创建人',
  `updater` varchar(255) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `register_cellphone` (`register_cellphone`),
  KEY `sub_channel` (`sub_channel`),
  KEY `trigger_time` (`trigger_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `loyalty_wxmpp_template_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `template_name` varchar(64) NOT NULL COMMENT '模板名称',
  `pri_tmpl_id` varchar(128) NOT NULL COMMENT '模板id',
  `template_data` text NOT NULL COMMENT '模板内容',
  `is_enabled` tinyint(1) NOT NULL COMMENT '是否启用',
  `subscribe_type` varchar(256) DEFAULT NULL COMMENT '引用标识',
  `creator` varchar(64) NOT NULL COMMENT '行记录创建者',
  `updater` varchar(64) NOT NULL COMMENT '行记录更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '行记录创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_ukey` (`pri_tmpl_id`) USING BTREE,
  KEY `ix_pri_tmpl_id` (`pri_tmpl_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='微信小程序模板信息';

CREATE TABLE `loyalty_wxmpp_subscribe_msg_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `appid` varchar(24) NOT NULL COMMENT '小程序帐号ID',
  `cellphone` varchar(24) NOT NULL COMMENT '用户手机号码',
  `unionid` varchar(64) DEFAULT NULL COMMENT 'unionid',
  `openid` varchar(64) NOT NULL COMMENT 'openid',
  `event` varchar(64) DEFAULT NULL COMMENT '订阅消息弹出事件',
  `template_id` varchar(128) NOT NULL COMMENT '模板id',
  `subscribe_status` varchar(64) NOT NULL COMMENT '订阅结果,可选{accept(接收),reject(拒收),ban(封禁),filter(被过滤),acceptWithAudio(接收并语音提醒)}',
  `subscribe_time` datetime NOT NULL COMMENT '订阅时间',
  `creator` varchar(64) NOT NULL COMMENT '行记录创建者',
  `updater` varchar(64) NOT NULL COMMENT '行记录更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '行记录创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_cellphone` (`cellphone`) USING BTREE,
  KEY `ix_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='微信小程序订阅消息记录';

ALTER TABLE campaign_redpacket_info
	add column redpacket_amount double(255,3) DEFAULT NULL COMMENT '红包金额';

