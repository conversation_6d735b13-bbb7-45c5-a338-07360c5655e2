##todo: cy 加约束 (好勒)
alter table loyalty_mall_member  DROP foreign key FK_Member_ON_MemberRank_RankId;

alter table loyalty_mall_commodity_imgresource  DROP foreign key FK_CommodityImgResource_ON_Commodity_CommodityId;
alter table loyalty_mall_commodity_sizekv  DROP foreign key FK_CommoditySizeKy_ON_Commodity_CommodityId;
alter table loyalty_mall_commo_size_combine  DROP foreign key FK_CommoditySizeCombine_ON_Commodity_CommodityId;
alter table loyalty_mall_commodity_price_detail  DROP foreign key FK_CommodityPriceDetail_ON_Commodity_CommodityId;

DROP TABLE IF EXISTS `loyalty_mall_member_rank`;
CREATE TABLE `loyalty_mall_member_rank` (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(50) DEFAULT NULL,
  `rank` int(11) DEFAULT NULL,
  `remark` varchar(100) DEFAULT NULL,
  `created_by_user` bigint(20) DEFAULT NULL,
  `updated_by_user` bigint(20) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `loyalty_mall_member`;
CREATE TABLE `loyalty_mall_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cellphone` varchar(25) DEFAULT NULL,
  `openid` varchar(100) DEFAULT NULL,
  `rank_id` int(11) DEFAULT NULL COMMENT '会员等级',
  `last_login_time` datetime DEFAULT NULL,
  `created_by_user` bigint(20) DEFAULT NULL,
  `updated_by_user` bigint(20) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cellphone` (`cellphone`),
  KEY `rank_id` (`rank_id`) USING BTREE,
  CONSTRAINT `FK_Member_ON_MemberRank_RankId` FOREIGN KEY (`rank_id`) REFERENCES `loyalty_mall_member_rank` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `loyalty_mall_channel`;
CREATE TABLE `loyalty_mall_channel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `channel_code` varchar(255) DEFAULT NULL,
  `channel_name` varchar(255) DEFAULT NULL,
    `created_by_user` bigint(20) DEFAULT NULL,
    `updated_by_user` bigint(20) DEFAULT NULL,
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `udx_channel_code` (`channel_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `cmp_loyalty_world`.`loyalty_mall_channel` (`id`, `channel_code`, `channel_name`, `created_by_user`, `updated_by_user`) VALUES ('1', 'TradeMark', 'TradeMark', '1', '1');
INSERT INTO `cmp_loyalty_world`.`loyalty_mall_channel` (`id`, `channel_code`, `channel_name`, `created_by_user`, `updated_by_user`) VALUES ('2', 'ECOM', 'ECOM', '1', '1');
INSERT INTO `cmp_loyalty_world`.`loyalty_mall_channel` (`id`, `channel_code`, `channel_name`, `created_by_user`, `updated_by_user`) VALUES ('3', 'D2C', 'D2C', '1', '1');


DROP TABLE IF EXISTS `loyalty_mall_dict_cmmosupplier`;
CREATE TABLE `loyalty_mall_dict_cmmosupplier` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `corporation` varchar(50) DEFAULT NULL COMMENT '公司名称',
  `address` varchar(100) DEFAULT NULL COMMENT '公司地址',
  `charge_person` varchar(20) DEFAULT NULL COMMENT '负责人姓名',
  `charge_person_phone` varchar(11) DEFAULT NULL COMMENT '负责人电话',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `contact_person` varchar(20) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(11) DEFAULT NULL COMMENT '联系人电话',
  `return_address` text DEFAULT NULL COMMENT '售后退货地址',
  `created_by_user` int(11) DEFAULT NULL,
  `updated_by_user` int(11) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `udx_corporation` (`corporation`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `loyalty_mall_commodity`;
CREATE TABLE `loyalty_mall_commodity` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `channel_id` varchar(64) DEFAULT NULL COMMENT '渠道',
  `commodity_code` varchar(120) NOT NULL,
  `commodity_name` varchar(120) NOT NULL,
  `virtual_type` varchar(20) DEFAULT NULL COMMENT '虚拟商品类型NOT_VIRTULE_ET | VIRTULE',
  `description` varchar(120) DEFAULT NULL,
  `is_visible` int(11) DEFAULT '1' COMMENT '上架状态',
  `category` varchar(320) DEFAULT NULL,
  `classes` varchar(320) DEFAULT NULL,
  `brand_name` varchar(64) DEFAULT NULL,
  `model` varchar(64) DEFAULT NULL COMMENT '商品型号',
  `market_price` decimal(10,0) DEFAULT NULL,
  `purchase_price` decimal(10,2) DEFAULT NULL,
  `is_multi_size` tinyint(1) DEFAULT NULL,
  `is_pricing_by_memberlv` tinyint(1) DEFAULT NULL,
  `detail_content` varchar(500) DEFAULT NULL,
  `supplier_id` bigint(11) DEFAULT NULL,
  `supplier_commodity_code` varchar(120) DEFAULT NULL,
  `created_by_user` varchar(64) NOT NULL COMMENT '创建人',
  `updated_by_user` varchar(64) NOT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `shelve_time` datetime DEFAULT NULL,
`off_shelve_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `udx_commodity_name` (`commodity_name`)
) ENGINE=InnoDB AUTO_INCREMENT=520 DEFAULT CHARSET=utf8;





DROP TABLE IF EXISTS `loyalty_mall_commodity_imgresource`;
CREATE TABLE `loyalty_mall_commodity_imgresource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `commodity_id` bigint(20) NOT NULL,
  `is_cover` tinyint(1) DEFAULT NULL COMMENT '是否封面图',
  `img_file_name` varchar(50) NOT NULL COMMENT '图片名，只名称',
  `sequency` int(11) DEFAULT NULL COMMENT '轮播顺序',
  `created_by_user` int(11) DEFAULT NULL,
  `updated_by_user` int(11) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_image_commodity_id` (`commodity_id`),
  CONSTRAINT `FK_CommodityImgResource_ON_Commodity_CommodityId` FOREIGN KEY (`commodity_id`) REFERENCES `loyalty_mall_commodity` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



DROP TABLE IF EXISTS `loyalty_mall_commodity_sizekv`;
CREATE TABLE `loyalty_mall_commodity_sizekv` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `commodity_id` bigint(20) NOT NULL,
  `option_key` varchar(20) DEFAULT NULL,
  `option_value` varchar(20) DEFAULT NULL,
  `key_sequency` int(11) DEFAULT NULL COMMENT '选项从上往下排列的位置',
  `value_sequency` int(11) DEFAULT NULL,
   `created_by_user` int(11) DEFAULT NULL,
    `updated_by_user` int(11) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `udx_commodity_key_value` (`commodity_id`,`option_key`,`option_value`),
  CONSTRAINT `FK_CommoditySizeKy_ON_Commodity_CommodityId` FOREIGN KEY (`commodity_id`) REFERENCES `loyalty_mall_commodity` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `loyalty_mall_commo_size_combine`;
CREATE TABLE `loyalty_mall_commo_size_combine` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `commodity_id` bigint(20) NOT NULL,
  `combined_kv_ids` varchar(255) DEFAULT NULL,
  `combined_desc` varchar(255) DEFAULT NULL,
    `created_by_user` int(11) DEFAULT NULL,
      `updated_by_user` int(11) DEFAULT NULL,
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `udx_combined_kv_ids` (`combined_kv_ids`),
  CONSTRAINT `FK_CommoditySizeCombine_ON_Commodity_CommodityId` FOREIGN KEY (`commodity_id`) REFERENCES `loyalty_mall_commodity` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;




DROP TABLE IF EXISTS `loyalty_mall_commodity_price_detail`;
CREATE TABLE `loyalty_mall_commodity_price_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `commodity_id` bigint(20) DEFAULT NULL,
  `rank_id` int(11) DEFAULT NULL,
  `currency_details` varchar(255) DEFAULT NULL COMMENT '[ { currencyId,  amount, bookIds } ]',
  `sequency` int(11) DEFAULT NULL,
  `created_by_user` int(11) DEFAULT NULL,
  `updated_by_user` int(11) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK_CommodityPriceDetail_ON_Commodity_CommodityId` FOREIGN KEY (`commodity_id`) REFERENCES `loyalty_mall_commodity` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `loyalty_mall_commodity_storage`;
CREATE TABLE `loyalty_mall_commodity_storage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `commodity_id` bigint(20) NOT NULL COMMENT '商品id',
  `size_comb_id` bigint(20) DEFAULT NULL COMMENT '某一规格商品',
  `accumultate_amount` bigint(20) NOT NULL COMMENT '累计上架总数(总配额)',
  `current_available_amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '商城当前上架数(当前配额)',
  `safe_storage` bigint(20) NOT NULL DEFAULT '0' COMMENT '安全库存',
  `last_update_event` varchar(32) DEFAULT NULL COMMENT '最后更新库存事件',
  `last_update_by_orderid` int(11) DEFAULT NULL COMMENT '订单id',
  `creator` varchar(64) NOT NULL COMMENT '创建人',
  `updator` varchar(64) NOT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `commodity_id` (`commodity_id`,`size_comb_id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8;

CREATE TRIGGER `loyalty_mall_commodity_storage_insert_checker` BEFORE INSERT ON `loyalty_mall_commodity_storage` FOR EACH ROW BEGIN
	IF new.current_available_amount < 0 then
   SIGNAL SQLSTATE '20002'
     SET MESSAGE_TEXT = 'STORAGE NOT ENOUGH';
	END IF;
END;

CREATE TRIGGER `loyalty_mall_commodity_storage_update_checker` BEFORE UPDATE ON `loyalty_mall_commodity_storage` FOR EACH ROW BEGIN
	IF new.current_available_amount < 0 then
   SIGNAL SQLSTATE '20002'
     SET MESSAGE_TEXT = 'STORAGE NOT ENOUGH';
	END IF;
END;

CREATE TRIGGER `insert_log` AFTER UPDATE ON `loyalty_mall_commodity_storage` FOR EACH ROW BEGIN
INSERT INTO log_loyalty_mall_commodity_storage(storage_id,accumultate_amount,old_available_amount,new_available_amount,safe_storage,last_update_by_orderid,last_update_event,creator)
VALUE(NEW.ID,NEW.accumultate_amount,OLD.current_available_amount,NEW.current_available_amount,NEW.safe_storage,NEW.last_update_by_orderid,NEW.last_update_event,NEW.creator);
END;


CREATE TABLE `loyalty_mall_search_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `union_id` varchar(100) DEFAULT NULL,
  `channel_id` int(11) DEFAULT NULL,
  `keyword` varchar(100) DEFAULT NULL,
  `trigger_time` datetime DEFAULT NULL,
  `creator` varchar(64) DEFAULT NULL,
  `updator` varchar(64) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `udx_union_id_keyword,channel_id` (`union_id`,`keyword`,`channel_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=213 DEFAULT CHARSET=utf8;




DROP TABLE IF EXISTS `loyalty_mall_work_order`;
CREATE TABLE `loyalty_mall_work_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `channel_id` int(11) DEFAULT NULL,
  `work_order_type` varchar(50) DEFAULT NULL COMMENT '工单类型',
  `cellphone` varchar(20) DEFAULT NULL,
  `union_id` varchar(100) DEFAULT NULL,
  `question_description` text,
  `antifake_codes` varchar(100) DEFAULT NULL,
  `submit_time` datetime DEFAULT NULL,
  `state` int(11) DEFAULT '0',
  `remark` varchar(255) DEFAULT NULL,
   `creator` varchar(64) DEFAULT NULL,
    `updator` varchar(64) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `loyalty_mall_work_order_attachment`;
CREATE TABLE `loyalty_mall_work_order_attachment` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `wo_id` bigint(20) DEFAULT NULL,
    `attachment_type` varchar(20) DEFAULT NULL,
    `file_name` varchar(50) DEFAULT NULL,
    `creator` varchar(64) DEFAULT NULL,
     `updator` varchar(64) DEFAULT NULL,
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
