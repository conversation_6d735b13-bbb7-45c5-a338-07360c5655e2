-- 新增不同渠道信息，用来对应不同的账本
DROP TABLE IF EXISTS loyalty_v2_book_owner;
CREATE TABLE loyalty_v2_book_owner(
	id bigint(20) NOT NULL AUTO_INCREMENT,
	owner_code VARCHAR(128) NOT NULL COMMENT'账本持有人编码',
	owner_name VARCHAR(128) NOT NULL COMMENT'账本持有人名称',
	remark VARCHAR(640) COMMENT'备注详细',
	created_by_user varchar(32) NOT NULL COMMENT '创建人',
	updated_by_user varchar(32) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP
);
 ALTER TABLE loyalty_v2_book_owner ADD unique(owner_code);
-- 新增渠道
INSERT INTO loyalty_v2_book_owner (id,owner_code,owner_name,remark,created_by_user,updated_by_user) VALUES (1,'b5483071c87411e9a29f0235d2b38928','TradeMark','TradeMark',1,1);
INSERT INTO loyalty_v2_book_owner (id,owner_code,owner_name,remark,created_by_user,updated_by_user) VALUES (2,'b5483070c87411e9a29f0235d2b38928','D2C','D2C',1,1);
INSERT INTO loyalty_v2_book_owner (id,owner_code,owner_name,remark,created_by_user,updated_by_user) VALUES (3,'b548306fc87411e9a29f0235d2b38928','eCom','eCom',1,1);


-- 新增不同渠道对应的账本关系表
DROP TABLE IF EXISTS loyalty_v2_currency_book;
CREATE TABLE loyalty_v2_currency_book(
	id bigint(20) NOT NULL AUTO_INCREMENT,
	gift_id bigint(20) NOT NULL COMMENT'货币的赠品id',
	book_owner_id bigint(20) NOT NULL COMMENT'账本持有人id',
	remark VARCHAR(640) COMMENT'账本备注',
	book_name VARCHAR(64) COMMENT'账本名称',
	is_enabled tinyint(1) NOT NULL DEFAULT '1',
	created_by_user varchar(32) NOT NULL COMMENT '创建人',
	updated_by_user varchar(32) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP
);
 ALTER TABLE loyalty_v2_currency_book ADD unique(book_name);
-- 赠品表新增字段
ALTER TABLE campaign_gift ADD COLUMN gift_pool_name VARCHAR(64) DEFAULT NULL COMMENT '奖品池名字';
ALTER TABLE campaign_gift ADD COLUMN rmb_rating DOUBLE COMMENT'赠品兑RMB比例';
ALTER TABLE campaign_gift ADD COLUMN remark VARCHAR(1000) DEFAULT NULL COMMENT '描述';
-- 活动赠品关系表新增字段
ALTER TABLE campaign_gift_relationship ADD COLUMN currency_book_id INT DEFAULT NULL COMMENT '如果是LOYALTY货币，则填写账本id';
ALTER TABLE campaign_gift_relationship ADD COLUMN expire_type VARCHAR(64) COMMENT'过期类型 RELATIVE|FIX';
ALTER TABLE campaign_gift_relationship ADD COLUMN expire_date DATETIME COMMENT'如果过期类型为FIX,那该过期时间生效';
ALTER TABLE campaign_gift_relationship ADD COLUMN expire_in_hour INT COMMENT'如果过期时间为RELATIVE,那该字段生效';
--ALTER TABLE campaign_gift_relationship ADD COLUMN rmb_rating DOUBLE COMMENT'赠品在本次活动中兑RMB汇率';
