DROP TABLE IF EXISTS loyalty_admin_user;
CREATE TABLE loyalty_admin_user (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  user_name varchar(255) NOT NULL,
  cellphone varchar(255) DEFAULT NULL,
  mail_box varchar(255) DEFAULT NULL,
  remark varchar(255) DEFAULT NULL,
  password varchar(255) NOT NULL,
  is_enabled tinyint(1) NOT NULL DEFAULT '1',
  creator varchar(32) NOT NULL,
  updator varchar(32) NOT NULL,
  create_time datetime DEFAULT CURRENT_TIMESTAMP,
  update_time datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY user_name (user_name)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS loyalty_admin_user_role;
CREATE TABLE loyalty_admin_user_role (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  role_name varchar(30) NOT NULL,
  creator varchar(32) NOT NULL,
  updator varchar(32) NOT NULL,
  create_time datetime DEFAULT CURRENT_TIMESTAMP,
  update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

INSERT INTO loyalty_admin_user_role (id, role_name, creator, updator, create_time, update_time) VALUES ('1', '超级管理员', 'xuetianhua', 'xuetianhua', '2019-10-22 17:36:16', '2019-10-22 17:36:16');
INSERT INTO loyalty_admin_user_role (id, role_name, creator, updator, create_time, update_time) VALUES ('2', '管理员', 'xuetianhua', 'xuetianhua', '2019-10-22 17:36:16', '2019-10-22 17:36:16');
INSERT INTO loyalty_admin_user_role (id, role_name, creator, updator, create_time, update_time) VALUES ('3', '运营人员', 'xuetianhua', 'xuetianhua', '2019-10-22 17:36:16', '2019-10-22 17:36:16');
INSERT INTO loyalty_admin_user_role (id, role_name, creator, updator, create_time, update_time) VALUES ('4', '客服', 'xuetianhua', 'xuetianhua', '2019-10-22 17:36:16', '2019-10-22 17:36:16');

DROP TABLE IF EXISTS loyalty_admin_user_role_auth;
CREATE TABLE loyalty_admin_user_role_auth (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  role_id bigint(20) NOT NULL,
  function_id bigint(20) NOT NULL,
  authorized_by_user bigint(20) NOT NULL,
  create_time datetime DEFAULT CURRENT_TIMESTAMP,
  update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY role_id (role_id,function_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS loyalty_admin_user_function;
CREATE TABLE loyalty_admin_user_function(
  id bigint(20) NOT NULL AUTO_INCREMENT,
  function_name varchar(30) NOT NULL,
  function_code varchar(30) NOT NULL,
  creator varchar(32) NOT NULL,
  updator varchar(32) NOT NULL,
  create_time datetime DEFAULT CURRENT_TIMESTAMP,
  update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  request_path varchar(200) DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY function_code (function_code)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS loyalty_admin_user_auth;
CREATE TABLE loyalty_admin_user_auth (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  user_id bigint(20) NOT NULL,
  role_id bigint(20) NOT NULL,
  authorized_by_user bigint(20) NOT NULL,
  create_time datetime DEFAULT CURRENT_TIMESTAMP,
  update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY user_id (user_id),
  KEY role_id (role_id),
  KEY authorized_by_user (authorized_by_user),
  CONSTRAINT system_user_auth_ibfk_1 FOREIGN KEY (user_id) REFERENCES loyalty_admin_user (id),
  CONSTRAINT system_user_auth_ibfk_2 FOREIGN KEY (role_id) REFERENCES loyalty_admin_user_role (id),
  CONSTRAINT system_user_auth_ibfk_3 FOREIGN KEY (authorized_by_user) REFERENCES loyalty_admin_user (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;