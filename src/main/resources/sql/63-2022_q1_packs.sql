

------------------------------------------------------------------------------------------------------------------

rename table loyalty_v2_member_add_book to loyalty_v2_member_add_book_2022before;
rename table loyalty_v2_member_reduce_book to loyalty_v2_member_reduce_book_2022before;
RENAME TABLE loyalty_v2_book_event_exception TO loyalty_v2_book_event_exception_2022before;
-------miss loyalty_v2_book_event_exception create, create manually


CREATE TABLE loyalty_v2_member_add_book(
	id INT auto_increment,
	book_id INT NOT NULL COMMENT'账本id',
	cellphone VARCHAR(64) NOT NULL COMMENT'消费者手机号',
	currency_amount float(11,1) NOT NULL COMMENT'收入货币数量',
	event_id INT NOT NULL DEFAULT 0 COMMENT'事件id',
	changed_by_add_id INT COMMENT'改变积分的历史记录id【不同事件对应的历史记录表不一样】',

	trigger_time DATETIME NOT NULL COMMENT'积分变更的触发时间【程序生成】',
	expire_time DATETIME COMMENT'积分过期时间',
	close_expend_id INT COMMENT'平盘记录id',
	inherit_parent_id INT COMMENT'拆分自哪条加分记录的id',
	create_operation_id VARCHAR(120) COMMENT'创建操作的标识',
	close_operation_id VARCHAR(120) COMMENT'平盘使用操作的标识',

	creator VARCHAR(64) NOT NULL COMMENT '创建人',
	updator VARCHAR(64) NOT NULL COMMENT '修改人',
    create_time DATETIME DEFAULT NOW(),
    update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	INDEX book_id_cellphone(book_id,cellphone) USING BTREE,
	INDEX trigger_time(trigger_time) USING BTREE,
	INDEX expire_time(expire_time) USING BTREE,
	INDEX close_expend_id(close_expend_id) USING BTREE,
	INDEX close_operation_id(close_operation_id) USING BTREE,
	INDEX `id`(`id`) USING BTREE,
    INDEX `cellphone`(`cellphone`) USING BTREE,
    INDEX `changed_by_add_id`(`changed_by_add_id`) USING BTREE,
    INDEX `inherit_parent_id`(`inherit_parent_id`) USING BTREE,
    INDEX `cellphone_combine`(`cellphone`, `close_expend_id`) USING BTREE,
    INDEX `currency_amount`(`currency_amount`) USING BTREE,
	KEY(id),KEY(cellphone),KEY(book_id)
);

-- 增加100个分区
ALTER TABLE loyalty_v2_member_add_book PARTITION BY KEY(book_id,cellphone) PARTITIONS 100;


CREATE TABLE loyalty_v2_member_reduce_book(
	id INT auto_increment,
	book_id INT NOT NULL COMMENT'账本id',
	cellphone VARCHAR(64) NOT NULL COMMENT'消费者手机号',
	currency_amount float(11,1) NOT NULL COMMENT'收入货币数量',
	event_id INT NOT NULL DEFAULT 0 COMMENT'事件id',
	changed_by_reduce_id INT COMMENT'改变积分的历史记录id【不同事件对应的历史记录表不一样】',
	trigger_time DATETIME NOT NULL COMMENT'积分变更的触发时间【程序生成】',
	close_operation_id VARCHAR(120) COMMENT'平盘使用操作的标识',
	close_time datetime COMMENT'平盘时间',

	creator VARCHAR(64) NOT NULL COMMENT '创建人',
	updator VARCHAR(64) NOT NULL COMMENT '修改人',
    create_time DATETIME DEFAULT NOW(),
    update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	INDEX book_id_cellphone(book_id,cellphone) USING BTREE,
	INDEX trigger_time(trigger_time) USING BTREE,
	INDEX close_operation_id(close_operation_id) USING BTREE,
	INDEX `id`(`id`) USING BTREE,
	INDEX `cellphone`(`cellphone`) USING BTREE,
	KEY(id),KEY(cellphone),KEY(book_id)
);
-- 增加100个分区
ALTER TABLE loyalty_v2_member_reduce_book PARTITION BY KEY(book_id,cellphone) PARTITIONS 100;




------------------------------------------------------------------------------------------------------------------
-------------------4 min 5 sec
CREATE TABLE tmp_20220101_tmpcurrency
SELECT cellphone, SUM(currency_amount-used_amount) AS total_rest FROM loyalty_v2_member_currency_add
WHERE used_amount < currency_amount AND currency_id = 10919 AND trigger_time <'2022-01-01'
GROUP BY cellphone;

ALTER TABLE tmp_20220101_tmpcurrency ADD COLUMN packs FLOAT(11,1);

ALTER TABLE tmp_20220101_tmpcurrency ADD INDEX cellphone(cellphone);


---------------------20.842 sec
UPDATE tmp_20220101_tmpcurrency SET
packs = IF((ROUND(total_rest/200) > FLOOR(total_rest/200)) AND (total_rest/200 != FLOOR(total_rest/200)+0.5)
, CEIL(total_rest/200),
IF( total_rest/200 != FLOOR(total_rest/200),  FLOOR(total_rest/200)+0.5, total_rest/200)) ;


SELECT * FROM tmp_20220101_tmpcurrency LIMIT 1000;

-----------------------------------------------------------------
CREATE TABLE tmp_20220110_tmpcurrency
SELECT cellphone, SUM(currency_amount-used_amount) AS total_rest FROM loyalty_v2_member_currency_add
WHERE used_amount < currency_amount AND currency_id = 10919 AND trigger_time >='2022-01-01'
GROUP BY cellphone;

ALTER TABLE tmp_20220110_tmpcurrency ADD COLUMN packs FLOAT(11,1);

ALTER TABLE tmp_20220110_tmpcurrency ADD INDEX cellphone(cellphone);

UPDATE tmp_20220110_tmpcurrency SET
packs = IF((ROUND(total_rest/200) > FLOOR(total_rest/200)) AND (total_rest/200 != FLOOR(total_rest/200)+0.5)
, CEIL(total_rest/200),
IF( total_rest/200 != FLOOR(total_rest/200),  FLOOR(total_rest/200)+0.5, total_rest/200)) ;


SELECT * FROM tmp_20220110_tmpcurrency LIMIT 1000;

-----------------------------------------------------------------------
--------------------1 min 44 sec
insert into loyalty_v2_member_currency_add(
	loyalty_v2_member_currency_add.currency_id,
	loyalty_v2_member_currency_add.cellphone,
	loyalty_v2_member_currency_add.currency_amount,
	loyalty_v2_member_currency_add.event_id,
	loyalty_v2_member_currency_add.reason_code,
	loyalty_v2_member_currency_add.trigger_time,
	loyalty_v2_member_currency_add.expire_time,
	loyalty_v2_member_currency_add.loyalty_channel_id,
	loyalty_v2_member_currency_add.creator,
	loyalty_v2_member_currency_add.updator,
	loyalty_v2_member_currency_add.used_amount,
	loyalty_v2_member_currency_add.currency_book_id)
select 20328, cellphone, packs,
5,
'E_LOYALTY_CHANGE_POINT_2021',
'2021-12-31 23:59:59','2022-12-31 23:59:59', 1,'2022patch','2022patch',0, 10 from tmp_20220101_tmpcurrency;

------------------------4 min 11 sec
INSERT INTO loyalty_v2_member_currency
(
`gift_id`, `cellphone`, `currency_amount`, `creator`, `updator`, `last_changed_by_add_id`
)
SELECT 20328, cellphone, currency_amount, creator,updator, id
FROM loyalty_v2_member_currency_add
WHERE loyalty_v2_member_currency_add.`reason_code` = 'E_LOYALTY_CHANGE_POINT_2021'
ON DUPLICATE KEY UPDATE
loyalty_v2_member_currency.currency_amount= loyalty_v2_member_currency.currency_amount+(loyalty_v2_member_currency_add.`currency_amount`),
updator = '2022-packpatch', last_changed_by_add_id=VALUES(last_changed_by_add_id);

--------------------------3 min 19 sec
INSERT INTO loyalty_v2_member_add_book
(
`book_id`, `cellphone`, `currency_amount`, `event_id`, `changed_by_add_id`, `trigger_time`, `expire_time`,     `creator`, `updator`
)
SELECT 10, cellphone,    currency_amount,   event_id,    id,                trigger_time,  expire_time, creator,updator
FROM loyalty_v2_member_currency_add
WHERE loyalty_v2_member_currency_add.`reason_code` = 'E_LOYALTY_CHANGE_POINT_2021';

------------------------------------------------------------------------------------------

insert into loyalty_v2_member_currency_add(
	loyalty_v2_member_currency_add.currency_id,
	loyalty_v2_member_currency_add.cellphone,
	loyalty_v2_member_currency_add.currency_amount,
	loyalty_v2_member_currency_add.event_id,
	loyalty_v2_member_currency_add.reason_code,
	loyalty_v2_member_currency_add.trigger_time,
	loyalty_v2_member_currency_add.expire_time,
	loyalty_v2_member_currency_add.loyalty_channel_id,
	loyalty_v2_member_currency_add.creator,
	loyalty_v2_member_currency_add.updator,
	loyalty_v2_member_currency_add.used_amount,
	loyalty_v2_member_currency_add.currency_book_id)
select 20328, cellphone, packs,
5,
'E_LOYALTY_CHANGE_POINT_2022',
'2022-01-10 12:00:00','2022-12-31 23:59:59',1,'2022patch','2022patch',0, 10 from tmp_20220110_tmpcurrency;



--------------------------------------------------------------- 2 <USER> <GROUP> sec
INSERT INTO loyalty_v2_member_currency
(
`gift_id`, `cellphone`, `currency_amount`, `creator`, `updator`, `last_changed_by_add_id`
)
SELECT 20328, cellphone, currency_amount, creator,updator, id
FROM loyalty_v2_member_currency_add
WHERE loyalty_v2_member_currency_add.`reason_code` = 'E_LOYALTY_CHANGE_POINT_2022'
ON DUPLICATE KEY UPDATE
loyalty_v2_member_currency.currency_amount= loyalty_v2_member_currency.currency_amount+(loyalty_v2_member_currency_add.`currency_amount`),
updator = '2022-packpatch', last_changed_by_add_id=VALUES(last_changed_by_add_id);

--------------------------------------------------------------2 min 26 sec
INSERT INTO loyalty_v2_member_add_book
(
`book_id`, `cellphone`, `currency_amount`, `event_id`, `changed_by_add_id`, `trigger_time`, `expire_time`,     `creator`, `updator`
)
SELECT 10, cellphone,  currency_amount,   event_id,    id, trigger_time,  expire_time, creator,updator
FROM loyalty_v2_member_currency_add
WHERE loyalty_v2_member_currency_add.`reason_code` = 'E_LOYALTY_CHANGE_POINT_2022';
---------------------------------------------------------------------------


UPDATE loyalty_v2_member_add_book
INNER JOIN loyalty_v2_member_currency_add ON loyalty_v2_member_add_book.changed_by_add_id = loyalty_v2_member_currency_add.id
SET loyalty_v2_member_add_book.currency_amount = loyalty_v2_member_currency_add.`currency_amount`
WHERE (loyalty_v2_member_currency_add.updator='20220111patchplus' OR  loyalty_v2_member_currency_add.updator='20220111patchminus');






