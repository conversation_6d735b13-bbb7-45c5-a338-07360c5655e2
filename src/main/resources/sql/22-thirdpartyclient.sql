 DROP TABLE IF EXISTS loyalty_v2_log_3rdpartycall;
CREATE TABLE loyalty_v2_log_3rdpartycall(
	id INT PRIMARY KEY AUTO_INCREMENT,
    fail_record_table VARCHAR(24) NOT NULL,
	fail_record_id bigint(20) NOT NULL,
	request_senario VARCHAR(24) NOT NULL,
	request_body VARCHAR(24) NOT NULL,
	request_url VARCHAR(24) NOT NULL,
	response_body VARCHAR(24) NOT NULL,

	creator VARCHAR(60) NOT NULL COMMENT '创建人',
	updator VARCHAR(60) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP
);

alter table loyalty_v2_log_3rdpartycall add updator varchar(60);
alter table loyalty_v2_log_3rdpartycall add index request_senario(request_senario);
ALTER TABLE loyalty_v2_log_3rdpartycall ADD INDEX create_time(create_time);