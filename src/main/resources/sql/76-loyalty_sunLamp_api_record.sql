#loyalty_sunLamp_api_record

drop table if exists `loyalty_sunLamp_api_record`;
CREATE TABLE `loyalty_sunLamp_api_record` (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `appid` varchar(64) DEFAULT NULL COMMENT '请求appid',
                                              `req_platform` varchar(64) NOT NULL COMMENT '请求平台',
                                              `req_id` varchar(255) NOT NULL COMMENT '请求id',
                                              `url` varchar(255) NOT NULL COMMENT '请求地址',
                                              `req_param` text COMMENT '请求参数',
                                              `req_body` text COMMENT '请求体',
                                              `resp_body` text COMMENT '响应体',
                                              `resp_code` varchar(32) DEFAULT NULL COMMENT '响应码',
                                              `is_suc` tinyint(1) DEFAULT NULL COMMENT '是否请求成功',
                                              `trigger_time` datetime DEFAULT NULL COMMENT '触发时间',
                                              `creator` varchar(64) DEFAULT NULL COMMENT '数据创建系统',
                                              `updator` varchar(64) DEFAULT NULL COMMENT '数据更新系统',
                                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '行记录创建时间',
                                              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
                                              PRIMARY KEY (`id`),
                                              KEY `ix_req_platform_id` (`req_platform`,`req_id`),
                                              UNIQUE KEY `req_id` (`req_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='太阳码-小程序码生成记录';




INSERT INTO `loyalty_api_user`( `appid`, `app_secret`, `update_time`, `platform`) VALUES ('MJN-463_MJN@PROD', 'GO9823XF3793D362317GH7C547658D6E', NOW(), 'Howard');