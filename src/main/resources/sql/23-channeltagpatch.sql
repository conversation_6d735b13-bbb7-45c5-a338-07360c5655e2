insert into `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
select id, 'COMMODITY', 1, 1, 50,50 from `loyalty_mall_commodity`
where channel_id like '%[1]%';


INSERT INTO `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
SELECT id, 'COMMODITY', 2, 1, 50,50 FROM `loyalty_mall_commodity`
WHERE channel_id LIKE '%[2]%';




 INSERT INTO `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
SELECT id, 'COMMODITY', 3, 3, 50,50 FROM `loyalty_mall_commodity`
WHERE channel_id LIKE '%[3]%';

insert into `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
select id, 'ANNOUNCE', 1, 1, 50,50 from  `loyalty_mall_announcement`
where channel_ids like '%[1]%';

insert into `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
select id, 'ANNOUNCE', 2, 1, 50,50 from  `loyalty_mall_announcement`
where channel_ids like '%[2]%';




insert into `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
select id, 'ADV', 1, 1, 50,50 from  `loyalty_mall_adv`
where channel_ids like '%[1]%';

insert into `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
select id, 'ADV', 2, 1, 50,50 from  `loyalty_mall_adv`
where channel_ids like '%[2]%';




insert into `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
select id, 'FQA', 1, 1, 50,50 from `loyalty_mall_fqa`
where channel_id = 1;

INSERT INTO `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
SELECT id, 'FQA', 2, 1, 50,50 FROM `loyalty_mall_fqa`
WHERE channel_id  =2;








-------支付宝小程序

insert into `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
select id, resource_type, 2, 5, 50,50 from `loyalty_mall_channeltag_resource`
where tag_id =2;

insert into `loyalty_mall_channeltag_resource` (`resource_id`,`resource_type`,`tag_id`,`channel_id`, `created_by_user`, `updated_by_user`)
select id, resource_type, 1, 5, 50,50 from `loyalty_mall_channeltag_resource`
where tag_id =1;
