CREATE TABLE `loyalty_v2_log_3rdpartyebcall`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `req_desc`         varchar(320) DEFAULT NULL COMMENT '请求接口描述',
    `cellphone`        varchar(32)  DEFAULT NULL COMMENT '用户手机号',
    `send_successed`   tinyint(1) DEFAULT '0' COMMENT '请求成功标志，0-不成功 1-成功',
    `update_successed` tinyint(1) DEFAULT NULL COMMENT '重试请求标志，0-不成功 1-成功',
    `request_senario`  varchar(120) NOT NULL COMMENT '请求接口',
    `request_url`      varchar(640) DEFAULT NULL,
    `request_body`     text,
    `response_body`    text,
    `creator`          varchar(64)  NOT NULL COMMENT '创建人',
    `updator`          varchar(64)  DEFAULT NULL,
    `create_time`      datetime     DEFAULT CURRENT_TIMESTAMP,
    `update_time`      datetime     DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    KEY                `request_senario` (`request_senario`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8 COMMENT='婴贝儿接口请求失败记录表';