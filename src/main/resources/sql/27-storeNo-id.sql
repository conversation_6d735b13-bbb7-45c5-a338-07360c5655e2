CREATE TABLE `campaign_history_biz_unique_ticket` (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `unique_ticket` varchar(50) DEFAULT NULL,
  `bxgx_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_unique_ticket` (`unique_ticket`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

alter table campaign add column `join_times_constraint_type` varchar(20) DEFAULT NULL;



ALTER TABLE `campaign_history_product` ADD COLUMN belongs_store_no VARCHAR(16),ADD COLUMN belongs_store_name VARCHAR(64), add column update_time datetime default current_timestamp ,ALGORITHM=INPLACE;

ALTER TABLE `campaign_history_biz_unique_ticket`
ADD INDEX `idx` (`bxgx_id`) USING BTREE ;

UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_4' WHERE `ID` = 20000;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_2' WHERE `ID` = 20004;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_1' WHERE `ID` = 20007;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_2' WHERE `ID` = 20011;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_1' WHERE `ID` = 20012;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_1' WHERE `ID` = 20014;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_1' WHERE `ID` = 20015;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_1' WHERE `ID` = 20016;

UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_1' WHERE `ID` = 20030;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_1' WHERE `ID` = 20048;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_1' WHERE `ID` = 20051;
UPDATE `cmp`.`campaign` SET `join_times_constraint_type` = 'BYMEMBER_1' WHERE `ID` = 20066;
