-- 社群晒单抽奖兑换码
DROP TABLE IF EXISTS `loyalty_community_lucky_draw_code`;
CREATE TABLE `loyalty_community_lucky_draw_code`  (
     `id` bigint(0) NOT NULL AUTO_INCREMENT,
     `redeem_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL NOT NULL COMMENT '兑换码',
     `code_platform` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL NOT NULL COMMENT '兑换码活动code',
     `is_enabled` tinyint(1) NULL DEFAULT 1 COMMENT '可用标识1.可用 0.不可用',
     `import_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
     `user_cellphone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '兑换手机号',
     `openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'openid',
     `unionid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'unionid',
     `redeem_time` datetime(0) NULL DEFAULT null COMMENT '兑换时间',
     `creator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
     `updator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
     `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
     `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
     PRIMARY KEY (`id`) USING BTREE,
     UNIQUE INDEX `uk_redeem_code_platform`(`redeem_code`,`code_platform`) USING BTREE,
     UNIQUE INDEX `uk_platform_phone`(`code_platform`,`user_cellphone`) USING BTREE,
     INDEX `idx_redeem_code`(`redeem_code`) USING BTREE,
     INDEX `idx_code_phone`(`redeem_code`,`user_cellphone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT '社群晒单抽奖兑换码';



alter table loyalty_community_lucky_draw_code add column `end_time` datetime(0) DEFAULT NULL COMMENT '兑换码结束时间';
alter table loyalty_community_lucky_draw_code add column `start_time` datetime(0) DEFAULT NULL COMMENT '兑换码开始时间';
