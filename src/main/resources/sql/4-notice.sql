-- 新建公告设定表
DROP TABLE IF EXISTS loyalty_mall_announcement;
CREATE TABLE `loyalty_mall_announcement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `channel_ids` varchar(20) NOT NULL COMMENT '渠道id，可多个',
  `title` varchar(100) DEFAULT NULL COMMENT '公告题目',
  `content` varchar(300) DEFAULT NULL COMMENT '公告内容',
  `is_online` tinyint(1) DEFAULT NULL COMMENT '0:下线  1:上线',
  `start_at` datetime DEFAULT NULL COMMENT '公告开始时间',
  `end_at` datetime DEFAULT NULL COMMENT '公告结束时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '注解',
  `created_by_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_by_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 新建广告位置表
DROP TABLE IF EXISTS loyalty_mall_dict_advposition;
CREATE TABLE `loyalty_mall_dict_advposition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `adv_position_type` varchar(30) DEFAULT NULL COMMENT '首页轮播 | 最新活动 | 高颜值好物推荐 | 臻奇新品上架 | 个人中心banner',
  `display_type` varchar(30) DEFAULT NULL COMMENT 'SINGLE_CYCLE | LIST_CYCLE(轮播) | LIST_STATIC（列表静态图片，返回多个） | SINGLE_STATIC（单静态图，返回单图）',
  `size_width` varchar(10) DEFAULT NULL COMMENT '图片宽度',
  `size_height` varchar(10) DEFAULT NULL COMMENT '图片高度',
  `created_by_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_by_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `type` (`adv_position_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 新建广告表
drop table IF exists loyalty_mall_adv;
CREATE TABLE `loyalty_mall_adv` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `channel_ids` varchar(20) NOT NULL COMMENT '渠道id，可多个',
  `title` varchar(100) DEFAULT NULL COMMENT '广告图名称',
  `is_online` tinyint(1) DEFAULT NULL COMMENT '是否启用',
  `adv_position_id` bigint(10) DEFAULT NULL,
  `click_type` varchar(20) DEFAULT NULL COMMENT 'HREF_LINK(链接地址) | FULL_IMAGE(链接图片) | NONE(无链接) ',
  `sequency` int(10) DEFAULT NULL COMMENT '排序',
  `href_link` varchar(100) DEFAULT NULL,
  `remark` varchar(50) DEFAULT NULL,
  `start_at` datetime DEFAULT NULL,
  `end_at` datetime DEFAULT NULL,
  `created_by_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_by_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `adv_position_id` (`adv_position_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 存放各个静态板块的图片资源表
drop table IF exists loyalty_mall_adv_resource;
CREATE TABLE `loyalty_mall_adv_resource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `adv_id` bigint(20) DEFAULT NULL,
  `resource_file_name` varchar(100) DEFAULT NULL COMMENT '图片文件名',
  `resource_type` varchar(50) DEFAULT NULL COMMENT 'COVER (广告图片) | FULL_IMAGE (链接图片)',
  `created_by_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_by_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `adv_id` (`adv_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
