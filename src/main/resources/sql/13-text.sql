DROP TABLE IF EXISTS loyalty_mall_dict_text;
CREATE TABLE loyalty_mall_dict_text(
 id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  channel_id bigint(10),
  text_area VARCHAR(20),
  int_code VA<PERSON>HA<PERSON>(40),
  ext_code <PERSON><PERSON><PERSON><PERSON>(40),
  ext_desc VARCHAR(100),
  int_desc VARCHAR(100),
  priority int(4),
  is_enabled bool,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id)
);
