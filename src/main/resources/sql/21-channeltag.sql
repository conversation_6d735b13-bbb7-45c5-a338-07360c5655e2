DROP TABLE IF EXISTS loyalty_mall_dict_channeltag;
CREATE TABLE loyalty_mall_dict_channeltag(
	id bigint(20) PRIMARY KEY auto_increment,
	tag_name VARCHAR(24) NOT NULL,
	tag_code VARCHAR(24) NOT NULL unique,
	created_by_user INT NOT NULL COMMENT '创建人',
	updated_by_user INT NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP
);



DROP TABLE IF EXISTS loyalty_mall_memberchannel;
 DROP TABLE IF EXISTS loyalty_mall_memberchannel;
CREATE TABLE loyalty_mall_memberchannel(
	id bigint(20) auto_increment,
	cellphone VARCHAR(24) NOT NULL unique,
	sync_tag_id	bigint(20),
	manual_tag_id 	bigint(20),
	current_tag_id	bigint(20) not null,
	current_tag_type VARCHAR(24) NOT NULL,
	sync_time	datetime,
	can_sync	bool not null default true,

	last_modified_by	varchar(60),
	last_modifer_id		bigint(20),
	last_modify_remark		varchar(60),
	last_modify_time	datetime,

	creator varchar(60) NOT NULL COMMENT '创建人',
	updator varchar(60) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,

  key(id),
  primary key(cellphone)
)
partition by key(cellphone) partitions 100;


DROP TABLE IF EXISTS log_loyalty_mall_memberchannel;

CREATE TABLE log_loyalty_mall_memberchannel(
	id BIGINT(20) PRIMARY KEY AUTO_INCREMENT,
	source_id BIGINT(20) NOT NULL UNIQUE,

	old_tag_id bigint(20),
	old_last_modified_by	varchar(60),
	old_last_modifer_id 	BIGINT(20),
	old_last_modify_remark		VARCHAR(200),

  new_tag_id bigint(20),
  new_last_modified_by	varchar(60),
	new_last_modifer_id 	BIGINT(20),
	new_last_modify_remark		varchar(200),

  create_time DATETIME DEFAULT NOW()
);


ALTER TABLE log_loyalty_mall_memberchannel ADD INDEX new_last_modified_by_id(new_last_modified_by, new_last_modifer_id);
ALTER TABLE log_loyalty_mall_memberchannel ADD INDEX new_last_modified_by(new_last_modified_by);
ALTER TABLE log_loyalty_mall_memberchannel ADD INDEX new_last_modified_by_mid( new_last_modifer_id);
ALTER TABLE log_loyalty_mall_memberchannel ADD INDEX create_time( create_time);

drop trigger IF EXISTS log_loyalty_mall_memberchannel_update;

DELIMITER $$
CREATE TRIGGER log_loyalty_mall_memberchannel_update AFTER UPDATE ON loyalty_mall_memberchannel
FOR EACH ROW
BEGIN
    if old.current_tag_id != new.current_tag_id then
    insert into log_loyalty_mall_memberchannel(source_id, old_tag_id, old_last_modified_by, old_last_modifer_id, old_last_modify_remark, new_tag_id, new_last_modified_by, new_last_modifer_id, new_last_modify_remark)
    values(old.id, old.current_tag_id, old.last_modified_by, old.last_modifer_id, old.last_modify_remark,  new.current_tag_id, new.last_modified_by, new.last_modifer_id, new.last_modify_remark);
    end if;
END $$;

ALTER TABLE `loyalty_v2_member_currency_add` ADD COLUMN user_tag_id INT(10), ADD COLUMN currency_book_id INT(10), ALGORITHM=INPLACE;

ALTER TABLE `loyalty_v2_member_currency_reduce` ADD COLUMN user_tag_id INT(10), ALGORITHM=INPLACE;