DROP TABLE IF EXISTS `loyalty_mall_channeltag_resource`;
CREATE TABLE `loyalty_mall_channeltag_resource` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `resource_id` bigint(20) NOT NULL COMMENT '资源表主键id',
  `resource_type` varchar(64) NOT NULL COMMENT '资源类型',
  `tag_id` bigint(20) DEFAULT NULL COMMENT '标签id',
  `channel_id` bigint(20) DEFAULT NULL COMMENT '渠道id',
  `created_by_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by_user` varchar(64) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk` (`resource_id`,`resource_type`,`tag_id`,`channel_id`),
  KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;


alter table loyalty_mall_commodity drop column channel_id;
alter table loyalty_mall_announcement drop column channel_ids;
alter table loyalty_mall_adv drop column channel_ids;
alter table loyalty_mall_fqa drop column channel_id;



create table loyalty_v2_campaign_gift_price (
 id bigint(20) primary key auto_increment,
 gift_id bigint(20) not null,
 currency_id BIGINT(20) NOT NULL,
 currency_price double not null,
 `created_by_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by_user` varchar(64) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk` (`gift_id`)
);

alter table loyalty_mall_purchase_order add current_member_tag_id bigint(20);
--todo: event记录和这个表的初始化数据

INSERT INTO `cmp`.`loyalty_v2_event`(`id`, `event_code`, `description`, `direction`, `creator`, `updator`, `create_time`, `update_time`) VALUES (8, 'CAMPAIGN_REDUCE', '参与活动', 'OUT', 'admin', 'admin', '2020-02-21 12:30:53', '2020-02-21 14:22:22');
