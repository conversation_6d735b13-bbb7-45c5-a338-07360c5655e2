-- 扫码历史表
DROP TABLE IF EXISTS `loyalty_v2_campaign_history_scan`;
CREATE TABLE `loyalty_v2_campaign_history_scan` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cellphone` varchar(64) NOT NULL COMMENT '消费者手机号',
  `openid` varchar(64) NOT NULL COMMENT '消费者openid',
  `campaign_id` bigint(20) NOT NULL COMMENT '活动主表id',
  `antifake_code` varchar(64) NOT NULL COMMENT '防伪码',
  `traceability_code` varchar(64) COMMENT '罐码',
  `creator` varchar(64) NOT NULL COMMENT '记录创建人',
  `updator` varchar(64) NOT NULL COMMENT '记录最后更新人',
   `status` varchar(32) DEFAULT NULL COMMENT '记录状态',
  trigger_time datetime not null default now(),
  `longitude` varchar(32) DEFAULT NULL COMMENT '经度',
  `latitude` varchar(32) DEFAULT NULL COMMENT '纬度',
  `unionId` varchar(64) DEFAULT NULL COMMENT 'unionId',
  UNIQUE KEY `cellphone_campaign_id_antifake_code` (`cellphone`,`campaign_id`,`antifake_code`),
  INDEX cellphone(cellphone) USING BTREE,
  INDEX openid(openid) USING BTREE,
  INDEX campaign_id(campaign_id) USING BTREE,
  INDEX antifake_code(antifake_code) USING BTREE,
  KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
ALTER TABLE loyalty_v2_campaign_history_scan PARTITION BY KEY(cellphone,campaign_id,antifake_code) PARTITIONS 50;

-- 扫码成功积分表
DROP TABLE IF EXISTS `loyalty_v2_campaign_history_scan_detail`;
CREATE TABLE `loyalty_v2_campaign_history_scan_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `scan_history_id` bigint(20) NOT NULL COMMENT '扫码历史主表id',
  `senario` varchar(64) NOT NULL COMMENT '获取奖励情景common_base 其他| scan_code_base 扫码积分|upload_babycert上传准生证',
  `gift_id` bigint(20) NOT NULL COMMENT '赠品id',
  `reason_object` text COMMENT '加分规则执行rawdata',
  `reason_group_id` bigint(20) COMMENT '对应的加分规则组',
  `reason_remark` varchar(64) COMMENT '原因简单描述',
  `amount` int(11) NOT NULL COMMENT '获得赠品数量',
  `trigger_time` DATETIME NOT NULL COMMENT '真正扫码时间',
  `creator` varchar(64) NOT NULL COMMENT '记录创建人',
  `updator` varchar(64) NOT NULL COMMENT '记录最后更新人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX scan_history_id(scan_history_id) USING BTREE,
  INDEX trigger_time(trigger_time) USING BTREE,
  KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
ALTER TABLE loyalty_v2_campaign_history_scan_detail PARTITION BY KEY(scan_history_id) PARTITIONS 10;

-- 扫码结果表
DROP TABLE IF EXISTS `loyalty_v2_campaign_scan_result`;
CREATE TABLE `loyalty_v2_campaign_scan_result` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `antifake_code` varchar(64) NOT NULL COMMENT '防伪码',
  `cellphone` varchar(64) NOT NULL COMMENT '消费者电话',
  `is_scored_suc` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否成功',
  `reason_object` text NOT NULL COMMENT '积分规则执行rawdata',
  `scan_history_id` bigint(20) DEFAULT NULL COMMENT '扫码历史表id',
  `creator` varchar(64) NOT NULL COMMENT '记录创建人',
  `updator` varchar(64) NOT NULL COMMENT '记录最后更新人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `unionId` varchar(64) DEFAULT NULL,
  `campaign_id` bigint(20) DEFAULT NULL COMMENT '活动id',
  KEY `cellphone` (`cellphone`) USING BTREE,
  KEY `antifake_code` (`antifake_code`) USING BTREE,
  KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8
ALTER TABLE loyalty_v2_campaign_scan_result PARTITION BY KEY(cellphone,antifake_code) PARTITIONS 100;

alter table campaign_gift_relationship add expire_type varchar(64);
alter table campaign_gift_relationship add datetime;
alter table campaign_gift_relationship add expire_in_hour int(20);

ALTER TABLE campaign_gift_relationship ADD currency_book_id BIGINT(20);
ALTER TABLE campaign_gift ADD COLUMN rmb_rating DOUBLE;
ALTER TABLE campaign_gift_relationship DROP COLUMN rmb_rating;
