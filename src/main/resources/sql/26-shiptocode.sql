DROP TABLE IF EXISTS `loyalty_v2_dict_shiptocode`;
CREATE TABLE `loyalty_v2_dict_shiptocode` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shiptocode` varchar(24) NOT NULL,
  `owner_code` varchar(24) NOT NULL,
  `distributor_name` varchar(64) DEFAULT NULL,
  `creator` int(11) NOT NULL COMMENT '创建人',
  `updator` int(11) NOT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `shiptocode` (`shiptocode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;