ALTER TABLE `loyalty_v2_logistics_data` ADD COLUMN from_cellphone VARCHAR(24), ALGORITHM=INPLACE, LOCK=NONE;

ALTER TABLE loyalty_v2_logistics_data_detail ADD COLUMN good_count INT(8), ALGORITHM=INPLACE, LOCK=NONE;

ALTER TABLE `loyalty_v2_logistics_data` ADD COLUMN channel_id INT(10), ALGORITHM=INPLACE, LOCK=NONE;

alter table loyalty_v2_logistics_data add index from_cellphone(from_cellphone), ALGORITHM=INPLACE, LOCK=NONE;


UPDATE loyalty_v2_logistics_data
INNER  JOIN loyalty_mall_purchase_order ON loyalty_mall_purchase_order.id = loyalty_v2_logistics_data.`mall_order_id`
SET from_cellphone = loyalty_mall_purchase_order.cellphone, loyalty_v2_logistics_data.channel_id =  loyalty_mall_purchase_order.channel_id;



UPDATE loyalty_v2_logistics_data
INNER JOIN campaign_history_bxgx ON loyalty_v2_logistics_data.`campaign_history_bxgxid` = campaign_history_bxgx.id
SET from_cellphone = campaign_history_bxgx.`USER_CELLPHONE`, loyalty_v2_logistics_data.channel_id = campaign_history_bxgx.channel_id;

UPDATE loyalty_v2_logistics_data_detail
INNER JOIN loyalty_v2_logistics_data ON loyalty_v2_logistics_data.id = loyalty_v2_logistics_data_detail.`logistics_data_id`
INNER JOIN campaign_history_bxgx ON loyalty_v2_logistics_data.`campaign_history_bxgxid` = campaign_history_bxgx.id
INNER JOIN campaign_history_gift ON campaign_history_bxgx.id = campaign_history_gift.`history_bxgx_id` AND loyalty_v2_logistics_data_detail.`gift_id` = campaign_history_gift.`gift_id`
SET loyalty_v2_logistics_data_detail.good_count = campaign_history_gift.`amount`;

UPDATE loyalty_v2_logistics_data_detail a
INNER  JOIN loyalty_mall_purchase_order_detail b ON a.`order_detail_id` = b.`id`
SET a.good_count = b.`commodity_amount`;
