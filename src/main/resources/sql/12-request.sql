DROP TABLE IF EXISTS loyalty_v2_biz_request;
CREATE TABLE loyalty_v2_biz_request(
 id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  request_unique_key VARCHAR(40) NOT NULL,
  process_data LONGTEXT,
  channel_id INT(10) NOT NULL,
  event_id INT(10) NOT NULL,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KE<PERSON> (id),
  PRIMARY KEY(request_unique_key, channel_id, event_id)
);
ALTER TABLE loyalty_v2_biz_request PARTITION BY KEY(request_unique_key, channel_id, event_id) PARTITIONS 100;

DROP TABLE IF EXISTS loyalty_v2_log_interface;
CREATE TABLE loyalty_v2_log_interface(
  id BIGINT AUTO_INCREMENT,
  platform VARCHAR(32) ,
  campaign_id BIGINT(10),
  channel_id BIGINT(10),
  cellphone VARCHAR(32),
  product_info varchar(1000),
  result_code VARCHAR(16),
  request_path VARCHAR(512),
  request_body LONGTEXT,
  result_body LONGTEXT ,
  sender VARCHAR(10),
  create_time DATETIME NOT NULL DEFAULT  NOW(),
  KEY(create_time),
  KEY(cellphone),
  KEY(request_path),
  KEY(id),
  key(product_info)
);

ALTER TABLE loyalty_v2_log_interface PARTITION BY KEY(cellphone,create_time, request_path) PARTITIONS 100;



CREATE TABLE `loyalty_v2_schedule_task` (
 id BIGINT(10) PRIMARY KEY AUTO_INCREMENT,
 task_name VARCHAR(30) NOT NULL,
 is_suc BOOL NOT NULL,
 data_time DATETIME,
 job_time DATETIME NOT NULL DEFAULT NOW()
);