DROP TABLE IF EXISTS loyalty_v2_dict_currency_reason;
CREATE TABLE loyalty_v2_dict_currency_reason	(
	id INT PRIMARY KEY auto_increment,
	reason_code VARCHAR(64) NOT NULL,
	reason_remark VARCHAR(128),
	senario VARCHAR(32),
	created_by_user varchar(32) NOT NULL COMMENT '创建人',
	updated_by_user varchar(32) NOT NULL COMMENT '修改人',
  create_time DATETIME DEFAULT NOW(),
  update_time DATETIME DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
	UNIQUE KEY reason_code(reason_code,senario)
);


INSERT INTO `loyalty_v2_dict_currency_reason`(`id`, `reason_code`, `reason_remark`, `senario`, `created_by_user`, `updated_by_user`, `create_time`, `update_time`) VALUES (1, 'E_0001', '商城商品兑换', NULL, '1', '1', '2019-11-07 17:22:06', '2019-11-07 17:22:06');
INSERT INTO `loyalty_v2_dict_currency_reason`(`id`, `reason_code`, `reason_remark`, `senario`, `created_by_user`, `updated_by_user`, `create_time`, `update_time`) VALUES (2, 'E_0002', '工单处理', NULL, '1', '1', '2019-11-07 17:22:32', '2019-11-07 17:22:32');
INSERT INTO `loyalty_v2_dict_currency_reason`(`id`, `reason_code`, `reason_remark`, `senario`, `created_by_user`, `updated_by_user`, `create_time`, `update_time`) VALUES (3, 'E_0004', '退货扣减', NULL, '1', '1', '2019-11-07 17:23:06', '2019-11-07 17:23:06');
INSERT INTO `loyalty_v2_dict_currency_reason`(`id`, `reason_code`, `reason_remark`, `senario`, `created_by_user`, `updated_by_user`, `create_time`, `update_time`) VALUES (5, 'E_0005', '积分过期', NULL, '1', '1', '2019-11-07 17:23:06', '2019-11-07 17:23:06');

INSERT INTO `loyalty_v2_dict_currency_reason`(`reason_code`, `reason_remark`, `senario`, `created_by_user`, `updated_by_user`, `create_time`, `update_time`) VALUES ( 'E_WE_001', '臻金卡抽奖活动', NULL, '1', '1', '2019-11-07 17:23:06', '2019-11-07 17:23:06');

  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_addPoint' , '扫码累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_ncAddPoint' , 'NC手动积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint' , 'NNC扫码累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_luckyAddPoint' , '抽奖累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminAddPoint' , '管理员手动累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminAddPoint-timeout' , '积分补登','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_idCardAddPoint' , '出生证上传累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_stageBonusAddPoint' , '转段奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_keepBuyBonusAddPoint' , '持续购买奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_s3FirstBonusAddPoint' , 's3首罐奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actB2g1BonusAddPoint' , '活动B2G1奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actMultiBonusAddPoint' , '活动多罐多倍奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actNuaBonusAddPoint' , '活动Nua奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actRetentionBonusAddPoint' , '活动Retention奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actQ2BonusAddPoint' , '活动Q2奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_act150percentBonusAddPoint' , '活动1.5倍累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actsouthAreaBonusAddPoint' , '活动南区翻倍累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_acteastDoubleAddPoint' , '活动东区翻倍累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actq3ChineseAddPoint' , '多罐多倍活动积累','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actq3leyouDoubleBonusAddPoint','乐友双倍积分累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actq3aiyinshiDoubleBonusAddPoint','爱婴室双倍积分累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actq3haiziwangDoubleBonusAddPoint','孩子王双倍积分累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminActq3ChineseAddPoint' , '多罐多倍活动积累补登','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminActq3leyouDoubleBonusAddPoint','乐友双倍积分累积补登','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminActq3aiyinshiDoubleBonusAddPoint','爱婴室双倍积分累积补登','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminActTimeoutAddPoint','扫码累积补登','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminGiftredeemAddPoint1200','取消订单退还积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminGiftredeemAddPoint1000','取消订单退还积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminGiftredeemAddPoint600','取消订单退还积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actoldClient800PointPoint','臻粉狂欢趴活动奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actFullReturnStrongBonusAddPoint','多罐多倍活动奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actLeyouReturnStrongBonusAddPoint','乐友满4罐2倍积分奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actAiyinshiReturnStrongBonusAddPoint','爱婴室满4罐2倍积分奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actHaiziwangReturnStrongBonusAddPoint','孩子王满4罐2倍积分奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_uguAddPoint','ugu积分奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_act20190315_all','全国门店多罐多倍活动','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_act20190315_haiziwang','孩子王门店多罐多倍活动','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_act20190315_walmart','沃尔玛多罐多倍奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_act20190315_oldClient','老客800分奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actShop4610AddPoint','米氏多罐多倍奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actSapidAddPoint','指定SAPID双倍积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_oldClient1200Point','华北老客1200奖励积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_taskStageChangeAddPoint','转段成长积金','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_taskKeepBuyAddPoint','臻情回馈积金','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actShopSamAddPoint','山㟂门店多倍奖励','1','1');

  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-stageBonusAddPoint' , 'NNC 转段奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-keepBuyBonusAddPoint' , 'NNC 持续购买奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-s3FirstBonusAddPoint' , 'NNC s3首罐奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actB2g1BonusAddPoint' , 'NNC 活动B2G1奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actMultiBonusAddPoint' , 'NNC 活动多罐多倍奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actNuaBonusAddPoint' , 'NNC 活动Nua奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actRetentionBonusAddPoint' , 'NNC 活动Retention奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actQ2BonusAddPoint' , 'NNC 活动Q2奖励累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-act150percentBonusAddPoint' , '活动1.5倍累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actsouthAreaBonusAddPoint' , '活动南区翻倍累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-acteastDoubleAddPoint' , '活动东区翻倍累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actq3ChineseAddPoint' , '多罐多倍活动积累','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actq3leyouDoubleBonusAddPoint','乐友双倍积分累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actq3aiyinshiDoubleBonusAddPoint','爱婴室双倍积分累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actq3haiziwangDoubleBonusAddPoint','孩子王双倍积分累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actoldClient800PointPoint','臻粉狂欢趴活动奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actFullReturnStrongBonusAddPoint','多罐多倍活动奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actLeyouReturnStrongBonusAddPoint','乐友满4罐2倍积分奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actAiyinshiReturnStrongBonusAddPoint','爱婴室满4罐2倍积分奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actHaiziwangReturnStrongBonusAddPoint','孩子王满4罐2倍积分奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actShop4610AddPoint','米氏多罐多倍奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-act20190315_all','全国门店多罐多倍活动','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-act20190315_haiziwang','孩子王门店多罐多倍活动','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-act20190315_walmart','沃尔玛多罐多倍奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-act20190315_oldClient','老客800分奖励','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actSapidAddPoint','指定SAPID双倍积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-oldClient1200Point','华北老客1200奖励积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-taskStageChangeAddPoint','转段成长积金','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-taskKeepBuyAddPoint','臻情回馈积金','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_nncAddPoint-actShopSamAddPoint','山㟂门店多倍奖励','1','1');

  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_zhenJinCardAddPoint','臻金卡奖励积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_tmallAddPoint' , '天猫订单增加积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_fillQuestionAddPoint' , '填写问卷累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_mallReducePoint' , '商城兑换消耗','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_returnGoodsReducePoint' , '退货后消耗','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_adminReducePoint' , '管理员手动消耗','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_cloudReducePoint' , '天猫商城消耗','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_clearExpirePoint' , '过期积分清理','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_tmallOneyuanReducePoint' , '天猫1元购重复积分扣减','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_actNuaGiftReducePoint' , 'Nua活动兑换赠品消耗','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_ncReturnGoodsReducePoint','NC操作退分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_pointDrawReducePoint','积分夺宝积分扣减','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_pointDrawAddPoint','积分夺宝积分中奖','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_pointFreeDrawAddPoint','积分夺宝积分返利','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_newMumUploadAddPoint','管理员手动累积','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_addOrderAddPoint','d2c订单奖励积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_d2cOrderSmallProgramAddPoint','d2c订购小程序订单奖励积分','1','1');
  INSERT INTO loyalty_v2_dict_currency_reason(reason_code,reason_remark, created_by_user, updated_by_user) VALUES('M_d2cOrderActAddPoint','d2c订单美赞臣蓝臻活动期间奖励积分','1','1');