INSERT INTO `cmp`.`rule_element`(`ID`, `ELEMENT_NAME`, `sel_level`, `DESCRIPTION`, `DATA_TYPE`, `SELECTOR_TYPE`, `SELECTOR_ID`, `SEL_STATEMENT`, `SEL_RESULT_VALUE_PATH`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `categories`, `is_visible`, `display_constraints`, `display_group`, `query_flag`, `platform`)
VALUES (20233, 'lh_user_canjoin_campaignlist', 0, '手机号可参与活动ID', 'varchar', 'DB', 4, ' SELECT GROUP_CONCAT(CONCAT(\'[\',campaign_user_cellphone_qualification.campaign_id,\']\') SEPARATOR \',\') FROM campaign_user_cellphone_qualification WHERE user_cellphone = ${cellphone} and is_include=true', '$.lh_user_canjoin_campaignlist', 'admin', 'admin', '2019-03-15 17:14:04', '2019-03-15 17:14:04', 'member', 1, NULL, NULL, 0, 'loyalty');
INSERT INTO `cmp`.`rule_element`(`ID`, `ELEMENT_NAME`, `sel_level`, `DESCRIPTION`, `DATA_TYPE`, `SELECTOR_TYPE`, `SELECTOR_ID`, `SEL_STATEMENT`, `SEL_RESULT_VALUE_PATH`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `categories`, `is_visible`, `display_constraints`, `display_group`, `query_flag`, `platform`)
VALUES (20234, 'lh_user_cantjoin_campaignlist', 0, '手机号不可参与活动ID', 'varchar', 'DB', 4, ' SELECT GROUP_CONCAT(CONCAT(\'[\',campaign_user_cellphone_qualification.campaign_id,\']\') SEPARATOR \',\') FROM campaign_user_cellphone_qualification WHERE user_cellphone = ${cellphone} and is_include=false', '$.lh_user_cantjoin_campaignlist', 'admin', 'admin', '2019-03-15 17:14:04', '2019-03-15 17:14:04', 'member', 1, NULL, NULL, 0, 'loyalty');


SELECT * FROM campaign WHERE id IN (20066,20067,20068) ORDER BY id ASC;
SELECT `campaign_quota`.* FROM `campaign_quota`, `campaign_gift_relationship` WHERE campaign_quota.`ID` = campaign_gift_relationship.`quota_id` AND  campaign_gift_relationship.`CAMPAIGN_ID` IN (20066,20067,20068);
SELECT * FROM rule WHERE id IN (SELECT `rule_group_detail`.`rule_id` FROM `rule_group_detail`, `rule_group`, `campaign_rule_info`
WHERE campaign_rule_info.`rule_group_id` = rule_group.id AND rule_group.id = rule_group_detail.`rule_group_id` AND campaign_rule_info.`CAMPAIGN_ID` IN (20066,20067,20068)) ORDER BY id ASC;
SELECT * FROM campaign_gift WHERE id IN (SELECT gift_id FROM campaign_gift_relationship WHERE campaign_id IN (20066,20067,20068)) ORDER BY id ASC;




---todo: 双倍积分规则手动搞
INSERT INTO  `rule_element`(`ID`, `ELEMENT_NAME`, `sel_level`, `DESCRIPTION`, `DATA_TYPE`, `SELECTOR_TYPE`, `SELECTOR_ID`, `SEL_STATEMENT`, `SEL_RESULT_VALUE_PATH`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `categories`, `is_visible`, `display_constraints`, `display_group`, `query_flag`, `platform`) VALUES (20233, 'lh_user_canjoin_campaignlist', 0, '手机号可参与活动ID', 'varchar', 'DB', 4, ' SELECT GROUP_CONCAT(CONCAT(\'[\',campaign_user_cellphone_qualification.campaign_id,\']\') SEPARATOR \',\') FROM campaign_user_cellphone_qualification WHERE user_cellphone = ${cellphone} and is_include=true', '$.lh_user_canjoin_campaignlist', 'admin', 'admin', '2019-03-15 17:14:04', '2019-03-15 17:14:04', 'member', 1, NULL, NULL, 0, 'loyalty');
INSERT INTO  `rule_element`(`ID`, `ELEMENT_NAME`, `sel_level`, `DESCRIPTION`, `DATA_TYPE`, `SELECTOR_TYPE`, `SELECTOR_ID`, `SEL_STATEMENT`, `SEL_RESULT_VALUE_PATH`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `categories`, `is_visible`, `display_constraints`, `display_group`, `query_flag`, `platform`) VALUES (20234, 'lh_user_cantjoin_campaignlist', 0, '手机号不可参与活动ID', 'varchar', 'DB', 4, ' SELECT GROUP_CONCAT(CONCAT(\'[\',campaign_user_cellphone_qualification.campaign_id,\']\') SEPARATOR \',\') FROM campaign_user_cellphone_qualification WHERE user_cellphone = ${cellphone} and is_include=false', '$.lh_user_cantjoin_campaignlist', 'admin', 'admin', '2019-03-15 17:14:04', '2019-03-15 17:14:04', 'member', 1, NULL, NULL, 0, 'loyalty');


INSERT INTO  `campaign`(`ID`, `CAMPAIGN_NAME`, `DESCRIPTION`, `PLATFORM`, `START_TIME`, `END_TIME`, `IS_ENABLED`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `SIGNING_START_TIME`, `SIGNING_END_TIME`, `campaign_series`, `check_properties`, `quota_dimension`, `dis_quota_senario`, `IS_DELETED`, `has_enabled_before`, `int_name`, `int_description`, `campaign_senario`, `is_editable`) VALUES (20066, 'LT_V2_202002_S33TIMES_SCAN', '完成一罐3段蓝臻850克/900克产品积分', 'TradeMark', '2020-03-07 00:00:00', '2020-04-01 00:00:00', 1, '28', '28', '2020-02-24 14:49:27', '2020-02-24 16:04:31', NULL, NULL, 'LT_V2_LH_SCANCODE', NULL, NULL, 'ACTION', 0, 1, '蓝臻限时3倍积分(扫码)', '蓝臻限时3倍积分(扫码)', 'scan_additional', 1);
INSERT INTO  `campaign`(`ID`, `CAMPAIGN_NAME`, `DESCRIPTION`, `PLATFORM`, `START_TIME`, `END_TIME`, `IS_ENABLED`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `SIGNING_START_TIME`, `SIGNING_END_TIME`, `campaign_series`, `check_properties`, `quota_dimension`, `dis_quota_senario`, `IS_DELETED`, `has_enabled_before`, `int_name`, `int_description`, `campaign_senario`, `is_editable`) VALUES (20067, 'LT_V2_202002_S33TIMES', '领取叠加积分奖励(+400积分)', 'TradeMark', '2020-03-07 00:00:00', '2020-04-01 00:00:00', 1, '28', '28', '2020-02-24 15:43:42', '2020-02-24 17:53:58', NULL, NULL, 'LT_V2_GENERIC_GETPOINT', NULL, NULL, 'ACTION', 0, 1, '蓝臻限时3倍积分（领取）', '蓝臻限时3倍积分（领取）', NULL, 1);
INSERT INTO  `campaign`(`ID`, `CAMPAIGN_NAME`, `DESCRIPTION`, `PLATFORM`, `START_TIME`, `END_TIME`, `IS_ENABLED`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `SIGNING_START_TIME`, `SIGNING_END_TIME`, `campaign_series`, `check_properties`, `quota_dimension`, `dis_quota_senario`, `IS_DELETED`, `has_enabled_before`, `int_name`, `int_description`, `campaign_senario`, `is_editable`) VALUES (20068, 'LT_V2_202002_S33TIMES_NCQUERY', '蓝臻指定用户三倍积分', 'TradeMark', '2020-03-07 00:00:00', '2020-04-01 00:00:00', 1, '28', '28', '2020-02-26 10:45:23', '2020-02-26 10:45:23', NULL, NULL, 'LT_V2_QUALIFICATION', NULL, NULL, 'ACTION', 0, 1, '蓝臻指定用户三倍积分', '蓝臻指定用户三倍积分', NULL, 1);

INSERT INTO `rule`( `NAME`, `DESCRIPTION`, `expression`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `categories`, `rule_parse_opt`, `data_source_url`, `platform`) VALUES ( 'LH_S33TIMES_202003_MEMBER', '蓝臻3月转段3倍', '[[{\"ruleElementId\":20233,\"ruleOperator\":{\"operatorType\":\"STRING_CONTAINS\",\"operatorValue\":\"CONTAINS\"},\"ruleValues\":[\"20066\"],\"ruleElementsAssignments\":{}},{\"ruleElementId\":20152,\"ruleOperator\":{\"operatorType\":\"STRING_CONTAINS\",\"operatorValue\":\"NOT_CONTAINS\"},\"ruleValues\":[\"20066\"],\"ruleElementsAssignments\":{}}]]', '28', '28', '2020-02-24 15:01:58', '2020-02-24 16:02:07', 'member', 'AND', NULL, 'loyalty');
INSERT INTO `rule`( `NAME`, `DESCRIPTION`, `expression`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `categories`, `rule_parse_opt`, `data_source_url`, `platform`) VALUES ( 'LH_S33TIMES_202003_GETPOINT', '蓝臻3月转段3倍领取规则', '[[{\"ruleElementId\":20152,\"ruleOperator\":{\"operatorType\":\"STRING_CONTAINS\",\"operatorValue\":\"NOT_CONTAINS\"},\"ruleValues\":[\"20067\"],\"ruleElementsAssignments\":{}},{\"ruleElementId\":20164,\"ruleOperator\":{\"operatorType\":\"STRING_CONTAINS\",\"operatorValue\":\"CONTAINS\"},\"ruleValues\":[\"20067\"],\"ruleElementsAssignments\":{}}]]', '28', '28', '2020-02-24 15:42:37', '2020-02-24 17:53:54', 'member', 'AND', NULL, 'loyalty');
INSERT INTO `rule`( `NAME`, `DESCRIPTION`, `expression`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `categories`, `rule_parse_opt`, `data_source_url`, `platform`) VALUES ( 'LT_V2_S33TIMES_PURCHASE', '指定用户S33倍扫码规则', '[[{\"ruleElementId\":20147,\"ruleOperator\":{\"operatorType\":\"BASIC_TYPE\",\"operatorValue\":\"=\"},\"ruleValues\":[\"Enfinitas\",\"LH\"],\"ruleElementsAssignments\":{}},{\"ruleElementId\":20149,\"ruleOperator\":{\"operatorType\":\"BASIC_TYPE\",\"operatorValue\":\"=\"},\"ruleValues\":[850.0,900.0],\"ruleElementsAssignments\":{}},{\"ruleElementId\":20144,\"ruleOperator\":{\"operatorType\":\"BASIC_TYPE\",\"operatorValue\":\"=\"},\"ruleValues\":[3.0],\"ruleElementsAssignments\":{}}]]', '28', '28', '2020-02-24 15:53:02', '2020-02-24 16:04:28', 'purchase', 'AND', NULL, 'loyalty');
INSERT INTO `rule`( `NAME`, `DESCRIPTION`, `expression`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `categories`, `rule_parse_opt`, `data_source_url`, `platform`) VALUES ( 'LH_200307_S33TIMES_NCQUERY', '蓝臻指定用户3倍积分开通资格查询', '[[{\"ruleElementId\":20233,\"ruleOperator\":{\"operatorType\":\"STRING_CONTAINS\",\"operatorValue\":\"CONTAINS\"},\"ruleValues\":[\"20066\"],\"ruleElementsAssignments\":{}},{\"ruleElementId\":20152,\"ruleOperator\":{\"operatorType\":\"STRING_CONTAINS\",\"operatorValue\":\"NOT_CONTAINS\"},\"ruleValues\":[\"20067\"],\"ruleElementsAssignments\":{}}]]', '28', '28', '2020-02-26 10:45:13', '2020-02-26 10:45:13', 'member', 'AND', NULL, 'loyalty');

INSERT INTO `campaign_gift`(`ID`, `GIFT_NAME`, `TYPE`, `GIFT_VALUE`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`, `GIFT_SERIES`, `sap_id`, `platform`, `external_gift_code`, `gift_thumbnail`, `gift_mid_thumbnail`, `rmb_rating`, `logistics_supllier_id`, `gift_pool_name`) VALUES (20050, '蓝臻现时3倍资格（20年3月）', 'LOYALTY_QUALIFICATION', '20067', '28', '28', '2020-02-24 15:49:21', '2020-02-24 15:49:32', NULL, NULL, 'LOYALTY', NULL, NULL, NULL, NULL, 52, 'Offline赠品池');

INSERT INTO `cmp`.`loyalty_v2_dict_currency_reason`(`id`, `reason_code`, `reason_remark`, `senario`, `created_by_user`, `updated_by_user`, `create_time`, `update_time`)
VALUES (144, 'C_20068', '限时3倍积分', NULL, '28', '28', '2020-02-26 10:45:23', '2020-02-26 10:45:23');
