SELECT * FROM experience_amount
INNER JOIN
(SELECT cellphone, SUM(change_amt*change_flag) AS total FROM
`experience_statement` GROUP BY cellphone) AS tmp ON  experience_amount.`cellphone` = tmp.cellphone
WHERE experience_amount.`current_amt` != tmp.total;


ALTER TABLE `experience_amount_copy` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `experience_amount_copy`
MODIFY cellphone VARCHAR(64) CHARACTER SET utf8mb4  COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会员手机号';

ALTER TABLE experience_amount_copy MODIFY creator VARCHA<PERSON>(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE experience_amount_copy MODIFY updator VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-------------------------复制表到具体数据库后往下执行
INSERT INTO loyalty_v2_member_currency_add(
	loyalty_v2_member_currency_add.currency_id,
	loyalty_v2_member_currency_add.cellphone,
	loyalty_v2_member_currency_add.currency_amount,
	loyalty_v2_member_currency_add.event_id,
	loyalty_v2_member_currency_add.reason_code,
	loyalty_v2_member_currency_add.trigger_time,
	loyalty_v2_member_currency_add.loyalty_channel_id,
	loyalty_v2_member_currency_add.creator,
	loyalty_v2_member_currency_add.updator,
	loyalty_v2_member_currency_add.used_amount,
	loyalty_v2_member_currency_add.currency_book_id)
SELECT 10919, cellphone, current_amt,
5,
'E_EXP_ADD_POINT',
NOW(),1,'2022-exppatch','2022-exppatch',0, 10 FROM experience_amount_copy;


------------------------------4 min 32 sec
INSERT INTO loyalty_v2_member_currency
(
`gift_id`, `cellphone`, `currency_amount`, `creator`, `updator`, `last_changed_by_add_id`
)
SELECT 10919, cellphone, currency_amount, creator,updator, id
FROM loyalty_v2_member_currency_add
WHERE loyalty_v2_member_currency_add.`reason_code` = 'E_EXP_ADD_POINT'
ON DUPLICATE KEY UPDATE
loyalty_v2_member_currency.currency_amount= loyalty_v2_member_currency.currency_amount+(loyalty_v2_member_currency_add.`currency_amount`),
updator = '2022-exppatch', last_changed_by_add_id=VALUES(last_changed_by_add_id);


-----------------------------2 min 25 sec
INSERT INTO loyalty_v2_member_add_book
(
`book_id`, `cellphone`, `currency_amount`, `event_id`, `changed_by_add_id`, `trigger_time`, `expire_time`,     `creator`, `updator`
)
SELECT 10, cellphone,    currency_amount,   event_id,    id,                trigger_time,  '2022-12-31 23:59:59', creator,updator
FROM loyalty_v2_member_currency_add
WHERE loyalty_v2_member_currency_add.`reason_code` = 'E_EXP_ADD_POINT';

