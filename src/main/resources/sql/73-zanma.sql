drop table if exists `loyalty_bogof_campaign`;
create table `loyalty_bogof_campaign` (
    `id` int(10) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
    `cc_campaign_type` varchar(64) NOT NULL COMMENT '驰骛cmp活动类型',
    `template_id` varchar(64) NOT NULL COMMENT '模板类型id',
    `config_properties` text  COMMENT '配置属性集合，json格式',
    `remark` varchar(255)  COMMENT '备注',
    `created_by_user` int(10)  COMMENT '行记录创建用户id',
    `updated_by_user` int(10)  COMMENT '行记录更新用户id',
    `create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
    `update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
    UNIQUE KEY uk_uk_type(cc_campaign_type),
    INDEX ix_ik_type(cc_campaign_type),
    INDEX ix_ik_template_id(template_id)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赞妈有礼-活动页面配置';
drop table if exists `loyalty_bogof_campaign_record`;
create table `loyalty_bogof_campaign_record` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`cellphone` varchar(32) NOT NULL COMMENT '手机号',
	`openid` varchar(64)  COMMENT 'openid',
	`unionid` varchar(64)  COMMENT 'unionid',
	`cc_campaign_id` bigint(20)  COMMENT '驰骛活动id',
	`cc_campaign_instId` varchar(64)  COMMENT '池骛活动实例id',
	`cc_campaign_type` varchar(64) NOT NULL COMMENT '驰骛cmp活动类型',
	`cc_campaign_code` varchar(64) NOT NULL COMMENT '驰骛cmp活动编码',
	`cc_participated_time` datetime NOT NULL COMMENT '驰骛cmp活动参与时间',
	`order_no` varchar(64)  COMMENT '外部订单编号',
	`gravida` int(2)  COMMENT '胎次',
	`longitude` decimal(10,7)  COMMENT '经度',
	`latitude` decimal(10,7)  COMMENT '纬度',
	`trigger_time` datetime  COMMENT '触发时间',
	`creator` varchar(64)  COMMENT '数据创建系统',
	`updater` varchar(64)  COMMENT '数据更新系统',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	UNIQUE KEY uk_uk_order_no(order_no),
	INDEX ix_ik_cellphone(cellphone),
	INDEX ix_ik_cc_campaign_id(cc_campaign_id),
	INDEX ix_ik_campaign_id(cc_campaign_id,cellphone) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赞妈有礼-活动参与记录';
drop table if exists `loyalty_bogof_campaign_product_scan`;
create table `loyalty_bogof_campaign_product_scan` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`cc_campaign_id` bigint(20) NOT NULL COMMENT '驰骛活动id',
	`cc_campaign_instId` varchar(64)  COMMENT '池骛活动实例id',
	`cellphone` varchar(32) NOT NULL COMMENT '消费者手机号',
	`digitcode` varchar(32)  COMMENT '防伪码',
	`gravida` int(2)  COMMENT '胎次',
	`traceability_code` varchar(32)  COMMENT '追溯码',
	`product_sapid` varchar(32)  COMMENT '产品sapid',
    `product_name` varchar(32)  COMMENT '产品名称',
	`has_qualification` bool  COMMENT '防伪码是否满足活动资格',
	`trigger_time` datetime  COMMENT '扫码时间',
	`creator` varchar(64)  COMMENT '数据创建系统',
	`updater` varchar(64)  COMMENT '数据更新系统',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_ik_cc_campaign_id(cc_campaign_id),
	INDEX ix_ik_traceability_code(traceability_code),
	INDEX ix_ik_cellphone(cellphone),
	INDEX ix_ik_cc_campaign_instId(cc_campaign_instId) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赞妈有礼-罐码扫码记录';
drop table if exists `loyalty_bogof_campaign_product_detail`;
create table `loyalty_bogof_campaign_product_detail` (
    `id` bigint(20) NOT NULL COMMENT 'id',
    `campaign_record_id` bigint(20) NOT NULL COMMENT '活动参与记录ID',
    `campaign_product_scan_id` bigint(20)  COMMENT '扫码记录ID',
    `traceability_code` varchar(32)  COMMENT '追溯码',
    `digitcode` varchar(32)  COMMENT '防伪码',
    `product_sapid` varchar(32)  COMMENT '产品sapid',
    `creator` varchar(64)  COMMENT '数据创建系统',
    `updater` varchar(64)  COMMENT '数据更新系统',
    `create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
    `update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
    INDEX ix_ik_campaign_record_id(campaign_record_id),
    INDEX ix_ik_traceability_code(traceability_code),
    INDEX ix_campaign_record_id(campaign_record_id)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赞妈有礼-罐码参与明细';
drop table if exists `log_loyalty_bogof_wechat_authorize`;
create table `log_loyalty_bogof_wechat_authorize` ( 
	 `id` bigint(20) NOT NULL COMMENT 'id',
	`cellphone` varchar(32) NOT NULL COMMENT '手机号',
	`cc_campaign_id` bigint(20)  COMMENT '驰骛活动id',
	`openid` varchar(64)  COMMENT 'openid',
	`unionid` varchar(64)  COMMENT 'unionid',
	`auth_type` varchar(32) NOT NULL COMMENT '授权类型',
	`is_auth` bool  COMMENT '是否授权',
	`creator` varchar(64)  COMMENT '数据创建系统',
	`updater` varchar(64)  COMMENT '数据更新系统',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_ik_cellphone(cellphone) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赞妈有礼-微信授权日志';
drop table if exists `loyalty_bogof_campaign_gift_detail`;
create table `loyalty_bogof_campaign_gift_detail` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`loyalty_bogof_campaign_record_id` bigint(20) NOT NULL COMMENT '活动参与记录',
	`gift_id` bigint(20) NOT NULL COMMENT '赠品id',
	`gift_name` varchar(64)  COMMENT '赠品名称',
	`gift_value` varchar(64)  COMMENT '赠品编码',
	`gift_img_url` varchar(128)  COMMENT '赠品图片',
	`youan_order_sn` varchar(64)  COMMENT '优安订单id',
	`trigger_time` datetime  COMMENT '触发时间',
	`creator` varchar(64)  COMMENT '数据创建系统',
	`updater` varchar(64)  COMMENT '数据更新系统',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	UNIQUE KEY uk_uk_record_gift_id(loyalty_bogof_campaign_record_id,gift_id),
	INDEX ix_ik_record_id(loyalty_bogof_campaign_record_id) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赞妈有礼-活动赠品记录';
drop table if exists `loyalty_bogof_campaign_api_record`;
create table `loyalty_bogof_campaign_api_record` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`api_platform` varchar(64) NOT NULL COMMENT '接口平台，可选{YOUAN_API(优安接口),CC_API(驰骛接口)}',
	`cellphone` varchar(32) NOT NULL COMMENT '手机号',
	`loyalty_bogof_campaign_record_id` bigint(20) NOT NULL COMMENT '活动参与记录',
	`url` varchar(255) NOT NULL COMMENT '请求地址',
	`req_param` text  COMMENT '请求参数',
	`req_body` text  COMMENT '请求体',
	`resp_body` text  COMMENT '响应体',
	`resp_code` varchar(32)  COMMENT '响应码',
	`is_suc` bool  COMMENT '是否请求成功',
	`trigger_time` datetime  COMMENT '触发时间',
	`creator` varchar(64)  COMMENT '数据创建系统',
	`updater` varchar(64)  COMMENT '数据更新系统',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_ik_record_id_api(api_platform,loyalty_bogof_campaign_record_id) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赞妈有礼-api调用记录';
drop table if exists `loyalty_subscribe_msg_info`;
create table `loyalty_subscribe_msg_info` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`cellphone` varchar(32) NOT NULL COMMENT '手机号',
	`unionid` varchar(64) NOT NULL,
	`openid` varchar(64) NOT NULL,
	`source_table` varchar(255) NOT NULL COMMENT '来源table',
	`source_id` bigint(20) NOT NULL COMMENT '来源table_id',
	`campaign_id` bigint(20) ,
	`wxmpp_subscribe_msg_info_id` bigint(20)  COMMENT '订阅消息记录id',
	`is_send` tinyint(1)  COMMENT '是否已发送',
	`creator` varchar(64)  COMMENT '数据创建系统',
	`updater` varchar(64)  COMMENT '数据更新系统',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_ik_source(source_table,source_id),
	INDEX ix_ik_cellphone(cellphone) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='loyalty通用消息订阅信息';
drop table if exists `log_loyalty_subscribe_send_msg`;
create table `log_loyalty_subscribe_send_msg` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`loyalty_subscribe_msg_info_id` bigint(20) NOT NULL COMMENT 'loyalty_subscribe_msg_info.id',
	`send_data` text NOT NULL,
	`is_suc` tinyint(1)  COMMENT '是否成功',
	`response_msg` varchar(255) NOT NULL COMMENT '返回body',
	`creator` varchar(64)  COMMENT '数据创建系统',
	`updater` varchar(64)  COMMENT '数据更新系统',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_ik_msg_id(loyalty_subscribe_msg_info_id) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='loyalty通用消息订阅信息';
drop table if exists `business_unique_history`;
CREATE TABLE `business_unique_history` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `biz_unique_key` varchar(255) NOT NULL COMMENT '业务唯一key',
    `business_type` varchar(100) NOT NULL COMMENT '业务类型：BOGOF',
    `business_table` varchar(100) NOT NULL COMMENT '业务表',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `biz_unique_key_2` (`biz_unique_key`,`business_type`,`business_table`),
    KEY `biz_unique_key_index` (`biz_unique_key`,`business_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

ALTER TABLE `loyalty_bogof_campaign_gift_detail` ADD COLUMN `gift_inst_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '赠品实例id' AFTER `gift_id`;

drop table if exists `loyalty_bogof_campaign_redbooklet_detail`;
CREATE TABLE `loyalty_bogof_campaign_redbooklet_detail` (
    `id` int NOT NULL AUTO_INCREMENT,
    `loyalty_bogof_campaign_record_id` bigint DEFAULT NULL COMMENT '活动参与记录',
    `cc_campaign_instId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动实例id',
    `red_booklet_img_url` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '小红书图片阿里云链接',
    `red_booklet_link` varchar(512) COLLATE utf8mb4_general_ci NOT NULL COMMENT '小红书文章链接',
    `trigger_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '触发时间',
    `creator` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
    `updater` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `cc_campaign_instId` (`cc_campaign_instId`),
    KEY `ix_ik_recordId` (`loyalty_bogof_campaign_record_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE `loyalty_bogof_campaign` DROP INDEX `uk_uk_type`;
ALTER TABLE `loyalty_bogof_campaign` ADD COLUMN `cc_campaign_id` bigint(0) NULL DEFAULT NULL COMMENT '池骛活动id' AFTER `cc_campaign_type`;
ALTER TABLE `loyalty_bogof_campaign` ADD INDEX `ix_ik_type_id`(`cc_campaign_type`, `cc_campaign_id`) USING BTREE;

CREATE TABLE `loyalty_bogof_campaign_sunlamp_detail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `campaign_id` bigint NOT NULL COMMENT '活动id',
  `pnec_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'pnecId',
  `plat_form` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台标识',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生成的图片url',
  `skip_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跳转路径',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据创建系统',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据更新系统',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '行记录创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_campaignId_pnecId` (`campaign_id`,`pnec_id`) USING BTREE,
  KEY `ik_campaignId` (`campaign_id`) USING BTREE,
  KEY `ik_pnecId` (`pnec_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE `loyalty_bogof_campaign_gift_detail` ADD COLUMN `gift_type` varchar(64) NULL DEFAULT NULL COMMENT '赠品类型' AFTER `gift_id`;

ALTER TABLE `loyalty_bogof_campaign_record` ADD COLUMN `cc_campaign_name` varchar(128) NULL COMMENT '驰骛活动名称' AFTER `cc_campaign_type`;
ALTER TABLE `loyalty_bogof_campaign_gift_detail` ADD COLUMN `gift_num` int NULL COMMENT '赠品数量' AFTER `gift_value`;
ALTER TABLE `loyalty_bogof_campaign_redbooklet_detail` ADD COLUMN `audit_status` varchar (32) NULL COMMENT '审核状态{WAIT:待审核,AUDITING:审核中,PASS:审核通过,NO_PASS:审核不通过}' AFTER `red_booklet_link`;
ALTER TABLE `loyalty_bogof_campaign_gift_detail` ADD COLUMN `supplier` varchar(32) NULL COMMENT '供应商' AFTER `gift_type`,
ADD COLUMN `outbound_position` varchar(255) NULL COMMENT '发货仓位' AFTER `supplier`;

