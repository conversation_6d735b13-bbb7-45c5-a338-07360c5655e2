alter table experience_amount_copy add column patched_in2021 bool not null default true;
alter table experience_amount_copy add column has_exception bool not null default false;

update
experience_amount_copy
LEFT JOIN
tmp_20220101_tmpcurrency
ON experience_amount_copy.cellphone = tmp_20220101_tmpcurrency.`cellphone`
set patched_in2021=false
WHERE tmp_20220101_tmpcurrency.`cellphone` IS NULL;

update
experience_amount_copy
inner JOIN
loyalty_v2_book_event_exception ON experience_amount_copy.`cellphone` = loyalty_v2_book_event_exception.`cellphone`
set has_exception=true
WHERE err_msg = '货币数量不足以扣除' AND patched_in2021 = FALSE;




<!--校验 0-->
SELECT * FROM experience_amount_copy
INNER JOIN `loyalty_v2_member_currency_add` ON experience_amount_copy.`cellphone` = loyalty_v2_member_currency_add.`cellphone`
WHERE patched_in2021 = FALSE
AND loyalty_v2_member_currency_add.`reason_code` = 'E_LOYALTY_CHANGE_POINT_2021';

SELECT * FROM experience_amount_copy
INNER JOIN `loyalty_v2_member_currency_add` ON experience_amount_copy.`cellphone` = loyalty_v2_member_currency_add.`cellphone`
WHERE has_exception = true;




UPDATE loyalty_v2_member_currency
INNER JOIN experience_amount_copy ON loyalty_v2_member_currency.cellphone = experience_amount_copy.`cellphone`
SET currency_amount = currency_amount-experience_amount_copy.packs, loyalty_v2_member_currency.updator = '20210413'
WHERE experience_amount_copy.patched_in2021= FALSE AND has_exception=FALSE AND  loyalty_v2_member_currency.`gift_id` = 20328 AND
currency_amount-experience_amount_copy.packs >=0;

不够扣和有异常的未处理

-- insert into loyalty_v2_member_currency_add(
-- 	loyalty_v2_member_currency_add.currency_id,
-- 	loyalty_v2_member_currency_add.cellphone,
-- 	loyalty_v2_member_currency_add.currency_amount,
-- 	loyalty_v2_member_currency_add.event_id,
-- 	loyalty_v2_member_currency_add.reason_code,
-- 	loyalty_v2_member_currency_add.trigger_time,
-- 	loyalty_v2_member_currency_add.expire_time,
-- 	loyalty_v2_member_currency_add.loyalty_channel_id,
-- 	loyalty_v2_member_currency_add.creator,
-- 	loyalty_v2_member_currency_add.updator,
-- 	loyalty_v2_member_currency_add.used_amount,
-- 	loyalty_v2_member_currency_add.currency_book_id)
-- select 20328, cellphone, packs,
-- 5,
-- 'E_LOYALTY_CHANGE_POINT_2021',
-- '2021-12-31 23:59:59','2022-12-31 23:59:59', 1,'20220413patch','20220413patch',0, 10 from experience_amount_copy WHERE patched_in2021 = FALSE;
--
-- INSERT INTO loyalty_v2_member_add_book
-- (
-- `book_id`, `cellphone`, `currency_amount`, `event_id`, `changed_by_add_id`, `trigger_time`, `expire_time`,     `creator`, `updator`
-- )
-- SELECT 10, cellphone,    currency_amount,   event_id,    id,                trigger_time,  '2022-12-31 23:59:59', creator,updator
-- FROM loyalty_v2_member_currency_add
-- WHERE loyalty_v2_member_currency_add.`updator` = '20220413patch';