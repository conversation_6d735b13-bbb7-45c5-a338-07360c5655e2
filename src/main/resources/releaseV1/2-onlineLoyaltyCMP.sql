ALTER TABLE `campaign_external_loyalty` RENAME AS old_campaign_external_loyalty;
ALTER TABLE `campaign_config_loyalty` RENAME AS old_campaign_config_loyalty;
ALTER TABLE `loyalty_add_point` RENAME AS old_loyalty_add_point;
ALTER TABLE `loyalty_add_point_1011` RENAME AS old_loyalty_add_point_1011;
ALTER TABLE `loyalty_exchange_history` RENAME AS old_loyalty_exchange_history;
ALTER TABLE `loyalty_fail_reason` RENAME AS old_loyalty_fail_reason;
ALTER TABLE `loyalty_nc_list` RENAME AS old_loyalty_nc_list;
ALTER TABLE `loyalty_reduce_point` RENAME AS old_loyalty_reduce_point;
ALTER TABLE `loyalty_reduce_point_1011` RENAME AS old_loyalty_reduce_point_1011;
ALTER TABLE `loyalty_target_member` RENAME AS old_loyalty_target_member;
ALTER TABLE `loyalty_target_member_list` RENAME AS old_loyalty_target_member_list;
ALTER TABLE `loyalty_target_store_list` RENAME AS old_loyalty_target_store_list;


alter table campaign_rule_info
	add column active_start_time datetime DEFAULT NULL comment '规则启用时间',
	add column active_end_time datetime DEFAULT NULL comment '规则停用时间',
	add column is_enabled  tinyint(1)  DEFAULT TRUE,
	add column output_type varchar(256) DEFAULT NULL,
	add column output_result varchar(128) DEFAULT NULL;

alter table campaign_rule_info add column rule_group_id	bigint(20) DEFAULT null;






-- DATA PATCH

update campaign_rule_info set active_start_time= EXTERNAL_PROPERTIES->>"$.startTime" , 	active_end_time= EXTERNAL_PROPERTIES->>"$.endTime" ,
  is_enabled= (case when EXTERNAL_PROPERTIES->>"$.enabled" = 'true' then 1 when  EXTERNAL_PROPERTIES->>"$.enabled" = 'false' then 0 else NULL END)
WHERE EXTERNAL_PROPERTIES IS NOT NULL	;


update 	campaign_rule_info set
		output_type=
			  if(
						LOCATE('=', RULE_EXECUTED_VALUE)>0,
						substring(
								RULE_EXECUTED_VALUE, 1, LOCATE('=', RULE_EXECUTED_VALUE)-1
						) ,
						NULL
				),
		output_result =
				if(
						LOCATE('=', RULE_EXECUTED_VALUE)>0,
						substring(
								RULE_EXECUTED_VALUE,  LOCATE('=', RULE_EXECUTED_VALUE)+1
						) ,
						NULL
				)
  where RULE_EXECUTED_VALUE is not null and RULE_EXECUTED_VALUE!='loyalty_common_rule';

--



-- 删除唯一索引
alter table campaign_rule_info drop index CAMPAIGN_ID;

alter table campaign_rule_info add index index_rule_group_id(rule_group_id);
alter table campaign_rule_info add foreign key rule_group_id_ibfk(rule_group_id) references rule_group(id);
-- 添加唯一索引
alter table campaign_rule_info add CONSTRAINT  uk_group_output unique (campaign_id, rule_group_id, dimension_id, priority, output_type, output_result) ;

update campaign_rule_info set output_type='loyalty_common_rule' where RULE_EXECUTED_VALUE='loyalty_common_rule'	;



-- rule_element 索引问题
ALTER TABLE `rule_element` DROP INDEX `ELEMENT_NAME`,ADD UNIQUE `uk_name_platform`(`ELEMENT_NAME`, `platform`);


---数据写入
UPDATE rule SET platform='cmp' WHERE categories !='loyalty';

INSERT IGNORE INTO rule_group(id, group_name, group_code, remark, created_by_user, updated_by_user)
SELECT id, description, NAME, NULL, 'init', 'init' FROM rule WHERE rule.`categories` != 'loyalty';

INSERT IGNORE INTO rule_group_detail(rule_group_id, rule_id, created_by_user, updated_by_user)
SELECT id, id,'init', 'init' FROM rule_group WHERE id >0;

UPDATE campaign_rule_info INNER JOIN rule_group ON rule_group.`id` = campaign_rule_info.`RULE_ID`
SET rule_group_id = rule_id;

ALTER TABLE rule_group ADD COLUMN categories VARCHAR(20);

UPDATE rule_group INNER JOIN rule ON rule.id = rule_group.id SET rule_group.`categories` = rule.`categories`;

--以下脚本必须在执行完 java 的datapatch 脚本后再执行
ALTER TABLE `campaign_rule_info` DROP COLUMN rule_id, DROP COLUMN rule_executed_value;
ALTER TABLE campaign_rule_info DROP FOREIGN KEY campaign_rule_info_ibfk_2;
ALTER TABLE `campaign_rule_info` DROP COLUMN rule_id, DROP COLUMN rule_executed_value;