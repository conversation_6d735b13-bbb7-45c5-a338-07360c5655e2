CREATE TABLE order_return_history(
  id BIGINT(20) PRIMARY KEY AUTO_INCREMENT,
  user_cellphone VARCHAR(20) NOT NULL,
  order_detail_id BIGINT(20) NOT NULL,
  remark VARCHAR(500) NOT NULL,
  updator VARCHAR(20),
  creator <PERSON><PERSON><PERSON><PERSON>(20),
  create_time DATETIME NOT NULL DEFAULT NOW(),
  update_time DATETIME NOT NULL DEFAULT NOW() ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE(order_detail_id)
);