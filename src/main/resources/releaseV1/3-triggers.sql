DELIMITER $$
CREATE  TRIGGER `loyalty_v2_gift_storage_Insertchecker` BEFORE INSERT ON loyalty_v2_gift_storage FOR EACH ROW BEGIN
IF new.`storage_amount` < 0 then
   SIGNAL SQLSTATE '20002'
     SET MESSAGE_TEXT = 'GIFT STORAGE_AMOUNT NOT ENOUGH';
END IF;
END $$;

DELIMITER $$
CREATE  TRIGGER loyalty_v2_gift_storage_Updatechecker BEFORE UPDATE ON loyalty_v2_gift_storage FOR EACH ROW BEGIN
IF new.storage_amount < 0 then
   SIGNAL SQLSTATE '20002'
     SET MESSAGE_TEXT = 'GIFT STORAGE_AMOUNT NOT ENOUGH';
END IF;
END $$;

DELIMITER $$
CREATE TRIGGER loyalty_mall_commodity_storage_insert_checker BEFORE INSERT ON loyalty_mall_commodity_storage FOR EACH ROW
BEGIN
	IF new.current_available_amount < 0 THEN
   SIGNAL SQLSTATE '20002'
     SET MESSAGE_TEXT = 'STORAGE NOT ENOUGH';
	END IF;
END $$;
-- 更新数量少于0报错触发器
DELIMITER $$
CREATE TRIGGER loyalty_mall_commodity_storage_update_checker BEFORE UPDATE ON loyalty_mall_commodity_storage FOR EACH ROW
BEGIN
	IF new.current_available_amount < 0 THEN
   SIGNAL SQLSTATE '20002'
     SET MESSAGE_TEXT = 'STORAGE NOT ENOUGH';
	END IF;
END $$;

DELIMITER $$
DROP TRIGGER
IF EXISTS `log_loyalty_v2_currency_record_write_record_update`;
DELIMITER ;;
CREATE TRIGGER `log_loyalty_v2_currency_record_write_record_update` AFTER UPDATE ON `loyalty_v2_member_currency` FOR EACH ROW INSERT INTO log_loyalty_v2_currency_amt (
	member_currency_id,
	org_currency_amt,
	new_currency_amt,
	last_changed_by_add_id,
	last_changed_by_reduce_id,
	creator
)
VALUES
	(
		OLD.id,
		OLD.currency_amount,
		NEW.currency_amount,
		OLD.last_changed_by_add_id,
		NEW.last_changed_by_reduce_id,
		NEW.updator
	);;
DELIMITER ;

DROP TRIGGER
IF EXISTS `log_loyalty_v2_currency_add_record_write_record_update`;
DELIMITER ;;
CREATE TRIGGER `log_loyalty_v2_currency_add_record_write_record_update` AFTER UPDATE ON `loyalty_v2_member_currency_add` FOR EACH ROW INSERT INTO log_loyalty_v2_currency_amt_add (
	member_currency_id,
	org_used_amt,
	new_used_amt,
	last_changed_by_add_id,
	last_changed_by_reduce_id,
	member_grade,
	creator
)
VALUES
	(
		OLD.id,
		OLD.currency_amount,
		NEW.used_amount,
		OLD.id,
		NEW.changed_by_reduce_id,
		NEW.member_grade,
		NEW.updator
	);;
DELIMITER ;





CREATE  TRIGGER `log_loyalty_v2_gift_storage_record_insert` AFTER INSERT ON loyalty_v2_gift_storage
		FOR EACH ROW INSERT INTO log_loyalty_v2_gift_storage_record(`loyalty_v2_gift_storage_id`,`gift_id`,`storage_amount`,`creator`)VALUES(NEW.`id`,NEW.`gift_id`,NEW.`storage_amount`,NEW.updator);


CREATE  TRIGGER `log_loyalty_v2_gift_storage_recordd_update` AFTER UPDATE ON loyalty_v2_gift_storage
		FOR EACH ROW INSERT INTO log_loyalty_v2_gift_storage_record(`loyalty_v2_gift_storage_id`,`gift_id`,`changed_delta`,`storage_amount`,`creator`)VALUES(NEW.`id`,NEW.`gift_id`,NEW.`storage_amount`-OLD.`storage_amount`,OLD.`storage_amount`,NEW.updator);






-- 写log触发器
CREATE TRIGGER update_log AFTER UPDATE ON loyalty_mall_commodity_storage FOR EACH ROW
BEGIN
INSERT INTO log_loyalty_mall_commodity_storage(storage_id,accumultate_amount,old_available_amount,new_available_amount,safe_storage,last_update_by_orderid,last_update_event,creator)
VALUE(NEW.ID,NEW.accumultate_amount,OLD.current_available_amount,NEW.current_available_amount,NEW.safe_storage,NEW.last_update_by_orderid,NEW.last_update_event,NEW.creator);
END;
CREATE TRIGGER insert_log AFTER INSERT ON loyalty_mall_commodity_storage FOR EACH ROW
BEGIN
INSERT INTO log_loyalty_mall_commodity_storage(storage_id,accumultate_amount,old_available_amount,new_available_amount,safe_storage,last_update_by_orderid,last_update_event,creator)
VALUE(NEW.ID,NEW.accumultate_amount,NEW.current_available_amount,NEW.current_available_amount,NEW.safe_storage,NEW.last_update_by_orderid,NEW.last_update_event,NEW.creator);
END;
-- 新增数量少于0报错触发器

