create table rule_group (
  id bigint(20) not null auto_increment,
	group_name varchar(512) default null comment '分组名字',
	group_code varchar(128) not null comment '分组码',
	remark varchar(500) default null comment '备注',
	created_by_user varchar(32) not null,
  updated_by_user varchar(32) not null,
  create_time datetime default current_timestamp,
  update_time datetime default current_timestamp ON UPDATE CURRENT_TIMESTAMP,
	primary key (id),
	unique key uk_group_code (group_code) using btree,
	key group_code (group_code) using btree
);

DROP TABLE IF EXISTS rule_group_detail;
CREATE TABLE rule_group_detail (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
	rule_group_id BIGINT(20) NOT NULL,
	rule_id BIGINT(20) NOT NULL,
	created_by_user VARCHAR(32) NOT NULL,
  updated_by_user VARCHAR(32) NOT NULL,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	<PERSON>IMARY KEY (id),
	CONSTRAINT `fk_rule_group_id` FOREIGN KEY (`rule_group_id`) REFERENCES `rule_group` (`id`),
	UNIQUE KEY uk_group_code_rule (rule_group_id,rule_id) USING BTREE,
	KEY rule_group_rule_id (rule_id) USING BTREE,
	KEY group_code (rule_group_id) USING BTREE,
	KEY group_code_rule (rule_group_id, rule_id) USING BTREE
);


alter table rule add column platform	VARCHAR(32) DEFAULT null comment '规则刷新平台,cmp,loyalty平台,null表示全平台可用';
alter table rule_element add column platform	VARCHAR(32) DEFAULT null  comment '规则刷新平台,cmp,loyalty平台,null表示全平台可用';

--xue
alter table campaign add column `campaign_senario` varchar(50) DEFAULT NULL;

alter table campaign_gift_relationship add expire_type varchar(64);
ALTER TABLE campaign_gift_relationship ADD expire_date DATETIME;
alter table campaign_gift_relationship add expire_in_hour int(20);
ALTER TABLE campaign_gift_relationship ADD currency_book_id BIGINT(20);
ALTER TABLE campaign_gift ADD COLUMN rmb_rating DOUBLE;

--howard
ALTER TABLE `campaign_gift`
ADD COLUMN `logistics_supllier_id` bigint(20) COMMENT '供应商id';

ALTER TABLE `campaign`
ADD COLUMN `is_editable` tinyint(1) DEFAULT 1 COMMENT '是否在页面可编辑';



INSERT INTO `campaign_dimension`(`ID`, `DIMENSION`, `DISPLAY_NAME`, `CREATOR`, `UPDATOR`, `CREATE_TIME`, `UPDATE_TIME`) VALUES (7, 'loyalty_v2_composite', 'loyalty', 'admin', 'admin', '2018-10-29 15:38:39', '2018-10-29 15:38:43');
--以下操作会造成锁表，必须晚上进行, jane
alter table campaign_history_bxgx add column channel_id bigint(20), add column trigger_time datetime not null default now(), drop column gift_id_bk,  drop column exchange_id;
update campaign_history_bxgx set trigger_time = create_time where id > 0;

alter table campaign_history_product add
(ship_to_code varchar(20), agency varchar(20), custom_region varchar(20), tracking_region varchar(20), is_gift int(2), is_tmall_order int(2));


