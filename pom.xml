<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>loyalty.share.library</groupId>
    <artifactId>loyalty-share-library</artifactId>
    <version>${revision}</version>

    <scm>
        <connection>
            scm:git:https://e.coding.net/opworks/loyalty/loyalty-share-library.git
        </connection>
        <url>https://opworks.coding.net/p/loyalty/d/loyalty-share-library/git</url>
        <developerConnection>
            scm:git:https://e.coding.net/opworks/loyalty/loyalty-share-library.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>


    <properties>
        <revision>1.123-SNAPSHOT</revision> <!-- 默认本地开发用SNAPSHOT -->
        <public.data.model.version>3.0.58</public.data.model.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.redis.version>2.3.0.RELEASE</spring.redis.version>
        <maven.javadoc.skip>true</maven.javadoc.skip>
    </properties>

    <dependencies>

        <dependency>
            <groupId>public.data.model</groupId>
            <artifactId>data-model</artifactId>
            <version>${public.data.model.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>${spring.redis.version}</version>
        </dependency>
        <dependency>
            <groupId>public.data.model</groupId>
            <artifactId>generic-tool</artifactId>
            <version>${public.data.model.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.14.0</version>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>release</id>
            <distributionManagement>
                <repository>
                    <!--必须与 settings.xml 的 id 一致-->
                    <id>opworks-gongyongziyuan-release</id>
                    <name>gyzyrelease</name>
                    <url>http://nexus.icyanstone.com/repository/maven-releases/</url>
                </repository>
            </distributionManagement>
        </profile>
        <profile>
            <id>snapshot</id>
            <distributionManagement>
                <repository>
                    <!--必须与 settings.xml 的 id 一致-->
                    <id>opworks-gongyongziyuan-snapshot</id>
                    <name>snapshot</name>
                    <url>http://nexus.icyanstone.com/repository/maven-snapshots/</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>


    <build>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <username>${repo_user}</username>
                    <password>${repo_password}</password>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-scm-plugin</artifactId>
                <version>1.9.5</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.4</version>
                <configuration>
                    <!--指定编码为UTF-8-->
                    <encoding>UTF-8</encoding>
                    <aggregate>false</aggregate>
                    <charset>UTF-8</charset>
                    <docencoding>UTF-8</docencoding>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>